<?php

use App\Jobs\ArticlePublishedJob;
use App\Jobs\CheckQabzinoServiceHealthUptimeJob;
use App\Jobs\CheckQbzinoServiceHealthJob;
use App\Jobs\GenerateDailyQabzinoReportJob;
use App\Jobs\GenerateSitemapJob;
use App\Jobs\HostHttpCheckJob;
use App\Jobs\RunMyRouteJob;
use App\Jobs\TransferUserIranisoftToShebacartJob;
use App\Jobs\UpdateTransactionQabzinoPhoneJob;
use App\Jobs\ZarinpalRefundTransactionJob;
use Illuminate\Support\Facades\Schedule;

// Schedule::job(new ZarinpalRefundTransactionJob)->everyFiveMinutes();
Schedule::job(new UpdateTransactionQabzinoPhoneJob)->daily();
Schedule::job(new GenerateDailyQabzinoReportJob)->daily();
// Schedule::job(new GenerateDailyQabzinoReportJob)->hourly();
Schedule::job(new ArticlePublishedJob)->hourly();
// Schedule::job(new HostHttpCheckJob)->everyFifteenMinutes();
// Schedule::job(new RunMyRouteJob)->everyFiveMinutes();
Schedule::job(new CheckQbzinoServiceHealthJob)->hourly();
Schedule::job(new CheckQabzinoServiceHealthUptimeJob)->hourly();
// Schedule::job(new TransferUserIranisoftToShebacartJob)->hourly();

// Generate sitemap files every hour
Schedule::job(new GenerateSitemapJob)->hourly();
