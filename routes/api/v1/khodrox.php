<?php

use App\Models\Setting;
use App\Models\TransactionUserQabzino;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Route;
use Symfony\Component\HttpFoundation\Response;

Route::group(['namespace' => 'Api\\v1', 'prefix' => 'v1'], function () {

    Route::post('/payment/qabzino/verfication', function (Request $request) {

        $qabzinoToken = env('QABZINO_TOKEN');

        $dataRequest = [
            'Identity' => [
                'Token' => $qabzinoToken,
            ],
            'Parameters' => [
                'PaymentKey' => (string) $request->key,
            ],
        ];

        $response = Http::post('https://core.inquiry.ayantech.ir/webservices/core.svc/BillPaymentInfoCallBackResult', $dataRequest);
        $responseData = $response->json();
        $message = null;

        if (isset($responseData['Parameters']['Bills'])) {

            $bill = $responseData['Parameters']['Bills'][0];

            $message = 'گاهی اوقات ممکن است اطلاعات پرداختی با تأخیر در سامانه سراسری به‌ روزرسانی شود. لطفاً کمی صبر کنید و پس از چند ساعت مجدداً وضعیت خلافی خود را در سامانه بررسی کنید.';

            TransactionUserQabzino::create([
                'phone' => null,
                'payment_key' => (string) $request->key,
                'amount' => $bill['Amount'] ?? null,
                'bill_id' => $bill['BillID'] ?? null,
                'payment_id' => $bill['PaymentID'] ?? null,
                'paid' => $bill['Paid'] ?? null,
                'description' => $bill['Description'] ?? null,
                'status' => $bill['Paid'] ?? false,
                'trace_number' => $bill['TraceNumber'] ?? null,
                'transaction_date_time' => $bill['TransactionDateTime'] ?? null,
                'result' => $responseData,
            ]);
            // }

        } else {
            TransactionUserQabzino::create([
                'phone' => null,
                'payment_key' => (string) $request->key,
                'amount' => null,
                'bill_id' => null,
                'payment_id' => null,
                'paid' => null,
                'description' => null,
                'status' => false,
                'trace_number' => null,
                'transaction_date_time' => null,
                'result' => $responseData,
            ]);
        }

        //     if (isset($responseData['result']['Parameters']['Bills'][0]['Description']) &&
        // $responseData['result']['Parameters']['Bills'][0]['Description'] === 'موفق') {
        //         $responseData['result']['Parameters']['Bills'][0]['Description'] = $message;
        //     }

        $responseData['Parameters']['Bills'][0]['Description'] = $message;

        return $response = [
            'success' => true,
            'data' => $responseData,
            'status' => Response::HTTP_OK,
        ];

    });

    Route::get('services', function () {
        $services = Setting::pluck('value', 'key')->toArray();

        return response()->json([
            'success' => true,
            'data' => [
                'services' => $services,
            ],
            'status' => Response::HTTP_OK,
        ], Response::HTTP_OK);
    });

    Route::post('/notification/client-token', 'PushNotificationController@storeClientToken');

});
