<?php

use App\Models\UtmConvertion;
use Carbon\Carbon;
use Illuminate\Support\Facades\Route;

Route::group(['namespace' => 'Api\\v1', 'prefix' => 'v1'], function () {

    Route::get('/online-users', function () {
        $twoMinutesAgo = Carbon::now()->subMinutes(3);

        $count = UtmConvertion::where('created_at', '>=', $twoMinutesAgo)
            ->distinct('u_trace')
            ->count('u_trace');

        return response()->json(['count' => $count]);
    });

    Route::group(['prefix' => '/user'], function () {

        Route::get('/', 'UserController@index');

        Route::post('/charge-wallet', 'WalletController@index');

    });

});
