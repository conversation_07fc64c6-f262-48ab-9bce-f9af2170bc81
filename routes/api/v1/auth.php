<?php

use Illuminate\Support\Facades\Route;

Route::group(['namespace' => 'Api\\v1', 'prefix' => 'v1'], function () {

    // Route::get('/sitemap/{type}', 'SitemapController@index');

    Route::post('/auth', 'AuthController@auth');

    Route::post('/verification', 'AuthController@verification');

    Route::post('/logout', 'AuthController@logout');

    Route::post('/auth/deposit/transfer', 'DepositController@transferUserDeposit');

    Route::post('/utm-conversion', 'UtmController@index');

});

// // XML Sitemap routes (outside v1 prefix for direct access)
// Route::group(['namespace' => 'Api\\v1'], function () {

//     // Main sitemap.xml
//     Route::get('/sitemap.xml', 'SitemapController@generateMainSitemap');

//     // Individual sitemaps
//     Route::get('/sitemaps/articles.xml', 'SitemapController@generateArticlesSitemap');
//     Route::get('/sitemaps/services.xml', 'SitemapController@generateServicesSitemap');

//     // Generate all sitemaps
//     Route::get('/generate-sitemaps', 'SitemapController@generateAllSitemaps');

// });
