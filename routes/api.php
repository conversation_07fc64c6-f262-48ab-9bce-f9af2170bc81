<?php

use App\Http\Controllers\Api\v1\BaleWebhookController;
use App\Http\Resources\Sitemap;
use App\Models\Article;
use App\Models\UtmConvertion;
use Carbon\Carbon;
use Illuminate\Support\Facades\Route;


Route::post('/bale/webhook', [BaleWebhookController::class, 'handle']);

require __DIR__ . '/api/v1/auth.php';

require __DIR__ . '/api/v1/user.php';

require __DIR__ . '/api/v1/inquiry.php';

require __DIR__ . '/api/v1/payment.php';

require __DIR__ . '/api/v1/khodrox.php';

require __DIR__ . '/api/v1/article.php';
