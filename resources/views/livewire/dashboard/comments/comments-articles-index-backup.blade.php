<div class="relative min-h-96 rounded-xl">
    <!-- Loading Overlay -->
    <div
        class="absolute inset-0 z-50 flex items-center justify-center rounded-xl bg-white bg-opacity-75"
        wire:loading
    >
        <div class="flex flex-col items-center">
            <svg
                class="mb-2 h-8 w-8 animate-spin text-blue-600"
                fill="none"
                viewBox="0 0 24 24"
            >
                <circle
                    class="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    stroke-width="4"
                ></circle>
                <path
                    class="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
            </svg>
            <span class="text-sm text-gray-600">در حال پردازش...</span>
        </div>
    </div>

    <!-- Flash Messages -->
    @if (session()->has('message'))
        <div
            class="mb-4 rounded-lg border border-green-400 bg-green-100 px-4 py-3 text-green-700"
            role="alert"
        >
            <span class="block sm:inline">{{ session('message') }}</span>
        </div>
    @endif
    <!-- Stats Cards -->
    <div class="mb-6 grid grid-cols-1 gap-4 md:grid-cols-4">
        <div class="rounded-lg bg-white p-6 shadow">
            <div class="flex items-center">

                <div class="mr-4">
                    <div class="text-2xl font-bold text-gray-900">{{ $stats['total'] }}</div>
                    <div class="text-sm text-gray-500">کل نظرات</div>
                </div>
            </div>
        </div>

        <div class="rounded-lg bg-white p-6 shadow">
            <div class="flex items-center">

                <div class="mr-4">
                    <div class="text-2xl font-bold text-gray-900">{{ $stats['pending'] }}</div>
                    <div class="text-sm text-gray-500">در انتظار تایید</div>
                </div>
            </div>
        </div>

        <div class="rounded-lg bg-white p-6 shadow">
            <div class="flex items-center">

                <div class="mr-4">
                    <div class="text-2xl font-bold text-gray-900">{{ $stats['confirmed'] }}</div>
                    <div class="text-sm text-gray-500">تایید شده</div>
                </div>
            </div>
        </div>

        <div class="rounded-lg bg-white p-6 shadow">
            <div class="flex items-center">

                <div class="mr-4">
                    <div class="text-2xl font-bold text-gray-900">{{ $stats['rejected'] }}</div>
                    <div class="text-sm text-gray-500">رد شده</div>
                </div>
            </div>
        </div>
    </div>
    <!-- Header -->
    <div class="overflow-hidden rounded-t-xl">
        <div class="rounded-t-xl bg-gray-800 px-6 py-4">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-base font-bold text-white">مدیریت نظرات مقالات</h1>

                </div>
                <div class="flex items-center gap-4">
                    <!-- Search and Filter -->
                    <div class="flex items-center gap-3">
                        <div class="relative">
                            <input
                                class="w-64 rounded-lg border border-gray-300 px-4 py-2 pr-10 text-sm focus:border-transparent focus:ring-2 focus:ring-blue-500"
                                type="text"
                                wire:model.live.debounce.300ms="search"
                                placeholder="جستجو در نظرات..."
                            >
                            <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                <svg
                                    class="h-4 w-4 text-gray-400"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                                    ></path>
                                </svg>
                            </div>
                        </div>

                        <select
                            class="rounded-lg border border-gray-300 px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500"
                            wire:model.live="statusFilter"
                        >
                            @foreach ($statusOptions as $value => $label)
                                <option value="{{ $value }}">{{ $label }}</option>
                            @endforeach
                        </select>

                        <!-- Export Button -->
                        <button
                            class="inline-flex items-center rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                            wire:click="exportComments"
                        >
                            <svg
                                class="ml-2 h-4 w-4"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                    d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                ></path>
                            </svg>
                            خروجی Excel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Comments Table -->
    <div class="rounded-b-xl bg-white shadow-lg">
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="border-b border-gray-200 bg-gray-50">
                    <tr>
                        <th class="border border-gray-200 px-2 py-2 text-center">
                            نویسنده</th>
                        <th class="border border-gray-200 px-2 text-center">
                            مقاله</th>
                        <th class="border border-gray-200 px-2 text-center">متن
                            نظر</th>
                        <th class="border border-gray-200 px-2 text-center">
                            امتیاز</th>
                        <th class="border border-gray-200 px-2 text-center">
                            وضعیت</th>
                        <th class="border border-gray-200 px-2 text-center">
                            تاریخ</th>
                        <th class="border border-gray-200 px-2 text-center">
                            پاسخ‌ها</th>
                        <th class="border border-gray-200 px-2 text-center">
                            عملیات</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 bg-white">
                    @forelse($comments as $comment)
                        <tr class="transition-colors hover:bg-gray-50">
                            <!-- نویسنده -->
                            <td class="whitespace-nowrap px-6 py-4">
                                <div class="flex items-center">
                                    <div class="h-10 w-10 flex-shrink-0">
                                        <div
                                            class="flex h-10 w-10 items-center justify-center rounded-full bg-gray-300">
                                            <span class="text-sm font-medium text-gray-700">
                                                {{ substr($comment->user->fullname ?? 'ناشناس', 0, 2) }}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="mr-4">
                                        <div class="text-sm font-medium text-gray-900">
                                            {{ $comment->user->fullname ?? 'کاربر ناشناس' }}
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            {{ $comment->user->phone ?? '-' }}
                                        </div>
                                    </div>
                                </div>
                            </td>

                            <!-- مقاله -->
                            <td class="px-6 py-4">
                                <div class="max-w-xs truncate text-sm text-gray-900">
                                    {{ $comment->commentable->title ?? 'مقاله حذف شده' }}
                                </div>

                            </td>

                            <!-- متن نظر -->
                            <td class="px-6 py-4">
                                <div class="max-w-md text-sm text-gray-900">
                                    <p class="line-clamp-3">{{ $comment->body }}</p>
                                </div>
                            </td>

                            <!-- امتیاز -->
                            <td class="whitespace-nowrap px-6 py-4">
                                @if ($comment->rate)
                                    <div class="flex items-center">
                                        @for ($i = 1; $i <= 5; $i++)
                                            <svg
                                                class="{{ $i <= $comment->rate ? 'text-yellow-400' : 'text-gray-300' }} h-4 w-4"
                                                fill="currentColor"
                                                viewBox="0 0 20 20"
                                            >
                                                <path
                                                    d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                                                />
                                            </svg>
                                        @endfor
                                        <span class="mr-2 text-sm text-gray-600">({{ $comment->rate }})</span>
                                    </div>
                                @else
                                    <span class="text-sm text-gray-400">بدون امتیاز</span>
                                @endif
                            </td>

                            <!-- وضعیت -->
                            <td class="whitespace-nowrap px-6 py-4">
                                @switch($comment->status->value)
                                    @case(0)
                                        <span
                                            class="inline-flex rounded-full bg-red-100 px-2 py-1 text-xs font-semibold text-red-800"
                                        >
                                            رد شده
                                        </span>
                                    @break

                                    @case(1)
                                        <span
                                            class="inline-flex rounded-full bg-yellow-100 px-2 py-1 text-xs font-semibold text-yellow-800"
                                        >
                                            در انتظار تایید
                                        </span>
                                    @break

                                    @case(2)
                                        <span
                                            class="inline-flex rounded-full bg-green-100 px-2 py-1 text-xs font-semibold text-green-800"
                                        >
                                            تایید شده
                                        </span>
                                    @break
                                @endswitch
                            </td>

                            <!-- تاریخ -->
                            <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                                {{ $comment->shamsi_date }}
                            </td>

                            <!-- پاسخ‌ها -->
                            <td class="whitespace-nowrap px-6 py-4">
                                @if ($comment->replies->count() > 0)
                                    <span
                                        class="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800"
                                    >
                                        {{ $comment->replies->count() }} پاسخ
                                    </span>
                                @else
                                    <span class="text-sm text-gray-400">بدون پاسخ</span>
                                @endif
                            </td>

                            <!-- عملیات -->
                            <td class="whitespace-nowrap px-6 py-4 text-sm font-medium">
                                <div class="flex flex-wrap items-center gap-2">
                                    <!-- تغییر وضعیت -->
                                    <select
                                        class="rounded border border-gray-300 px-2 py-1 text-xs focus:ring-2 focus:ring-blue-500"
                                        wire:change="changeCommentStatus('{{ $comment->id }}', $event.target.value)"
                                        wire:loading.attr="disabled"
                                    >
                                        <option
                                            value="{{ $comment->status->value }}"
                                            selected
                                        >
                                            @switch($comment->status->value)
                                                @case(0)
                                                    رد شده
                                                @break

                                                @case(1)
                                                    در انتظار
                                                @break

                                                @case(2)
                                                    تایید شده
                                                @break
                                            @endswitch
                                        </option>
                                        @if ($comment->status->value !== 1)
                                            <option value="1">در انتظار تایید</option>
                                        @endif
                                        @if ($comment->status->value !== 2)
                                            <option value="2">تایید</option>
                                        @endif
                                        @if ($comment->status->value !== 0)
                                            <option value="0">رد</option>
                                        @endif
                                    </select>

                                    <!-- مشاهده جزئیات -->
                                    <button
                                        class="inline-flex items-center rounded-md border border-blue-300 bg-blue-50 px-3 py-1 text-xs font-medium text-blue-700 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                                        x-on:click="$dispatch('openModal',{component:'dashboard.comments.view-comment-modal',arguments:{ commentId:'{{ $comment->id }}'}})"
                                    >
                                        <svg
                                            class="ml-1 h-3 w-3"
                                            fill="none"
                                            stroke="currentColor"
                                            viewBox="0 0 24 24"
                                        >
                                            <path
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                                stroke-width="2"
                                                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                                            ></path>
                                            <path
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                                stroke-width="2"
                                                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                                            ></path>
                                        </svg>
                                        مشاهده
                                    </button>

                                    <!-- تایید -->
                                    @if ($comment->status->value === 1)
                                        <button
                                            class="inline-flex items-center rounded-md border border-transparent bg-green-600 px-3 py-1 text-xs font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                                            x-on:click="$dispatch('openModal',{component:'dashboard.comments.confirm-action-modal',arguments:{ commentId:'{{ $comment->id }}', action:'approve'}})"
                                        >
                                            <svg
                                                class="ml-1 h-3 w-3"
                                                fill="none"
                                                stroke="currentColor"
                                                viewBox="0 0 24 24"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    stroke-width="2"
                                                    d="M5 13l4 4L19 7"
                                                ></path>
                                            </svg>
                                            تایید
                                        </button>

                                        <!-- رد -->
                                        <button
                                            class="inline-flex items-center rounded-md border border-transparent bg-yellow-600 px-3 py-1 text-xs font-medium text-white hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2"
                                            x-on:click="$dispatch('openModal',{component:'dashboard.comments.confirm-action-modal',arguments:{ commentId:'{{ $comment->id }}', action:'reject'}})"
                                        >
                                            <svg
                                                class="ml-1 h-3 w-3"
                                                fill="none"
                                                stroke="currentColor"
                                                viewBox="0 0 24 24"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    stroke-width="2"
                                                    d="M6 18L18 6M6 6l12 12"
                                                ></path>
                                            </svg>
                                            رد
                                        </button>
                                    @endif

                                    <!-- حذف -->
                                    <button
                                        class="inline-flex items-center rounded-md border border-transparent bg-red-600 px-3 py-1 text-xs font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                                        x-on:click="$dispatch('openModal',{component:'dashboard.comments.confirm-action-modal',arguments:{ commentId:'{{ $comment->id }}', action:'delete'}})"
                                    >
                                        <svg
                                            class="ml-1 h-3 w-3"
                                            fill="none"
                                            stroke="currentColor"
                                            viewBox="0 0 24 24"
                                        >
                                            <path
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                                stroke-width="2"
                                                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                                            ></path>
                                        </svg>
                                        حذف
                                    </button>
                                </div>
                            </td>
                        </tr>

                        <!-- نمایش پاسخ‌ها -->
                        @if ($comment->replies->count() > 0)
                            @foreach ($comment->replies as $reply)
                                <tr class="bg-gray-50">
                                    <td
                                        class="px-6 py-3"
                                        colspan="8"
                                    >
                                        <div class="mr-8 border-r-2 border-blue-200 pr-4">
                                            <div class="flex items-start gap-3">
                                                <div class="flex-shrink-0">
                                                    <div
                                                        class="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100">
                                                        <svg
                                                            class="h-4 w-4 text-blue-600"
                                                            fill="currentColor"
                                                            viewBox="0 0 20 20"
                                                        >
                                                            <path d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
                                                        </svg>
                                                    </div>
                                                </div>
                                                <div class="flex-1">
                                                    <div class="mb-1 flex items-center gap-2">
                                                        <span class="text-sm font-medium text-gray-900">
                                                            {{ $reply->user->fullname ?? 'مدیر سایت' }}
                                                        </span>
                                                        <span
                                                            class="text-xs text-gray-500">{{ $reply->shamsi_date }}</span>
                                                        @if ($reply->user_id === auth()->id())
                                                            <span
                                                                class="inline-flex rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800"
                                                            >
                                                                شما
                                                            </span>
                                                        @endif
                                                    </div>
                                                    <p class="text-sm text-gray-700">{{ $reply->body }}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        @endif
                        @empty
                            <tr>
                                <td
                                    class="px-6 py-12 text-center"
                                    colspan="8"
                                >
                                    <div class="flex flex-col items-center">
                                        <svg
                                            class="mb-4 h-12 w-12 text-gray-400"
                                            fill="none"
                                            stroke="currentColor"
                                            viewBox="0 0 24 24"
                                        >
                                            <path
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                                stroke-width="2"
                                                d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                                            ></path>
                                        </svg>
                                        <h3 class="mb-2 text-lg font-medium text-gray-900">هیچ نظری یافت نشد</h3>
                                        <p class="text-gray-500">در حال حاضر هیچ نظری برای نمایش وجود ندارد.</p>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="border-t border-gray-200 px-6 py-4">
                {{ $comments->links() }}
            </div>
        </div>
    </div>

    <!-- مودال‌ها حالا از wire-elements/modal استفاده می‌کنند -->
</div>
    <svg
        class="h-6 w-6 text-blue-600"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
    >
        <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
        ></path>
        <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
        ></path>
    </svg>
    </div>
    <div class="mt-3 w-full text-center sm:mr-4 sm:mt-0 sm:text-right">
        <h3
            class="text-lg font-medium leading-6 text-gray-900"
            id="modal-title"
        >
            جزئیات کامنت
        </h3>
        <div class="mt-4">
            @if ($selectedComment)
                <div class="mb-6 rounded-lg bg-gray-50 p-6">
                    <div class="mb-4 grid grid-cols-1 gap-4 md:grid-cols-2">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">نام
                                کاربر:</label>
                            <p class="text-sm text-gray-900">
                                {{ $selectedComment->user->fullname ?? 'کاربر ناشناس' }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">تاریخ
                                ثبت:</label>
                            <p class="text-sm text-gray-900">{{ $selectedComment->shamsi_date }}
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">عنوان
                                مقاله:</label>
                            <p class="text-sm text-gray-900">
                                {{ $selectedComment->commentable->title ?? 'مقاله حذف شده' }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">وضعیت:</label>
                            @switch($selectedComment->status->value)
                                @case(0)
                                    <span
                                        class="inline-flex rounded-full bg-red-100 px-2 py-1 text-xs font-semibold text-red-800">رد
                                        شده</span>
                                @break

                                @case(1)
                                    <span
                                        class="inline-flex rounded-full bg-yellow-100 px-2 py-1 text-xs font-semibold text-yellow-800"
                                    >در انتظار تایید</span>
                                @break

                                @case(2)
                                    <span
                                        class="inline-flex rounded-full bg-green-100 px-2 py-1 text-xs font-semibold text-green-800"
                                    >تایید شده</span>
                                @break
                            @endswitch
                        </div>
                    </div>
                    <div>
                        <label class="mb-2 block text-sm font-medium text-gray-700">متن
                            نظر:</label>
                        <div class="rounded border bg-white p-3">
                            <p class="text-sm leading-relaxed text-gray-900">
                                {{ $selectedComment->body }}</p>
                        </div>
                    </div>
                </div>

                @if ($selectedComment->replies && $selectedComment->replies->count() > 0)
                    <div class="border-t pt-6">
                        <h4 class="text-md mb-4 font-medium text-gray-900">پاسخ‌ها
                            ({{ $selectedComment->replies->count() }})</h4>
                        <div class="space-y-4">
                            @foreach ($selectedComment->replies as $reply)
                                <div class="rounded-lg border-r-4 border-blue-200 bg-blue-50 p-4">
                                    <div class="mb-2 flex items-center justify-between">
                                        <span class="text-sm font-medium text-gray-900">
                                            {{ $reply->user->fullname ?? 'مدیر سایت' }}
                                        </span>
                                        <span class="text-xs text-gray-500">{{ $reply->shamsi_date }}</span>
                                    </div>
                                    <p class="text-sm leading-relaxed text-gray-700">
                                        {{ $reply->body }}</p>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif
            @else
                <div class="py-8 text-center">
                    <p class="text-gray-500">کامنت یافت نشد.</p>
                </div>
            @endif
        </div>
    </div>
    </div>
    </div>
    <div class="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
        <button
            class="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:mt-0 sm:w-auto sm:text-sm"
            type="button"
            wire:click="closeViewModal"
        >
            بستن
        </button>
    </div>
    </div>
    </div>
    </div>
    @endif

    <!-- Confirm Modal -->
    @if ($showConfirmModal)
        <div
            class="fixed inset-0 z-50 overflow-y-auto"
            role="dialog"
            aria-labelledby="modal-title"
            aria-modal="true"
        >
            <div class="flex min-h-screen items-end justify-center px-4 pb-20 pt-4 text-center sm:block sm:p-0">
                <div
                    class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
                    wire:click="closeConfirmModal"
                ></div>

                <span
                    class="hidden sm:inline-block sm:h-screen sm:align-middle"
                    aria-hidden="true"
                >&#8203;</span>

                <div
                    class="inline-block transform overflow-hidden rounded-lg bg-white text-right align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:align-middle">
                    <div class="bg-white px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div
                                class="@if ($confirmAction === 'delete') bg-red-100 @elseif($confirmAction === 'reject') bg-yellow-100 @else bg-green-100 @endif mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full sm:mx-0 sm:h-10 sm:w-10">
                                @if ($confirmAction === 'delete')
                                    <svg
                                        class="h-6 w-6 text-red-600"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                                        ></path>
                                    </svg>
                                @elseif($confirmAction === 'reject')
                                    <svg
                                        class="h-6 w-6 text-yellow-600"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M6 18L18 6M6 6l12 12"
                                        ></path>
                                    </svg>
                                @else
                                    <svg
                                        class="h-6 w-6 text-green-600"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M5 13l4 4L19 7"
                                        ></path>
                                    </svg>
                                @endif
                            </div>
                            <div class="mt-3 text-center sm:mr-4 sm:mt-0 sm:text-right">
                                <h3 class="text-lg font-medium leading-6 text-gray-900">
                                    @switch($confirmAction)
                                        @case('approve')
                                            تایید کامنت
                                        @break

                                        @case('reject')
                                            رد کامنت
                                        @break

                                        @case('delete')
                                            حذف کامنت
                                        @break

                                        @default
                                            تایید عملیات
                                        @break
                                    @endswitch
                                </h3>
                                <div class="mt-2">
                                    <p class="text-sm text-gray-500">
                                        @switch($confirmAction)
                                            @case('approve')
                                                آیا از تایید این کامنت اطمینان دارید؟
                                            @break

                                            @case('reject')
                                                آیا از رد این کامنت اطمینان دارید؟
                                            @break

                                            @case('delete')
                                                آیا از حذف این کامنت اطمینان دارید؟ این عمل قابل بازگشت نیست.
                                            @break

                                            @default
                                                آیا از انجام این عملیات اطمینان دارید؟
                                            @break
                                        @endswitch
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                        <button
                            class="@if ($confirmAction === 'delete') bg-red-600 hover:bg-red-700 focus:ring-red-500
                            @elseif($confirmAction === 'reject') bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500
                            @else bg-green-600 hover:bg-green-700 focus:ring-green-500 @endif inline-flex w-full justify-center rounded-md border border-transparent px-4 py-2 text-base font-medium text-white shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm"
                            type="button"
                            wire:click="confirmAction"
                        >
                            @switch($confirmAction)
                                @case('approve')
                                    تایید
                                @break

                                @case('reject')
                                    رد
                                @break

                                @case('delete')
                                    حذف
                                @break

                                @default
                                    تایید
                                @break
                            @endswitch
                        </button>
                        <button
                            class="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:mt-0 sm:w-auto sm:text-sm"
                            type="button"
                            wire:click="closeConfirmModal"
                        >
                            انصراف
                        </button>
                    </div>
                </div>
            </div>
        </div>
    @endif
    </div>
