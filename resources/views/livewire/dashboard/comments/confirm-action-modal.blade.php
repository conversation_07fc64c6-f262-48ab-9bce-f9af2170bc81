<div class="mx-auto w-full max-w-md rounded-lg bg-white shadow-xl">
    <div class="p-6">
        <div class="flex items-center">
            <div
                class="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-gray-100 sm:mx-0 sm:h-10 sm:w-10">
                @if ($action === 'delete')
                    <svg
                        class="h-6 w-6 text-red-600"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                    >
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                        ></path>
                    </svg>
                @elseif($action === 'reject')
                    <svg
                        class="h-6 w-6 text-yellow-600"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                    >
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M6 18L18 6M6 6l12 12"
                        ></path>
                    </svg>
                @else
                    <svg
                        class="h-6 w-6 text-green-600"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                    >
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M5 13l4 4L19 7"
                        ></path>
                    </svg>
                @endif
            </div>
            <div class="mt-3 text-center sm:mr-4 sm:mt-0 sm:text-right">
                <h3 class="text-lg font-medium leading-6 text-gray-900">
                    {{ $title }}
                </h3>
                <div class="mt-2">
                    <p class="text-sm text-gray-500">
                        {{ $message }}
                    </p>
                </div>
            </div>
        </div>

        <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
            <button
                class="inline-flex w-full justify-center rounded-md border border-red-300 bg-red-600 px-4 py-2 text-base font-medium text-white transition-colors hover:bg-gray-100 hover:text-red-600 sm:ml-3 sm:w-auto sm:text-sm"
                type="button"
                wire:click="confirm"
                wire:loading.attr="disabled"
                wire:target="confirm"
            >
                <span
                    wire:loading.remove
                    wire:target="confirm"
                >{{ $confirmText }}</span>
                <span
                    class="flex items-center"
                    wire:loading
                    wire:target="confirm"
                >
                    <svg
                        class="-ml-1 mr-2 h-4 w-4 animate-spin"
                        fill="none"
                        viewBox="0 0 24 24"
                    >
                        <circle
                            class="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            stroke-width="4"
                        ></circle>
                        <path
                            class="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                    </svg>
                    در حال پردازش...
                </span>
            </button>
            <button
                class="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 transition-colors hover:bg-gray-100 sm:mt-0 sm:w-auto sm:text-sm"
                type="button"
                wire:click="$dispatch('closeModal')"
            >
                {{ $cancelText }}
            </button>
        </div>
    </div>
