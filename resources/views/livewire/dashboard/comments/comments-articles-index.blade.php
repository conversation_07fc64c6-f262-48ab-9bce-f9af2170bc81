<div>
    <!-- Loading Overlay -->
    <div class="fixed inset-0 z-[9999] items-center justify-center bg-black bg-opacity-50" wire:loading.flex>
        <div class="rounded-lg bg-white p-6 shadow-xl">
            <div class="flex items-center space-x-3">
                <svg class="h-6 w-6 animate-spin text-blue-600" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                    </path>
                </svg>
                <span class="text-lg font-medium text-gray-900">در حال پردازش...</span>
            </div>
        </div>
    </div>

    <div class="overflow-hidden rounded-t-xl" x-data="{ fillter: false }" wire:ignore>

        <div class="rounded-t-xl bg-gray-800 px-3 py-1.5">
            <div class="flex items-center justify-between">
                <div>
                    <span class="block text-base text-white max-md:text-sm">لیست دسته بندی ها</span>
                </div>
                <div class="flex items-center gap-4" x-data="playerState()" x-init="checkState()">

                    <div x-show="isPlaying">
                        <span class="text-sm text-gray-500" x-text="countdown"></span>
                        <span class="text-sm text-gray-500">ثانیه</span>
                    </div>
                    <div class="text-sm font-bold text-gray-100" x-show="isPlaying">
                        زمان بروزرسانی </div>
                    <button class="flex items-center gap-2 text-white" @click="togglePlay">
                        <span :class="!isPlaying ? 'text-green-500' : 'text-gray-500'">
                            <svg class="size-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M15.75 5.25v13.5m-7.5-13.5v13.5" />
                            </svg>
                        </span>
                        <span :class="isPlaying ? 'text-green-500' : 'text-gray-500'">
                            <svg class="size-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z" />
                            </svg>
                        </span>
                    </button>

                    <button class="flex items-center gap-2 p-2" @click="fillter = !fillter">
                        <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none"
                            viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75" />
                        </svg>
                        <span class="text-base text-white">فیلتر</span>
                    </button>

                </div>
            </div>
        </div>
        <form class="tranfsition-all transform overflow-hidden bg-gray-800 px-6" wire:submit="fillter" x-cloak
            :class="fillter ? 'h-auto pt-4' : 'h-0'">

            <div class="my-3 grid grid-cols-1 gap-3 md:grid-cols-8">
                <div>
                    <label class="mb-2 block text-sm text-white" for="phone">شماره تماس:</label>
                    <input
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="phone" type="text" wire:model="data.phone">
                    @error('data.phone')
                    <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                class="text-sm font-bold text-red-600">{{ $message }}</span></span></div>
                    @enderror
                </div>
                <div>
                    <label class="mb-2 block text-sm text-white" for="refId">شماره تراکنش:</label>
                    <input
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="refId" type="text" wire:model="data.refId">
                    @error('data.refId')
                    <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                class="text-sm font-bold text-red-600">{{ $message }}</span></span></div>
                    @enderror
                </div>
                <div class="md:col-span-2">
                    <label class="mb-2 block text-sm text-white" for="cardNumber">شماره کارت :</label>
                    <input
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="cardNumber" type="text" wire:model="data.cardNumber">
                    @error('data.cardNumber')
                    <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                class="text-sm font-bold text-red-600">{{ $message }}</span></span></div>
                    @enderror
                </div>

                <div>
                    <label class="mb-2 block text-sm text-white" for="amount_as">از مبلغ تراکنش :</label>
                    <input
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-center text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="amount_as" type="text" wire:model="data.amount_as"
                        onkeyup="javascript:this.value=Comma(this.value);">
                    @error('data.amount_as')
                    <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                class="text-sm font-bold text-red-600">{{ $message }}</span></span></div>
                    @enderror
                </div>
                <div>
                    <!-- Comments Table -->
                    <label class="mb-2 block text-sm text-white" for="amount_to">تا مبلغ تراکنش :</label>
                    <input
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-center text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="amount_to" type="text" wire:model="data.amount_to"
                        onkeyup="javascript:this.value=Comma(this.value);">
                    @error('data.amount_to')
                    <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                class="text-sm font-bold text-red-600">{{ $message }}</span></span></div>
                    @enderror
                </div>

                <div>
                    <label class="mb-2 block text-sm text-white" for="data_start">از تاریخ:</label>
                    <input
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="data_start" data-jdp type="text" wire:model="data.data_start">
                    @error('data.data_start')
                    <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                class="text-sm font-bold text-red-600">{{ $message }}</span></span></div>
                    @enderror
                </div>
                <div>
                    <label class="mb-2 block text-sm text-white" for="date_end">تا تاریخ:</label>
                    <input
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="date_end" data-jdp type="text" wire:model="data.date_end">
                    @error('data.date_end')
                    <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                class="text-sm font-bold text-red-600">{{ $message }}</span></span></div>
                    @enderror
                </div>
            </div>

            <div class="flex flex-row-reverse items-center gap-3 py-3">

                <button
                    class="rounded-lg bg-red-500 px-6 py-1 text-white transition-all hover:bg-red-600 disabled:bg-gray-100 disabled:text-gray-400"
                    type="submit">
                    <svg class="inline h-4 w-4 animate-spin text-red-600 dark:text-red-500" role="status"
                        aria-hidden="true" wire:loading wire:target="fillter" viewBox="0 0 100 101" fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="#E5E7EB" />
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentColor" />
                    </svg>
                    <span class="text-sm">اعمال فیلتر</span>
                </button>
                <button
                    class="rounded-lg bg-gray-300 px-6 py-1 text-gray-600 transition-all hover:bg-gray-200 disabled:bg-gray-100 disabled:text-gray-400"
                    type="button" wire:click="ClearFillter">
                    <svg class="inline h-4 w-4 animate-spin text-red-600 dark:text-red-500" role="status"
                        aria-hidden="true" wire:loading wire:target="ClearFillter" viewBox="0 0 100 101" fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="#E5E7EB" />
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentColor" />
                    </svg>
                    <span class="text-sm">حذف فیلترها</span>
                </button>
            </div>
        </form>
    </div>

    <div class="min-h-96 overflow-hidden rounded-lg bg-white shadow">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>

                        <th class="border border-gray-200 p-3 text-center">
                            کاربر
                        </th>
                        <th class="border border-gray-200 p-3 text-center">
                            مقاله / سرویس
                        </th>
                        <th class="border border-gray-200 p-3 text-center">
                            متن کامنت
                        </th>
                        <th class="border border-gray-200 p-3 text-center">
                            وضعیت
                        </th>
                        <th class="border border-gray-200 p-3 text-center">
                            پاسخ‌ها
                        </th>
                        <th class="border border-gray-200 p-3 text-center">
                            امتیاز
                        </th>
                        <th class="border border-gray-200 p-3 text-center">
                            تاریخ
                        </th>
                        <th class="border border-gray-200 p-3 text-center">
                            عملیات
                        </th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 bg-white">
                    @forelse($comments as $comment)
                    <tr class="cursor-pointer border-b hover:bg-gray-50" x-data="{ showReplies: false }"
                        @click="showReplies = !showReplies">

                        <td class="border px-6 py-4">
                            <div class="text-center text-sm font-bold text-gray-900">
                                {{ $comment->user->fullname ?? 'کاربر ناشناس' }}
                            </div>
                            <div class="text-sm text-gray-500">
                                {{ $comment->user->phone ?? '' }}
                            </div>
                        </td>
                        <td class="border px-6 py-4">
                            @if ($comment->commentable)
                            @php
                            // Determine if it's a service (has page) or article (uses slug)
                            $isService = !empty($comment->commentable->page);
                            $url = $isService
                            ? 'https://khodrox.com' . $comment->commentable->page
                            : 'https://khodrox.com/blog/' . $comment->commentable->slug;
                            @endphp
                            <a class="text-sm text-blue-600 transition-colors hover:text-blue-800 hover:underline"
                                href="{{ $url }}" title="مشاهده {{ $isService ? 'سرویس' : 'مقاله' }}" target="_blank">
                                {{ Str::limit($comment->commentable->title, 50) }}
                                <svg class="ml-1 inline-block h-3 w-3" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14">
                                    </path>
                                </svg>
                            </a>
                            @else
                            <div class="text-sm text-gray-500">
                                مقاله حذف شده
                            </div>
                            @endif
                        </td>
                        <td class="border px-6 py-4">
                            <div class="text-sm text-gray-900">
                                {{ Str::limit($comment->body, 100) }}
                            </div>
                        </td>
                        <td class="border px-6 py-4">
                            <div class="text-center">
                                @switch($comment->status->value)
                                @case(0)
                                <span
                                    class="inline-flex rounded-full bg-red-100 px-2 py-1 text-xs font-semibold text-red-800">
                                    رد شده
                                </span>
                                @break

                                @case(1)
                                <span
                                    class="inline-flex rounded-full bg-yellow-100 px-2 py-1 text-xs font-semibold text-yellow-800">
                                    در انتظار تایید
                                </span>
                                @break

                                @case(2)
                                <span
                                    class="inline-flex rounded-full bg-green-100 px-2 py-1 text-xs font-semibold text-green-800">
                                    تایید شده
                                </span>
                                @break
                                @endswitch
                            </div>
                        </td>
                        <td class="border px-6 py-4 text-center text-sm">
                            @if ($comment->replies && $comment->replies->count() > 0)
                            <span
                                class="inline-flex items-center rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800">
                                {{ $comment->replies->count() }} پاسخ
                            </span>
                            @else
                            <span class="text-xs text-gray-400">بدون پاسخ</span>
                            @endif
                        </td>
                        <td class="border px-6 py-4 text-center text-sm">
                            @if ($comment->rate)
                            <div class="flex items-center justify-center gap-1">
                                @for ($i = 1; $i <= 5; $i++) <svg
                                    class="{{ $i <= $comment->rate ? 'text-yellow-400' : 'text-gray-300' }} h-4 w-4"
                                    fill="currentColor" viewBox="0 0 20 20">
                                    <path
                                        d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                    </svg>
                                    @endfor
                                    <span class="mr-1 text-xs text-gray-600">({{ $comment->rate }})</span>
                            </div>
                            @else
                            <span class="text-xs text-gray-400">بدون امتیاز</span>
                            @endif
                        </td>
                        <td class="border px-6 py-4 text-center text-sm">
                            {{ $comment->shamsi_date }}
                        </td>
                        <td class="border px-6 py-4">
                            <div class="flex items-center justify-center gap-2">

                                {{-- <button
                                    class="inline-flex items-center rounded-md border border-gray-300 bg-white px-3 py-1 text-xs font-medium text-gray-700 transition-colors hover:bg-gray-100"
                                    x-on:click="$dispatch('openModal',{component:'dashboard.comments.view-comment-modal',arguments:{ commentId:'{{ $comment->id }}'}})">
                                    مشاهده
                                </button> --}}
                                <button class="rounded-xl bg-gray-100 px-4 py-1.5" type="button"
                                    x-on:click="$dispatch('openModal',{component:'dashboard.comments.view-comment-modal',arguments:{ commentId:'{{ $comment->id }}'}})">
                                    <span class="text-base text-gray-700">...</span>
                                </button>

                                {{-- @if ($comment->status->value !== 2)

                                <button
                                    class="inline-flex items-center rounded-md border border-gray-300 bg-white px-3 py-1 text-xs font-medium text-gray-700 transition-colors hover:bg-gray-100"
                                    x-on:click="$dispatch('openModal',{component:'dashboard.comments.confirm-action-modal',arguments:{ commentId:'{{ $comment->id }}', action:'approve'}})">
                                    تایید
                                </button>
                                @endif

                                @if ($comment->status->value !== 0)

                                <button
                                    class="inline-flex items-center rounded-md border border-gray-300 bg-white px-3 py-1 text-xs font-medium text-gray-700 transition-colors hover:bg-gray-100"
                                    x-on:click="$dispatch('openModal',{component:'dashboard.comments.confirm-action-modal',arguments:{ commentId:'{{ $comment->id }}', action:'reject'}})">
                                    رد
                                </button>
                                @endif --}}

                                {{-- <button
                                    class="inline-flex items-center rounded-md border border-red-300 bg-red-600 px-3 py-1 text-xs font-medium text-white transition-colors hover:bg-gray-100 hover:text-red-600"
                                    x-on:click="$dispatch('openModal',{component:'dashboard.comments.confirm-action-modal',arguments:{ commentId:'{{ $comment->id }}', action:'delete'}})">
                                    حذف
                                </button> --}}
                            </div>
                        </td>
                    </tr>

                    @if ($comment->replies && $comment->replies->count() > 0)
                    <tr x-show="showReplies" x-transition>
                        <td class="border-0 px-6 py-2" colspan="9">
                            <div class="rounded-lg border-r-4 border-green-200 bg-green-50 p-4">
                                <h4 class="mb-3 text-sm font-medium text-green-800">
                                    پاسخ‌ها ({{ $comment->replies->count() }})
                                </h4>
                                <div class="space-y-3">
                                    @foreach ($comment->replies as $reply)
                                    <div class="rounded-md border border-green-100 bg-white p-3">
                                        <div class="mb-2 flex items-center justify-between">
                                            <div class="flex items-center gap-2">
                                                <span class="text-sm font-medium text-gray-900">
                                                    {{ $reply->user->fullname ?? 'مدیر سایت' }}
                                                </span>
                                                @if ($reply->user_id === auth()->id())
                                                <span
                                                    class="inline-flex rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800">
                                                    شما
                                                </span>
                                                @endif
                                            </div>
                                            <span class="text-xs text-gray-500">{{ $reply->shamsi_date }}</span>
                                        </div>
                                        <p class="text-sm leading-relaxed text-gray-700">
                                            {{ $reply->body }}</p>
                                    </div>
                                    @endforeach
                                </div>
                            </div>
                        </td>
                    </tr>
                    @endif

                    @empty
                    <tr>
                        <td class="px-6 py-4 text-center text-gray-500" colspan="9">
                            کامنتی یافت نشد.
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <div class="bg-white px-4 py-3">
            {{ $comments->links() }}
        </div>
    </div>

</div>
