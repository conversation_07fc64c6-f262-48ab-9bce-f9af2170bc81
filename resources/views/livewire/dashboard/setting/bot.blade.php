<div class="mb-6">
    <p class="mb-3 text-sm font-bold text-gray-700">تتظیمات بات بله - این تنظیمات در حال حاضر برای بات بله کار میکند در
        صورتی که بات های دیگری اضافه شود در این بخش اطلاع رسانی و دسترسی ها قرار خواهد گرفت</p>
    <form
        class="flex w-full flex-col gap-2 py-3"
        wire:submit="update"
    >
        <div class="md:col-span-2">
            <label
                class="mb-1 block text-sm font-bold text-gray-700 dark:font-normal dark:text-gray-100"
                for="title"
            >عنوان بات</label>
            <div class="relative">
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-right text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:border-gray-900 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="title"
                    type="text"
                    wire:model="data.title"
                >
            </div>
        </div>
        <div class="md:col-span-2">
            <label
                class="mb-1 block text-sm font-bold text-gray-700 dark:font-normal dark:text-gray-100"
                for="description"
            >توضیحات بات</label>
            <div class="relative">
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-right text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:border-gray-900 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="description"
                    type="text"
                    wire:model="data.description"
                >
            </div>
        </div>
        <div class="md:col-span-2">
            <label
                class="mb-1 block text-sm font-bold text-gray-700 dark:font-normal dark:text-gray-100"
                for="botKey"
            >کلید اتصال</label>
            <div class="relative">
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:border-gray-900 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="botKey"
                    type="text"
                    dir="ltr"
                    wire:model="data.botKey"
                >
            </div>
        </div>
        <div class="md:col-span-2">
            <label
                class="mb-1 block text-sm font-bold text-gray-700 dark:font-normal dark:text-gray-100"
                for="usernameChanel"
            >نام کاربری یا آیدی کانالی که پیام به اون ارسال شود</label>
            <div class="relative">
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:border-gray-900 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="usernameChanel"
                    type="text"
                    dir="ltr"
                    wire:model="data.usernameChanel"
                >
            </div>
        </div>
        <div class="flex flex-row-reverse items-center">
            <button
                class="my-4 rounded-lg bg-red-500 px-3 py-2 text-white transition-all hover:bg-red-600 disabled:bg-gray-100 disabled:text-gray-400"
                type="submit"
                wire:target="changeBalanceWallet"
                wire:loading.attr="disabled"
            >
                <span class="flex items-center justify-center gap-2">
                    <svg
                        class="inline h-4 w-4 animate-spin text-red-600 dark:text-red-500"
                        role="status"
                        aria-hidden="true"
                        wire:loading
                        wire:target="update"
                        viewBox="0 0 100 101"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="#E5E7EB"
                        />
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentColor"
                        />
                    </svg>
                    <span class="text-sm">بروزرسانی اطلاعات</span>
                </span>
            </button>
        </div>
    </form>
    <div
        class="mt-6"
        wire:ignore
    >
        <p class="mb-6 text-sm font-bold text-gray-700">تنظیمات ارسال پیام</p>
        <div class="flex flex-col gap-4">
            <label class="flex cursor-pointer items-center">
                <input
                    class="peer sr-only"
                    type="checkbox"
                    wire:click="toggleService('reportDailyBot')"
                    {{ $settings['reportDailyBot'] ?? false ? 'checked' : '' }}
                >
                <div
                    class="peer relative h-6 w-11 rounded-full bg-gray-200 after:absolute after:start-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-red-500 peer-checked:after:-translate-x-full peer-checked:after:border-white peer-focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:peer-checked:bg-red-500 dark:peer-focus:ring-red-600 rtl:peer-checked:after:-translate-x-full">
                </div>
                <span class="ms-3 text-sm font-medium text-gray-900 dark:text-gray-300">
                    ارسال نوتیف گزارش مالی روزانه
                </span>
            </label>
            <label class="flex cursor-pointer items-center">
                <input
                    class="peer sr-only"
                    type="checkbox"
                    wire:click="toggleService('reportDailySms')"
                    {{ $settings['reportDailySms'] ?? false ? 'checked' : '' }}
                >
                <div
                    class="peer relative h-6 w-11 rounded-full bg-gray-200 after:absolute after:start-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-red-500 peer-checked:after:-translate-x-full peer-checked:after:border-white peer-focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:peer-checked:bg-red-500 dark:peer-focus:ring-red-600 rtl:peer-checked:after:-translate-x-full">
                </div>
                <span class="ms-3 text-sm font-medium text-gray-900 dark:text-gray-300">
                    ارسال پیامکی گزارش مالی روزانه
                </span>
            </label>
            <hr>
            <label class="flex cursor-pointer items-center">
                <input
                    class="peer sr-only"
                    type="checkbox"
                    wire:click="toggleService('reportServiceUpQabzinoBot')"
                    {{ $settings['reportServiceUpQabzinoBot'] ?? false ? 'checked' : '' }}
                >
                <div
                    class="peer relative h-6 w-11 rounded-full bg-gray-200 after:absolute after:start-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-red-500 peer-checked:after:-translate-x-full peer-checked:after:border-white peer-focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:peer-checked:bg-red-500 dark:peer-focus:ring-red-600 rtl:peer-checked:after:-translate-x-full">
                </div>
                <span class="ms-3 text-sm font-medium text-gray-900 dark:text-gray-300">
                    ارسال نوتیف گزارش ارتباط با سرور قبضینو
                </span>
            </label>
            <label class="flex cursor-pointer items-center">
                <input
                    class="peer sr-only"
                    type="checkbox"
                    wire:click="toggleService('reportServiceUpQabzinoSms')"
                    {{ $settings['reportServiceUpQabzinoSms'] ?? false ? 'checked' : '' }}
                >
                <div
                    class="peer relative h-6 w-11 rounded-full bg-gray-200 after:absolute after:start-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-red-500 peer-checked:after:-translate-x-full peer-checked:after:border-white peer-focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:peer-checked:bg-red-500 dark:peer-focus:ring-red-600 rtl:peer-checked:after:-translate-x-full">
                </div>
                <span class="ms-3 text-sm font-medium text-gray-900 dark:text-gray-300">
                    ارسال پیامک گزارش ارتباط با سرور قبضینو
                </span>
            </label>
            <hr>
            <label class="flex cursor-pointer items-center">
                <input
                    class="peer sr-only"
                    type="checkbox"
                    wire:click="toggleService('reportUptimeSitesBot')"
                    {{ $settings['reportUptimeSitesBot'] ?? false ? 'checked' : '' }}
                >
                <div
                    class="peer relative h-6 w-11 rounded-full bg-gray-200 after:absolute after:start-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-red-500 peer-checked:after:-translate-x-full peer-checked:after:border-white peer-focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:peer-checked:bg-red-500 dark:peer-focus:ring-red-600 rtl:peer-checked:after:-translate-x-full">
                </div>
                <span class="ms-3 text-sm font-medium text-gray-900 dark:text-gray-300">
                    ارسال نوتیف وضعیت آنلاین بودن سرویس ها
                </span>
            </label>
            <label class="flex cursor-pointer items-center">
                <input
                    class="peer sr-only"
                    type="checkbox"
                    wire:click="toggleService('reportUptimeSitesSms')"
                    {{ $settings['reportUptimeSitesSms'] ?? false ? 'checked' : '' }}
                >
                <div
                    class="peer relative h-6 w-11 rounded-full bg-gray-200 after:absolute after:start-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-red-500 peer-checked:after:-translate-x-full peer-checked:after:border-white peer-focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:peer-checked:bg-red-500 dark:peer-focus:ring-red-600 rtl:peer-checked:after:-translate-x-full">
                </div>
                <span class="ms-3 text-sm font-medium text-gray-900 dark:text-gray-300">
                    ارسال پیامک وضعیت آنلاین بودن سرویس ها
                </span>
            </label>
        </div>
    </div>
</div>
