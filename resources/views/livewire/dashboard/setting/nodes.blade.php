<div class="">
    <p class="mb-3 text-sm font-bold text-gray-700">لیست نودهای که سرویس host-checker استفاده میکند</p>
    <div
        class="flex flex-col gap-3 text-left"
        dir="ltr"
    >
        @foreach ($nodes as $item)
            <label>
                <div class="flex cursor-pointer items-center">
                    <input
                        class="peer sr-only"
                        type="checkbox"
                        wire:click="toggleNode('{{ $item['_id'] }}')"
                        {{ $item['active'] === true ? 'checked' : '' }}
                    >
                    <div
                        class="peer relative h-6 w-11 rounded-full bg-gray-200 after:absolute after:start-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-red-500 peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:peer-checked:bg-red-500 dark:peer-focus:ring-red-600 rtl:peer-checked:after:-translate-x-full">
                    </div>
                    <div>
                        <span class="ms-3 text-sm font-medium text-gray-900 dark:text-gray-300">
                            <span class="text-base font-bold">{{ $item['country'] }}</span>
                            <span class="text-base text-gray-500">{{ ' - ' . ($item['city'] ?? '') }}</span>
                        </span>
                    </div>
                </div>
                <p class="pl-12 text-sm text-gray-400">{{ $item['hostname'] }}</p>
            </label>
        @endforeach
    </div>

</div>
