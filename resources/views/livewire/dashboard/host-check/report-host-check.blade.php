<div class="px-3 pt-3">
    {{-- <pre>{{ json_encode($chartData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) }}</pre> --}}
    @foreach ($chartData as $site)
        <div class="mb-8">
            <h3
                class="mb-2 text-left font-bold"
                dir="ltr"
            >{{ $site['host'] }}</h3>
            <canvas
                class="h-72 w-full"
                id="chart-{{ Str::slug($site['host']) }}"
            ></canvas>

            <script>
                document.addEventListener('livewire:load', () => {
                    const ctx{{ Str::slug($site['host']) }} = document.getElementById(
                            'chart-{{ Str::slug($site['host']) }}')
                        .getContext('2d');

                    new Chart(ctx{{ Str::slug($site['host']) }}, {
                        type: 'line',
                        data: {
                            labels: {!! json_encode(collect($site['data'])->pluck('date')) !!},
                            datasets: [{
                                label: 'Uptime (%)',
                                data: {!! json_encode(collect($site['data'])->pluck('uptime')) !!},
                                borderColor: 'green',
                                backgroundColor: 'rgba(0,128,0,0.1)',
                                fill: true,
                                tension: 0.3
                            }]
                        },
                        options: {
                            responsive: true,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    max: 100,
                                    title: {
                                        display: true,
                                        text: 'درصد در دسترس بودن'
                                    }
                                },
                                x: {
                                    title: {
                                        display: true,
                                        text: 'تاریخ'
                                    }
                                }
                            }
                        }
                    });
                });
            </script>

        </div>
    @endforeach

</div>
