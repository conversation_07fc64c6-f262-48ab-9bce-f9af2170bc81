<div class="relative min-h-96 rounded-xl">

    <div
        class="overflow-hidden rounded-t-xl"
        x-data="{ fillter: false }"
        wire:ignore
    >
        <div class="rounded-t-xl bg-gray-800 px-3 py-1.5">
            <div class="flex items-center justify-between">
                <div>
                    <span class="block text-base text-white max-md:text-sm">لیست استعلامات <span
                            class="rounded-xl bg-red-500 px-2 text-base text-white max-md:text-xs"
                        >{{ formatMoney(
                            cache()->remember('inquiry_results_negativePoints_counts', 3600, function () {
                                return DB::connection('mongodb')->table('inquiry_results')->where('type', 'negativePoints')->count();
                            }),
                        ) }}</span></span>
                </div>
                <div
                    class="flex items-center gap-4"
                    x-data="playerState()"
                    x-init="checkState()"
                >

                    <div x-show="isPlaying">
                        <span
                            class="text-sm text-gray-500"
                            x-text="countdown"
                        ></span>
                        <span class="text-sm text-gray-500">ثانیه</span>
                    </div>
                    <div
                        class="text-sm font-bold text-gray-100"
                        x-show="isPlaying"
                    >
                        زمان بروزرسانی </div>
                    <button
                        class="flex items-center gap-2 text-white"
                        @click="togglePlay"
                    >
                        <span :class="!isPlaying ? 'text-green-500' : 'text-gray-500'">
                            <svg
                                class="size-6"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M15.75 5.25v13.5m-7.5-13.5v13.5"
                                />
                            </svg>
                        </span>
                        <span :class="isPlaying ? 'text-green-500' : 'text-gray-500'">
                            <svg
                                class="size-6"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z"
                                />
                            </svg>
                        </span>
                    </button>

                    <button
                        class="flex items-center gap-2 p-2"
                        @click="fillter = !fillter"
                    >
                        <svg
                            class="h-6 w-6 text-white"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                d="M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75"
                            />
                        </svg>
                        <span class="text-base text-white">فیلتر</span>
                    </button>

                </div>
            </div>
        </div>
        <form
            class="tranfsition-all transform overflow-hidden bg-gray-800 px-6"
            wire:submit="fillter"
            x-cloak
            :class="fillter ? 'h-auto pt-4' : 'h-0'"
        >

            <div class="my-3 grid grid-cols-1 gap-3 md:grid-cols-8">

                <div>
                    <label
                        class="mb-2 block text-sm text-white"
                        for="phone"
                    >شماره تماس:</label>
                    <input
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="phone"
                        type="text"
                        wire:model="data.phone"
                    >
                    @error('data.phone')
                        <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                    class="text-sm font-bold text-red-600"
                                >{{ $message }}</span></span></div>
                    @enderror
                </div>

                <div>
                    <label
                        class="mb-2 block text-sm text-white"
                        for="balance"
                    >موجودی بیش از :</label>
                    <input
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-center text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="balance"
                        type="text"
                        wire:model="data.balance"
                        onkeyup="javascript:this.value=Comma(this.value);"
                    >
                    @error('data.balance')
                        <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                    class="text-sm font-bold text-red-600"
                                >{{ $message }}</span></span></div>
                    @enderror
                </div>
                <div>
                    <label
                        class="mb-2 block text-sm text-white"
                        for="deposit"
                    >بازدید سپرده:</label>
                    <select
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 px-6 py-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="deposit"
                        wire:model="data.deposit"
                    >
                        <option value="">--انتخاب کنید--</option>

                    </select>
                    @error('data.deposit')
                        <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                    class="text-sm font-bold text-red-600"
                                >{{ $message }}</span></span></div>
                    @enderror
                </div>
                <div>
                    <label
                        class="mb-2 block text-sm text-white"
                        for="ads"
                    >تبلیغات:</label>
                    <select
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 px-6 py-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="ads"
                        wire:model="data.ads"
                    >
                        <option value="">--انتخاب کنید--</option>

                    </select>
                    @error('data.ads')
                        <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                    class="text-sm font-bold text-red-600"
                                >{{ $message }}</span></span></div>
                    @enderror
                </div>
                <div>
                    <label
                        class="mb-2 block text-sm text-white"
                        for="gate"
                    >بازدید درگاه پرداخت:</label>
                    <select
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 px-6 py-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="gate"
                        wire:model="data.gate"
                    >
                        <option value="">--انتخاب کنید--</option>

                    </select>
                    @error('data.gate')
                        <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                    class="text-sm font-bold text-red-600"
                                >{{ $message }}</span></span></div>
                    @enderror
                </div>

                <div>
                    <label
                        class="mb-2 block text-sm text-white"
                        for="data_start"
                    >از تاریخ:</label>
                    <input
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="data_start"
                        data-jdp
                        type="text"
                        wire:model="dateStart"
                    >
                    @error('dateStart')
                        <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                    class="text-sm font-bold text-red-600"
                                >{{ $message }}</span></span></div>
                    @enderror
                </div>
                <div>
                    <label
                        class="mb-2 block text-sm text-white"
                        for="date_end"
                    >تا تاریخ:</label>
                    <input
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="date_end"
                        data-jdp
                        type="text"
                        wire:model="dateEnd"
                    >
                    @error('dateEnd')
                        <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                    class="text-sm font-bold text-red-600"
                                >{{ $message }}</span></span></div>
                    @enderror
                </div>
            </div>

            <div class="flex flex-row-reverse items-center gap-3 py-3">

                <button
                    class="rounded-lg bg-red-500 px-6 py-1 text-white transition-all hover:bg-red-600 disabled:bg-gray-100 disabled:text-gray-400"
                    type="submit"
                >
                    <svg
                        class="inline h-4 w-4 animate-spin text-red-600 dark:text-red-500"
                        role="status"
                        aria-hidden="true"
                        wire:loading
                        wire:target="fillter"
                        viewBox="0 0 100 101"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="#E5E7EB"
                        />
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentColor"
                        />
                    </svg>
                    <span class="text-sm">اعمال فیلتر</span>
                </button>
                <button
                    class="rounded-lg bg-gray-300 px-6 py-1 text-gray-600 transition-all hover:bg-gray-200 disabled:bg-gray-100 disabled:text-gray-400"
                    type="button"
                    wire:click="ClearFillter"
                >
                    <svg
                        class="inline h-4 w-4 animate-spin text-red-600 dark:text-red-500"
                        role="status"
                        aria-hidden="true"
                        wire:loading
                        wire:target="ClearFillter"
                        viewBox="0 0 100 101"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="#E5E7EB"
                        />
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentColor"
                        />
                    </svg>
                    <span class="text-sm">حذف فیلترها</span>
                </button>
            </div>
        </form>
    </div>

    <div class="min-h-96 rounded-b-xl bg-white">

        <div class="w-full">

            <div class="relative bg-white">
                @include('layouts.tools.loading')

                <div
                    class="w-full overflow-x-auto"
                    wire:key="{{ now()->timestamp }}"
                >
                    <table class="w-full whitespace-nowrap">
                        <thead class="bg-gray-50">
                            <tr
                                class="mb-3 h-16 rounded border border-gray-200 focus:outline-none"
                                tabindex="0"
                            >

                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">شماره تماس</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">شماره گواهینامه</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">شماره ملی</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">مجاز به رانندگی</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">نمره منفی</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">شناسه قانون</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">تاریخ استعلام</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">ساعت استعلام</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold"> موجودی (قبل)</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold"> موجودی (بعد)</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">نتیجه</span>
                                </td>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($inquiries as $item)
                                <tr
                                    class="@if (isset($item?->balance) && $item?->balance == 0) bg-gray-100 @endif @if (isset($item->redirect) && $item->redirect == true) bg-yellow-100 @endif h-16 rounded border border-gray-200 transition-all hover:bg-gray-100 focus:outline-none"
                                    tabindex="0"
                                >
                                    <td class="px-2 text-center">
                                        <span class="flex flex-col gap-1">
                                            <span class="text-base">{{ $item?->phone }}</span>
                                            <span
                                                class="flex items-center justify-center gap-2 rounded-3xl bg-gray-100 px-1 py-0.5 text-gray-600"
                                            >
                                                <span
                                                    class="text-sm">{{ getUserBalance($item->phone, $item->source, $item->redirect ?? false) }}</span>
                                                <span class="text-xs text-gray-500">تومان</span>
                                            </span>
                                        </span>
                                    </td>
                                    <td class="px-2 text-center">
                                        <span>{{ $item->license_number }}</span>
                                    </td>
                                    <td class="px-2 text-center">
                                        <span>{{ $item->national_id }}</span>
                                    </td>
                                    <td class="px-2 text-center">
                                        <span>{{ isset($item->result['Parameters']['AllowedToDrive']) ? $item->result['Parameters']['AllowedToDrive'] : '' }}</span>
                                    </td>
                                    <td class="px-2 text-center">
                                        <span>{{ isset($item->result['Parameters']['Point']) ? $item->result['Parameters']['Point'] : '' }}</span>
                                    </td>
                                    <td class="px-2 text-center">
                                        <span>{{ isset($item->result['Parameters']['Rule']) ? $item->result['Parameters']['Rule'] : '' }}</span>
                                    </td>
                                    <td
                                        class="px-2 text-center"
                                        dir="ltr"
                                    >
                                        <span
                                            class="text-base">{{ $item?->created_at ? shamsiDateLimit($item?->created_at) : '' }}</span>
                                    </td>
                                    <td
                                        class="px-2 text-center"
                                        dir="ltr"
                                    >
                                        <span class="flex flex-row-reverse items-center justify-center gap-2">
                                            <span
                                                class="text-base">{{ $item?->created_at ? TimeDateLimit($item?->created_at) : '' }}</span>
                                            <svg
                                                class="size-5 text-gray-400"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                                                />
                                            </svg>
                                        </span>

                                    </td>

                                    <td class="px-2 text-center">
                                        @if (isset($item?->balance))
                                            <span class="flex items-center justify-center gap-2">
                                                <span class="text-base">{{ formatMoney($item?->balance) }}</span>
                                                <span class="text-xs text-gray-500">تومان</span>
                                            </span>
                                        @endif
                                    </td>
                                    <td class="px-2 text-center">
                                        @if (isset($item?->balance_after))
                                            <span class="flex items-center justify-center gap-2">
                                                <span
                                                    class="text-base">{{ formatMoney($item?->balance_after) }}</span>
                                                <span class="text-xs text-gray-500">تومان</span>
                                            </span>
                                        @endif
                                    </td>
                                    <td class="px-2 text-center">
                                        <span class="flex items-center justify-center gap-2">
                                            @if (isset($item->result['Status']['Description']) && $item->result['Status']['Description'] != null)
                                                @if ($item->result['Status']['Description'] == 'درخواست با موفقیت انجام شد.')
                                                    <p class="whitespace-normal break-words text-base text-green-500">
                                                        {{ $item->result['Status']['Description'] }}</p>
                                                @else
                                                    <p class="whitespace-normal break-words text-base text-red-500">
                                                        {{ $item->result['Status']['Description'] }}</p>
                                                @endif
                                            @endif

                                        </span>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <div
                    class="w-full bg-gray-100 p-3"
                    wire:loading.remove
                >
                    {{ $inquiries->links(data: ['dark' => false]) }}
                </div>
            </div>
        </div>
        <style>
            .checkbox:checked+.check-icon {
                display: flex;
            }
        </style>

    </div>
</div>
