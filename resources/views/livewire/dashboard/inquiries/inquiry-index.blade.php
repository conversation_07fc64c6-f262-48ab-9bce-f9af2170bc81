<div class="relative min-h-96 rounded-xl">

    <div
        class="overflow-hidden rounded-t-xl"
        x-data="{ fillter: false }"
        wire:ignore
    >
        <div class="rounded-t-xl bg-gray-800 px-3 py-1.5">
            <div class="flex items-center justify-between">
                <div>
                    <span class="block text-base text-white max-md:text-sm">لیست استعلامات <span
                            class="rounded-xl bg-red-500 px-2 text-base text-white max-md:text-xs"
                        >{{ formatMoney(
                            cache()->remember('inquiry_results_counts', 3600, function () {
                                return DB::connection('mongodb')->table('inquiry_results')->count();
                            }),
                        ) }}</span></span>
                </div>
                <div
                    class="flex items-center gap-4"
                    x-data="playerState()"
                    x-init="checkState()"
                >

                    <div x-show="isPlaying">
                        <span
                            class="text-sm text-gray-500"
                            x-text="countdown"
                        ></span>
                        <span class="text-sm text-gray-500">ثانیه</span>
                    </div>
                    <div
                        class="text-sm font-bold text-gray-100"
                        x-show="isPlaying"
                    >
                        زمان بروزرسانی </div>
                    <button
                        class="flex items-center gap-2 text-white"
                        @click="togglePlay"
                    >
                        <span :class="!isPlaying ? 'text-green-500' : 'text-gray-500'">
                            <svg
                                class="size-6"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M15.75 5.25v13.5m-7.5-13.5v13.5"
                                />
                            </svg>
                        </span>
                        <span :class="isPlaying ? 'text-green-500' : 'text-gray-500'">
                            <svg
                                class="size-6"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z"
                                />
                            </svg>
                        </span>
                    </button>

                    <button
                        class="flex items-center gap-2 p-2"
                        @click="fillter = !fillter"
                    >
                        <svg
                            class="h-6 w-6 text-white"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                d="M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75"
                            />
                        </svg>
                        <span class="text-base text-white">فیلتر</span>
                    </button>

                </div>
            </div>
        </div>
        <form
            class="tranfsition-all transform overflow-hidden bg-gray-800 px-6"
            wire:submit="fillter"
            x-cloak
            :class="fillter ? 'h-auto pt-4' : 'h-0'"
        >

            <div class="mb-3 grid grid-cols-1 gap-3 md:grid-cols-8">

                <div>
                    <label
                        class="mb-2 block text-sm text-white"
                        for="phone"
                    >شماره تماس:</label>
                    <input
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="phone"
                        type="text"
                        wire:model="data.phone"
                    >
                    @error('data.phone')
                        <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                    class="text-sm font-bold text-red-600"
                                >{{ $message }}</span></span></div>
                    @enderror
                </div>

                <div>
                    <label
                        class="mb-2 block text-sm text-white"
                        for="balance"
                    >موجودی بیش از :</label>
                    <input
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-center text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="balance"
                        type="text"
                        wire:model="data.balance"
                        onkeyup="javascript:this.value=Comma(this.value);"
                    >
                    @error('data.balance')
                        <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                    class="text-sm font-bold text-red-600"
                                >{{ $message }}</span></span></div>
                    @enderror
                </div>
                <div>
                    <label
                        class="mb-2 block text-sm text-white"
                        for="deposit"
                    >بازدید سپرده:</label>
                    <select
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 px-6 py-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="deposit"
                        wire:model="data.deposit"
                    >
                        <option value="">--انتخاب کنید--</option>

                    </select>
                    @error('data.deposit')
                        <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                    class="text-sm font-bold text-red-600"
                                >{{ $message }}</span></span></div>
                    @enderror
                </div>
                <div>
                    <label
                        class="mb-2 block text-sm text-white"
                        for="ads"
                    >تبلیغات:</label>
                    <select
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 px-6 py-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="ads"
                        wire:model="data.ads"
                    >
                        <option value="">--انتخاب کنید--</option>

                    </select>
                    @error('data.ads')
                        <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                    class="text-sm font-bold text-red-600"
                                >{{ $message }}</span></span></div>
                    @enderror
                </div>
                <div>
                    <label
                        class="mb-2 block text-sm text-white"
                        for="gate"
                    >بازدید درگاه پرداخت:</label>
                    <select
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 px-6 py-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="gate"
                        wire:model="data.gate"
                    >
                        <option value="">--انتخاب کنید--</option>

                    </select>
                    @error('data.gate')
                        <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                    class="text-sm font-bold text-red-600"
                                >{{ $message }}</span></span></div>
                    @enderror
                </div>

                <div>
                    <label
                        class="mb-2 block text-sm text-white"
                        for="data_start"
                    >از تاریخ:</label>
                    <input
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="data_start"
                        data-jdp
                        type="text"
                        wire:model="dateStart"
                    >
                    @error('dateStart')
                        <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                    class="text-sm font-bold text-red-600"
                                >{{ $message }}</span></span></div>
                    @enderror
                </div>
                <div>
                    <label
                        class="mb-2 block text-sm text-white"
                        for="date_end"
                    >تا تاریخ:</label>
                    <input
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="date_end"
                        data-jdp
                        type="text"
                        wire:model="dateEnd"
                    >
                    @error('dateEnd')
                        <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                    class="text-sm font-bold text-red-600"
                                >{{ $message }}</span></span></div>
                    @enderror
                </div>
            </div>

            <div class="flex flex-row-reverse items-center gap-3 py-3">

                <button
                    class="rounded-lg bg-red-500 px-6 py-1 text-white transition-all hover:bg-red-600 disabled:bg-gray-100 disabled:text-gray-400"
                    type="submit"
                >
                    <svg
                        class="inline h-4 w-4 animate-spin text-red-600 dark:text-red-500"
                        role="status"
                        aria-hidden="true"
                        wire:loading
                        wire:target="fillter"
                        viewBox="0 0 100 101"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="#E5E7EB"
                        />
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentColor"
                        />
                    </svg>
                    <span class="text-sm">اعمال فیلتر</span>
                </button>
                <button
                    class="rounded-lg bg-gray-300 px-6 py-1 text-gray-600 transition-all hover:bg-gray-200 disabled:bg-gray-100 disabled:text-gray-400"
                    type="button"
                    wire:click="ClearFillter"
                >
                    <svg
                        class="inline h-4 w-4 animate-spin text-red-600 dark:text-red-500"
                        role="status"
                        aria-hidden="true"
                        wire:loading
                        wire:target="ClearFillter"
                        viewBox="0 0 100 101"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="#E5E7EB"
                        />
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentColor"
                        />
                    </svg>
                    <span class="text-sm">حذف فیلترها</span>
                </button>
            </div>
        </form>
    </div>

    <div class="min-h-96 rounded-b-xl bg-white shadow">
        <div
            class="flex items-center gap-3 overflow-x-auto bg-gray-100 px-3 py-2"
            x-data="{
                selectedService: @entangle('service').defer,
                updateQueryParam(service) {
                    this.selectedService = service;
                    window.history.pushState({}, '', `?service=${service}`);
                    Livewire.dispatch('filterService', { service });
                }
            }"
        >
            @foreach ([
        'all' => 'همه سرویس ها',
        'khodrox' => 'خودراکس',
        'khodroyar' => 'خودرویار',
        'naghlie' => 'نقلیه',
        'test' => 'سیستم تست',
        'khalafionline' => 'خلافی آنلاین',
    ] as $key => $label)
                @php
                    $borderColor = match ($key) {
                        'khodroyar' => 'border-yellow-300 text-yellow-700',
                        'nikzi' => 'border-blue-500 text-blue-500',
                        'migrofin' => 'border-green-500 text-green-500',
                        'bankyar' => 'border-red-500 text-red-500',
                        'pishkhan724' => 'border-purple-500 text-purple-500',
                        'kouroshcc' => 'border-pink-500 text-pink-500',
                        'shebacart' => 'border-indigo-500 text-indigo-500',
                        'cardtosheba' => 'border-teal-500 text-teal-500',
                        'irancheque' => 'border-orange-500 text-orange-500',
                        'tartankala' => 'border-gray-500 text-gray-500',
                        'khalafiyar' => 'border-yellow-500 text-yellow-500',
                        'cardbecard' => 'border-blue-500 text-blue-500',
                        'pishkhaneman' => 'border-green-500 text-green-500',
                        'test' => 'border-red-500 text-red-500',
                        'naghlie' => 'border-blue-500 text-blue-500',
                        'khalafionline' => 'border-red-500 text-red-500',
                        'khodrox' => 'border-green-500 text-green-500',
                        default => 'border-gray-500 text-gray-700',
                    };
                @endphp

                <button
                    class="flex shrink-0 items-center justify-center rounded-2xl border-2 bg-white px-4 py-2 text-sm transition-all hover:bg-gray-100"
                    @click="updateQueryParam('{{ $key }}')"
                    :class="selectedService === '{{ $key }}' ? '{{ $borderColor }}' :
                        'border-gray-100 text-gray-700'"
                >
                    <span class="whitespace-nowrap">{{ $label }}</span>
                </button>
            @endforeach
            <button
                class="flex shrink-0 items-center justify-center rounded-2xl border-2 bg-white px-4 py-2 text-sm transition-all hover:bg-gray-100"
                wire:click="searchWithoutInquiryAmount"
            >
                <span class="whitespace-nowrap">بدون نتیجه ها</span>
            </button>
        </div>

        <div class="w-full">

            <div class="relative bg-white">
                @include('layouts.tools.loading')

                <div
                    class="w-full overflow-x-auto"
                    wire:key="{{ now()->timestamp }}"
                >
                    <table class="w-full whitespace-nowrap">
                        <thead class="bg-gray-50">
                            <tr
                                class="mb-3 h-16 rounded border border-gray-200 focus:outline-none"
                                tabindex="0"
                            >

                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">شماره تماس</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">سرویس</span>
                                </td>
                                {{-- <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">خودرو / موتور</span>
                                </td> --}}
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">پلاک</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold"> استعلامات</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">شماره ملی</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">شماره تماس مالک</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">تاریخ استعلام</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">ساعت استعلام</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">تکرار</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">عکس</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">مبلغ کل خلافی</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold"> موجودی (قبل)</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold"> موجودی (بعد)</span>
                                </td>
                                {{-- <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">موجودی (فعلی)</span>
                                </td> --}}
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">تنظیمات</span>
                                </td>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($inquiries as $item)
                                <tr
                                    class="@if (isset($item?->balance) && $item?->balance == 0) bg-gray-100 @endif @if (isset($item->redirect) && $item->redirect == true) bg-yellow-100 @endif h-16 rounded border border-gray-200 transition-all hover:bg-gray-100 focus:outline-none"
                                    tabindex="0"
                                >
                                    <td class="px-2 text-center">
                                        <span class="flex flex-col gap-1">
                                            <span class="text-base">{{ $item?->phone }}</span>
                                            <span
                                                class="flex items-center justify-center gap-2 rounded-3xl bg-gray-100 px-1 py-0.5 text-gray-600"
                                            >
                                                <span
                                                    class="text-sm">{{ getUserBalance($item->phone, $item->source, $item->redirect ?? false) }}</span>
                                                <span class="text-xs text-gray-500">تومان</span>
                                            </span>
                                        </span>
                                    </td>
                                    <td class="px-2 text-center">

                                        @php
                                            $service = getService($item->source);
                                        @endphp
                                        @if (isset($item->redirect) && $item->redirect == true)
                                            <span
                                                class="relative rounded-xl bg-yellow-300 px-3 py-0.5 text-sm text-gray-700"
                                            >
                                                خلافیار به - {{ $service['text'] }}
                                            </span>
                                        @else
                                            <span class="{{ $service['class'] }} relative text-sm">
                                                {{ $service['text'] }}
                                            </span>
                                        @endif

                                    </td>
                                    {{-- <td class="px-2 text-center">
                                        @if ($item->type == 'car')
                                            <span class="flex items-center gap-2">
                                                <svg
                                                    class="size-6 text-gray-700"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    viewBox="0 -960 960 960"
                                                    fill="#374151"
                                                >
                                                    <path
                                                        d="M200-204v54q0 12.75-8.62 21.37Q182.75-120 170-120h-20q-12.75 0-21.37-8.63Q120-137.25 120-150v-324l85-256q3-14 15.4-22t27.6-8h127v-75h212v75h125q15.2 0 27.6 8 12.4 8 15.4 22l85 256v324q0 12.75-8.62 21.37Q822.75-120 810-120h-21q-12.75 0-21.37-8.63Q759-137.25 759-150v-54H200Zm3-330h554l-55-166H258l-55 166Zm-23 60v210-210Zm105.76 160q23.24 0 38.74-15.75Q340-345.5 340-368q0-23.33-15.75-39.67Q308.5-424 286-424q-23.33 0-39.67 16.26Q230-391.47 230-368.24q0 23.24 16.26 38.74 16.27 15.5 39.5 15.5ZM675-314q23.33 0 39.67-15.75Q731-345.5 731-368q0-23.33-16.26-39.67Q698.47-424 675.24-424q-23.24 0-38.74 16.26-15.5 16.27-15.5 39.5 0 23.24 15.75 38.74Q652.5-314 675-314Zm-495 50h600v-210H180v210Z"
                                                    ></path>
                                                </svg>
                                                <span class="text-sm font-bold">خودرو</span>
                                            </span>
                                        @else
                                            <span class="flex items-center gap-2">
                                                <svg
                                                    class="size-6 text-gray-700"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    viewBox="0 -960 960 960"
                                                    fill="#374151"
                                                >
                                                    <path
                                                        d="M447-540H343h213-109ZM200-200q-83 0-141.5-58.5T0-400q0-83 58.5-141.5T200-600h474L574-700H423v-60h151q12 0 23.5 5t19.5 13l142 142q90 0 145.5 61T960-400q0 83-58.5 141.5T760-200q-83 0-141.5-58.5T560-400q0-18 2.5-35.5T572-470L462-360h-66q-14 70-69 115t-127 45Zm560-60q58 0 99-41t41-99q0-58-41-99t-99-41q-58 0-99 41t-41 99q0 58 41 99t99 41Zm-560 0q45 0 84-28t53-82H220v-60h117q-14-54-53-82t-84-28q-58 0-99 41t-41 99q0 58 41 99t99 41Zm198-170h49l109-110H343q20 20 35 48.5t20 61.5Z"
                                                    ></path>
                                                </svg>
                                                <span class="text-sm font-bold">موتور سیکلت</span>
                                            </span>
                                        @endif

                                    </td> --}}
                                    <td class="px-2 text-left">
                                        @if ($item?->type == 'car')
                                            <span
                                                class="flex w-44 max-w-44 items-center gap-2 overflow-hidden whitespace-nowrap rounded-xl bg-gray-100 max-md:w-48 max-md:max-w-40"
                                                dir="ltr"
                                            >
                                                <img
                                                    class="w-7"
                                                    src="/assets/images/plate-number-bg.svg"
                                                    alt=""
                                                >
                                                <div class="flex items-center gap-2">
                                                    <span>{{ $item->plaque_right }}</span>
                                                    <span>-</span>
                                                    <span>{{ $item->plaque_mid }}</span>

                                                    <span class="text-gray-500">{{ $item->plaque_alphabet }}</span>
                                                    <span>{{ isset($item->plaque_left) ? $item->plaque_left : '-' }}</span>
                                                </div>
                                            </span>
                                        @else
                                            <span
                                                class="flex max-w-48 items-center gap-2 overflow-hidden whitespace-nowrap rounded-xl bg-gray-100"
                                                dir="ltr"
                                            >
                                                <img
                                                    class="w-16"
                                                    src="/assets/images/motor-plate.svg"
                                                    alt=""
                                                >
                                                <div class="flex items-center gap-2">
                                                    <span>{{ isset($item->plaque_left) ? $item->plaque_left : '-' }}</span>
                                                    <span>-</span>
                                                    <span>{{ $item->plaque_right }}</span>
                                                </div>
                                            </span>
                                        @endif
                                    </td>
                                    <td class="px-2 text-center">
                                        @php
                                            $data = [
                                                'plaque_left' => $item->plaque_left,
                                                'plaque_mid' => $item->plaque_mid,
                                                'plaque_right' => $item->plaque_right,
                                                'plaque_alphabet' => $item->plaque_alphabet,
                                            ];
                                        @endphp
                                        <span>{{ getInquiryCount($item->phone, $item->source, $data) }}</span>
                                    </td>
                                    <td class="px-2 text-center">
                                        <span>{{ $item->detail_national_id }}</span>
                                    </td>
                                    <td class="px-2 text-center">
                                        <span>{{ $item->detail_phone }}</span>
                                    </td>
                                    <td
                                        class="px-2 text-center"
                                        dir="ltr"
                                    >
                                        <span
                                            class="text-base">{{ $item?->created_at ? shamsiDateLimit($item?->created_at) : '' }}</span>
                                    </td>
                                    <td
                                        class="px-2 text-center"
                                        dir="ltr"
                                    >
                                        <span class="flex flex-row-reverse items-center justify-center gap-2">
                                            <span
                                                class="text-base">{{ $item?->created_at ? TimeDateLimit($item?->created_at) : '' }}</span>
                                            <svg
                                                class="size-5 text-gray-400"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                                                />
                                            </svg>
                                        </span>

                                    </td>
                                    <td
                                        class="px-2 text-center"
                                        dir="ltr"
                                    >
                                        <span
                                            class="text-base">{{ isset($item?->data['inquiry']) && $item?->data['inquiry'] == true ? 'بله' : '' }}</span>
                                    </td>
                                    <td
                                        class="px-2 text-center"
                                        dir="ltr"
                                    >
                                        <button
                                            class="text-gray-400 hover:text-gray-500 focus:text-gray-500 focus:outline-none"
                                            type="button"
                                        >
                                            <svg
                                                class="size-5"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M6.827 6.175A2.31 2.31 0 0 1 5.186 7.23c-.38.054-.757.112-1.134.175C2.999 7.58 2.25 8.507 2.25 9.574V18a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9.574c0-1.067-.75-1.994-1.802-2.169a47.865 47.865 0 0 0-1.134-.175 2.31 2.31 0 0 1-1.64-1.055l-.822-1.316a2.192 2.192 0 0 0-1.736-1.039 48.774 48.774 0 0 0-5.232 0 2.192 2.192 0 0 0-1.736 1.039l-.821 1.316Z"
                                                />
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M16.5 12.75a4.5 4.5 0 1 1-9 0 4.5 4.5 0 0 1 9 0ZM18.75 10.5h.008v.008h-.008V10.5Z"
                                                />
                                            </svg>

                                        </button>
                                    </td>
                                    <td class="px-2 text-center">
                                        <span class="flex items-center justify-center gap-2">
                                            @php
                                                $parameters = $item->result['Parameters'] ?? null;
                                                $amount = $parameters['TotalAmount'] ?? ($parameters['Amount'] ?? null);
                                            @endphp
                                            @if ($amount !== null)
                                                {{ formatMoney($amount / 10) }} <span
                                                    class="text-xs text-gray-500">تومان</span>
                                            @endif

                                        </span>
                                    </td>
                                    <td class="px-2 text-center">
                                        @if (isset($item?->balance))
                                            <span class="flex items-center justify-center gap-2">
                                                <span class="text-base">{{ formatMoney($item?->balance) }}</span>
                                                <span class="text-xs text-gray-500">تومان</span>
                                            </span>
                                        @endif
                                    </td>
                                    <td class="px-2 text-center">
                                        @if (isset($item?->balance_after))
                                            <span class="flex items-center justify-center gap-2">
                                                <span
                                                    class="text-base">{{ formatMoney($item?->balance_after) }}</span>
                                                <span class="text-xs text-gray-500">تومان</span>
                                            </span>
                                        @endif
                                    </td>
                                    {{-- <td class="px-2 text-center">
                                        <span class="flex items-center justify-center gap-2">
                                            <span
                                                class="text-base">{{ getUserBalance($item->phone, $item->source) }}</span>
                                            <span class="text-xs text-gray-500">تومان</span>
                                        </span>

                                    </td> --}}
                                    <td class="px-2 text-left">
                                        <a
                                            class="rounded-xl bg-gray-100 px-4 py-1.5"
                                            href="https://khodrox.com/tickets-result?traceNumber={{ $item?->id }}&reInquiry=false&isMotor=false"
                                            target="_blank"
                                        >
                                            <span class="text-base text-gray-700">...</span>
                                        </a>
                                    </td>
                                </tr>
                                @if (isset($item->result['Status']['Description']) && $item->result['Status']['Code'] != 'G00000')
                                    <tr class="border-b border-red-500 bg-white">
                                        <td
                                            class="p-2"
                                            colspan="13"
                                        >
                                            <p class="whitespace-normal break-words text-sm text-red-500">
                                                {{ $item->result['Status']['Description'] }}</p>
                                        </td>
                                    </tr>
                                @endif
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <div
                    class="w-full bg-gray-100 p-3"
                    wire:loading.remove
                >
                    {{ $inquiries->links(data: ['dark' => false]) }}
                </div>
            </div>
        </div>
        <style>
            .checkbox:checked+.check-icon {
                display: flex;
            }
        </style>

    </div>
</div>
