<div class="relative min-h-96 rounded-xl">

    {{-- Summary Statistics --}}
    <div class="mb-4 grid grid-cols-2 gap-3 md:grid-cols-4">
        <div class="rounded-xl bg-gradient-to-r from-blue-500 to-blue-600 p-6 text-white shadow-lg">
            <div class="flex items-center justify-between">
                <div>
                    <p class="mb-3 text-xl font-bold">{{ count($campaignResults) }}</p>
                    <p class="text-sm text-blue-100">کل کمپین‌ها</p>
                </div>
                <div class="rounded-full">
                    <svg
                        class="size-16 text-blue-400"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke-width="1.5"
                        stroke="currentColor"
                    >
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"
                        />
                    </svg>

                </div>
            </div>
        </div>

        <div class="rounded-xl bg-gradient-to-r from-green-500 to-green-600 p-6 text-white shadow-lg">
            <div class="flex items-center justify-between">
                <div>
                    <p class="mb-3 text-xl font-bold">{{ number_format($campaignResults->sum('sum_transactions')) }}</p>
                    <p class="text-sm text-green-100">کل درآمد (تومان)</p>
                    {{-- <p class="text-xs text-green-100">تومان</p> --}}
                </div>
                <svg
                    class="size-16 text-green-400"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                >
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                    />
                </svg>

            </div>
        </div>

        <div class="rounded-xl bg-gradient-to-r from-yellow-500 to-yellow-600 p-6 text-white shadow-lg">
            <div class="flex items-center justify-between">
                <div>
                    <p class="mb-3 text-xl font-bold">{{ number_format($campaignResults->sum('count_click')) }}</p>
                    <p class="text-sm text-yellow-100">کل کلیک‌ها</p>
                </div>

                <svg
                    class="size-16 text-yellow-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                >
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122"
                    ></path>
                </svg>

            </div>
        </div>

        <div class="rounded-xl bg-gradient-to-r from-purple-500 to-purple-600 p-6 text-white shadow-lg">
            <div class="flex items-center justify-between">
                <div>
                    <p class="mb-3 text-xl font-bold">
                        {{ number_format($campaignResults->sum('total_subscribers_user') + $campaignResults->sum('total_guest_user')) }}
                    </p>
                    <p class="text-sm text-purple-100">کل کاربران</p>
                </div>
                <svg
                    class="size-16 text-purple-400"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                >
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"
                    />
                </svg>

            </div>
        </div>
    </div>

    {{-- Charts Toggle --}}
    <div class="mb-3 flex items-center justify-between">
        <h2 class="text-lg font-bold text-white">چارت‌های تحلیلی</h2>
        <button
            class="rounded-lg bg-gray-100 px-3 py-1 text-sm text-gray-600 hover:bg-gray-200"
            id="chartsToggle"
            onclick="toggleCharts()"
        >
            مخفی کردن چارت‌ها
        </button>
    </div>

    {{-- Charts Section --}}
    <div
        class="mb-4 grid grid-cols-1 gap-4 lg:grid-cols-2"
        id="chartsContainer"
        wire:ignore
    >
        {{-- Campaign Performance Chart --}}
        <div class="rounded-xl bg-white p-4 shadow-md">
            <h3 class="mb-3 text-base font-bold text-gray-800">عملکرد کمپین‌ها</h3>
            <canvas
                class="w-full"
                id="campaignPerformanceChart"
            ></canvas>
        </div>

        {{-- Conversion Rate Chart --}}
        <div class="rounded-xl bg-white p-4 shadow-md">
            <h3 class="mb-3 text-base font-bold text-gray-800">نرخ تبدیل (CR)</h3>
            <canvas
                class="w-full"
                id="conversionRateChart"
            ></canvas>
        </div>

        {{-- User Distribution Chart --}}
        <div class="rounded-xl bg-white p-4 shadow-md">
            <h3 class="mb-3 text-base font-bold text-gray-800">توزیع کاربران</h3>
            <canvas
                class="mx-auto max-h-96 w-full max-w-96"
                id="userDistributionChart"
            ></canvas>
        </div>

        {{-- Click Performance Chart --}}
        <div class="rounded-xl bg-white p-4 shadow-md">
            <h3 class="mb-3 text-base font-bold text-gray-800">عملکرد کلیک‌ها</h3>
            <canvas
                class="w-full"
                id="clickPerformanceChart"
            ></canvas>
        </div>

        {{-- Budget vs Revenue Chart --}}
        <div
            class="mb-4 rounded-xl bg-white p-4 shadow-md"
            wire:ignore
        >
            <h3 class="mb-3 text-base font-bold text-gray-800">مقایسه بودجه و درآمد</h3>
            <canvas
                class="w-full"
                id="budgetRevenueChart"
            ></canvas>
        </div>

        {{-- ROI Chart --}}
        <div
            class="mb-4 rounded-xl bg-white p-4 shadow-md"
            wire:ignore
        >
            <h3 class="mb-3 text-base font-bold text-gray-800">بازگشت سرمایه (ROI)</h3>
            <canvas
                class="w-full"
                id="roiChart"
            ></canvas>
        </div>
    </div>

    <div
        class="mb-6 rounded-b-xl bg-white shadow-md"
        wire:ignore
    >

        <div class="w-full">

            <div class="relative bg-white">
                @include('layouts.tools.loading')

                <div
                    class="w-full overflow-x-auto"
                    wire:key="{{ now()->timestamp }}"
                >
                    <table class="w-full whitespace-nowrap">
                        <thead class="bg-gray-50">
                            <tr
                                class="mb-3 h-16 rounded border border-gray-200 focus:outline-none"
                                tabindex="0"
                            >
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">utm_source</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">utm_campaign</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">utm_medium</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">تاریخ شروع</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">تاریخ پایان</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">ج.تراکنش ها</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">ت.تراکنش ها</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="mb-2 block border-b border-gray-300 py-2 text-sm font-bold">تعداد
                                        کاربران</span>
                                    <div class="flex items-center justify-between px-6 pb-2">
                                        <span class="text-sm">عضو</span>
                                        <span class="px-3 text-gray-300">|</span>
                                        <span class="text-sm">مهمان</span>
                                    </div>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">CR/عضو</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">CR/تراکنش ها</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">کلیک یونیک</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">کلیک ها</span>
                                </td>
                                {{-- <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">ایمپریشن</span>
                                </td> --}}
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">بودجه کمپین</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">تنظیمات</span>
                                </td>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($campaignResults as $campaign)
                                <tr class="h-16 border border-gray-200 hover:bg-gray-100">
                                    <td class="px-2 text-center text-sm">
                                        <a
                                            class="rounded-lg bg-gray-100 px-2 py-0.5"
                                            href="#"
                                        >{{ $campaign->utm_source }}</a>
                                    </td>
                                    <td class="px-2 text-center text-sm">{{ $campaign->utm_campaign }}</td>
                                    <td class="px-2 text-center text-sm">{{ $campaign->utm_medium }}</td>
                                    <td class="px-2 text-center text-sm">{{ shamsiDate($campaign->start_date) }}</td>
                                    <td class="px-2 text-center text-sm">{{ shamsiDate($campaign->end_date) }}</td>
                                    <td class="px-2 text-center text-sm">
                                        <span class="font-bold">
                                            {{ number_format($campaign->sum_transactions) }}</span>
                                        <span class="px-2"></span><span
                                            class="rounded-lg bg-gray-100 px-3 py-0.5 text-xs text-gray-700"
                                        >تومان</span>
                                    </td>
                                    <td class="px-2 text-center text-sm">
                                        {{ number_format($campaign->count_transactions) }} </td>
                                    <td class="bg-gray-50 px-2 text-center text-sm">
                                        <div class="flex items-center justify-between px-6">
                                            <span class="text-sm">
                                                {{ formatMoney($campaign->total_subscribers_user) }}</span>
                                            <span class="px-3 text-gray-300">|</span>
                                            <span class="text-sm">
                                                {{ formatMoney($campaign->total_guest_user) }}</span>
                                        </div>

                                    </td>
                                    <td class="px-2 text-center text-sm">{{ $campaign->cr_per_member }}%</td>
                                    <td class="px-2 text-center text-sm">{{ $campaign->cr_per_transaction }}%</td>
                                    <td class="px-2 text-center text-sm">
                                        {{ number_format($campaign->count_unique_click) }}
                                    </td>
                                    <td class="px-2 text-center text-sm">{{ number_format($campaign->count_click) }}
                                    </td>
                                    {{-- <td class="px-2 text-center text-sm">{{ number_format($campaign->impression) }}
                                    </td> --}}
                                    <td class="px-2 text-center text-sm">
                                        {{ number_format($campaign->campaign_budget) }}</td>
                                    <td class="px-2 text-center text-sm">
                                        <button
                                            class="rounded-xl bg-gray-100 px-4 py-1.5"
                                            type="button"
                                        >
                                            <span class="text-base text-gray-700">...</span>
                                        </button>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

            </div>
        </div>
        <style>
            .checkbox:checked+.check-icon {
                display: flex;
            }
        </style>

    </div>

    <div
        class="overflow-hidden rounded-t-xl"
        x-data="{ fillter: false }"
    >
        <div class="rounded-t-xl bg-gray-800 px-3 py-1.5">
            <div class="flex items-center justify-between">
                <div>
                    <span class="block text-base text-white max-md:text-sm">
                        <span>گزارشات بازدید سایت</span>
                        <span class="rounded-md bg-red-500 px-2 text-sm">{{ formatMoney($reports->total()) }}
                            رکورد</span></span>
                </div>
                <div
                    class="flex items-center gap-4"
                    wire:ignore
                    x-data="playerState()"
                    x-init="checkState()"
                >

                    <div x-show="isPlaying">
                        <span
                            class="text-sm text-gray-500"
                            x-text="countdown"
                        ></span>
                        <span class="text-sm text-gray-500">ثانیه</span>
                    </div>
                    <div
                        class="text-sm font-bold text-gray-100"
                        x-show="isPlaying"
                    >
                        زمان بروزرسانی </div>
                    <button
                        class="flex items-center gap-2 text-white"
                        @click="togglePlay"
                    >
                        <span :class="!isPlaying ? 'text-green-500' : 'text-gray-500'">
                            <svg
                                class="size-6"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M15.75 5.25v13.5m-7.5-13.5v13.5"
                                />
                            </svg>
                        </span>
                        <span :class="isPlaying ? 'text-green-500' : 'text-gray-500'">
                            <svg
                                class="size-6"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z"
                                />
                            </svg>
                        </span>
                    </button>

                    <button
                        class="flex items-center gap-2 p-2"
                        @click="fillter = !fillter"
                    >
                        <svg
                            class="h-6 w-6 text-white"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                d="M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75"
                            />
                        </svg>
                        <span class="text-base text-white">فیلتر</span>
                    </button>

                </div>
            </div>
        </div>
        <form
            class="tranfsition-all transform overflow-hidden bg-gray-800 px-6"
            wire:submit="fillter"
            x-cloak
            :class="fillter ? 'h-auto pt-4' : 'h-0'"
            wire:ignore
        >

            <div class="my-3 grid grid-cols-1 gap-3 md:grid-cols-8">

                <div>
                    <label
                        class="mb-2 block text-sm text-white"
                        for="u_trace"
                    >U Trace:</label>
                    <input
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="u_trace"
                        type="text"
                        wire:model="u_trace"
                    >
                </div>

                <div>
                    <label
                        class="mb-2 block text-sm text-white"
                        for="phone"
                    >Phone:</label>
                    <input
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-center text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="phone"
                        type="text"
                        wire:model="data.phone"
                    >
                </div>
                <div>
                    <label
                        class="mb-2 block text-sm text-white"
                        for="page"
                    >Page:</label>
                    <input
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-center text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="page"
                        type="text"
                        wire:model="data.page"
                    >
                </div>
                <div>
                    <label
                        class="mb-2 block text-sm text-white"
                        for="utm_source"
                    >UTM Source:</label>
                    <input
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-center text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="utm_source"
                        type="text"
                        wire:model="data.utm_source"
                    >
                </div>
                <div>
                    <label
                        class="mb-2 block text-sm text-white"
                        for="utm_medium"
                    >UTM Medium:</label>
                    <input
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-center text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="utm_medium"
                        type="text"
                        wire:model="data.utm_medium"
                    >
                </div>
                <div>
                    <label
                        class="mb-2 block text-sm text-white"
                        for="utm_campaign"
                    >UTM Campaign:</label>
                    <input
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-center text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="utm_campaign"
                        type="text"
                        wire:model="data.utm_campaign"
                    >
                </div>
                <div>
                    <label
                        class="mb-2 block text-sm text-white"
                        for="dateStart"
                    >از تاریخ:</label>
                    <input
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="dateStart"
                        data-jdp
                        type="text"
                        wire:model="dateStart"
                    >
                </div>
                <div>
                    <label
                        class="mb-2 block text-sm text-white"
                        for="dateEnd"
                    >تا تاریخ:</label>
                    <input
                        class="block w-full rounded-lg border-2 border-gray-800 bg-gray-700 p-2 text-sm text-gray-200 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="dateEnd"
                        data-jdp
                        type="text"
                        wire:model="dateEnd"
                    >
                </div>
            </div>

            <div class="flex flex-row-reverse items-center gap-3 py-3">

                <button
                    class="rounded-lg bg-red-500 px-6 py-1 text-white transition-all hover:bg-red-600 disabled:bg-gray-100 disabled:text-gray-400"
                    type="submit"
                >
                    <svg
                        class="inline h-4 w-4 animate-spin text-red-600 dark:text-red-500"
                        role="status"
                        aria-hidden="true"
                        wire:loading
                        wire:target="fillter"
                        viewBox="0 0 100 101"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="#E5E7EB"
                        />
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentColor"
                        />
                    </svg>
                    <span class="text-sm">اعمال فیلتر</span>
                </button>
                <button
                    class="rounded-lg bg-gray-300 px-6 py-1 text-gray-600 transition-all hover:bg-gray-200 disabled:bg-gray-100 disabled:text-gray-400"
                    type="button"
                    wire:click="ClearFillter"
                >
                    <svg
                        class="inline h-4 w-4 animate-spin text-red-600 dark:text-red-500"
                        role="status"
                        aria-hidden="true"
                        wire:loading
                        wire:target="ClearFillter"
                        viewBox="0 0 100 101"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="#E5E7EB"
                        />
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentColor"
                        />
                    </svg>
                    <span class="text-sm">حذف فیلترها</span>
                </button>
            </div>
        </form>
    </div>

    <div class="min-h-96 rounded-b-xl bg-white shadow-md">

        <div class="w-full">

            <div class="relative bg-white">
                @include('layouts.tools.loading')

                <div
                    class="w-full overflow-x-auto"
                    wire:key="{{ now()->timestamp }}"
                >
                    <table class="w-full whitespace-nowrap">
                        <thead class="bg-gray-50">
                            <tr
                                class="mb-3 h-16 rounded border border-gray-200 focus:outline-none"
                                tabindex="0"
                            >
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">U_Trace</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">Phone</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">Page</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">UTM_Source</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">UTM_Medium</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">UTM_Campaign</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">UTM_Term</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">UTM_Content</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">UTM_Yn</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">UTM_Yn_Plt</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">UTM_Yn_Ab</span>
                                </td>
                                <td class="border border-gray-200 px-2 text-center">
                                    <span class="text-sm font-bold">تاریخ</span>
                                </td>

                            </tr>
                        </thead>
                        <tbody>

                            @foreach ($reports as $item)
                                <tr
                                    class="h-16 rounded border border-gray-200 transition-all hover:bg-gray-100 focus:outline-none"
                                    tabindex="0"
                                >
                                    <td
                                        class="justify-center px-2 text-right"
                                        dir="ltr"
                                    >
                                        <a
                                            class="rounded-2xl bg-gray-200 px-2 py-1 text-sm"
                                            href="/dashboard/utm-reports?u_trace={{ $item->u_trace }}"
                                        >
                                            <span title="{{ $item->u_trace }}">
                                                ...{{ \Illuminate\Support\Str::substr($item->u_trace, -6) }}
                                            </span>
                                        </a>
                                        {{-- <span
                                            class="p-.5 h-5 w-5 rounded-full bg-gray-900 text-white">{{ $item->u_trace_count ?? 1 }}</span> --}}
                                    </td>
                                    <td class="justify-center px-4 text-right">
                                        @if ($item->type != 'guest')
                                            <span class="text-sm">{{ $item->phone }}</span>
                                        @else
                                            <span class="text-sm text-green-500">مهمان</span>
                                        @endif

                                    </td>
                                    <td
                                        class="justify-center px-2 text-left"
                                        dir="ltr"
                                    >
                                        <a
                                            class="rounded-2xl bg-gray-200 px-2 py-1 text-sm"
                                            href="{{ $item->page }}"
                                            target="_blank"
                                        >
                                            <span title="{{ $item->page }}">
                                                {{ Str::limit($item->page, 30) }}
                                            </span>
                                        </a>
                                    </td>

                                    <td class="justify-center px-2 text-center">
                                        <span
                                            class="rounded-2xl bg-gray-100 px-2 py-1 text-sm">{{ $item->cookie_data['utm_source'] ?? '—' }}</span>
                                    </td>
                                    <td class="justify-center px-2 text-center">
                                        <span class="text-sm">{{ $item->cookie_data['utm_medium'] ?? '—' }}</span>
                                    </td>
                                    <td class="justify-center px-2 text-center">
                                        <span class="text-sm">{{ $item->cookie_data['utm_campaign'] ?? '—' }}</span>
                                    </td>
                                    <td class="justify-center px-2 text-center">
                                        <span class="text-sm">{{ $item->cookie_data['utm_term'] ?? '—' }}</span>
                                    </td>
                                    <td class="justify-center px-2 text-center">
                                        <span class="text-sm">{{ $item->cookie_data['utm_content'] ?? '—' }}</span>
                                    </td>
                                    <td class="justify-center px-2 text-center">
                                        <span title="{{ $item->cookie_data['utm_yn'] ?? '—' }}">
                                            {{ isset($item->cookie_data['utm_yn']) ? Str::limit($item->cookie_data['utm_yn'], 5) : '—' }}

                                        </span>
                                    </td>
                                    <td class="justify-center px-2 text-center">

                                        <span title="{{ $item->cookie_data['utm_yn_plt'] ?? '—' }}">
                                            {{ isset($item->cookie_data['utm_yn_plt']) ? Str::limit($item->cookie_data['utm_yn_plt'], 40) : '—' }}

                                        </span>
                                    </td>
                                    <td class="justify-center px-2 text-center">
                                        <span title="{{ $item->cookie_data['utm_yn_ab'] ?? '—' }}">
                                            {{ isset($item->cookie_data['utm_yn_ab']) ? Str::limit($item->cookie_data['utm_yn_ab'], 40) : '—' }}

                                        </span>

                                    </td>
                                    <td class="justify-center px-2 text-center">
                                        <span class="text-sm">{{ shamsiDate($item->created_at) }}</span>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <div
                    class="w-full bg-gray-100 p-3"
                    wire:loading.remove
                >
                    {{ $reports->links(data: ['dark' => false]) }}
                </div>
            </div>
        </div>
        <style>
            .checkbox:checked+.check-icon {
                display: flex;
            }
        </style>

    </div>

</div>

@push('script')
    <script wire:ignore>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if we have data first
            const campaignData = @json($campaignResults);

            if (!campaignData || campaignData.length === 0) {
                console.warn('No campaign data available for charts');
                // Hide charts section if no data
                const chartsContainer = document.getElementById('chartsContainer');
                if (chartsContainer) {
                    chartsContainer.style.display = 'none';
                }
                return;
            }

            // Wait for Chart.js to load
            let attempts = 0;
            const maxAttempts = 10;

            function tryInitCharts() {
                attempts++;
                if (typeof Chart !== 'undefined') {
                    initUTMCharts();
                } else if (attempts < maxAttempts) {
                    setTimeout(tryInitCharts, 500);
                } else {
                    console.error('Chart.js failed to load after multiple attempts');
                }
            }

            tryInitCharts();
        });

        function initUTMCharts() {
            const campaignData = @json($campaignResults);
            console.log('Initializing charts with data:', campaignData);

            // Campaign Performance Chart (Bar Chart)
            const campaignLabels = campaignData.map(campaign => campaign.utm_campaign || 'نامشخص');
            const campaignAmounts = campaignData.map(campaign => campaign.sum_transactions || 0);

            const ctx1 = document.getElementById('campaignPerformanceChart').getContext('2d');
            new Chart(ctx1, {
                type: 'bar',
                data: {
                    labels: campaignLabels,
                    datasets: [{
                        label: 'مبلغ تراکنش (تومان)',
                        data: campaignAmounts,
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(239, 68, 68, 0.8)',
                            'rgba(139, 92, 246, 0.8)',
                            'rgba(236, 72, 153, 0.8)'
                        ],
                        borderColor: [
                            'rgba(59, 130, 246, 1)',
                            'rgba(16, 185, 129, 1)',
                            'rgba(245, 158, 11, 1)',
                            'rgba(239, 68, 68, 1)',
                            'rgba(139, 92, 246, 1)',
                            'rgba(236, 72, 153, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: true,
                    aspectRatio: 2,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return new Intl.NumberFormat('fa-IR').format(value) + ' تومان';
                                }
                            }
                        }
                    }
                }
            });

            // Conversion Rate Chart (Line Chart)
            const crPerMember = campaignData.map(campaign => campaign.cr_per_member || 0);
            const crPerTransaction = campaignData.map(campaign => campaign.cr_per_transaction || 0);

            const ctx2 = document.getElementById('conversionRateChart').getContext('2d');
            new Chart(ctx2, {
                type: 'line',
                data: {
                    labels: campaignLabels,
                    datasets: [{
                        label: 'CR/عضو (%)',
                        data: crPerMember,
                        borderColor: 'rgba(59, 130, 246, 1)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        fill: false,
                        tension: 0.3
                    }, {
                        label: 'CR/تراکنش (%)',
                        data: crPerTransaction,
                        borderColor: 'rgba(16, 185, 129, 1)',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        fill: false,
                        tension: 0.3
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: true,
                    aspectRatio: 2,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    }
                }
            });

            // User Distribution Chart (Doughnut Chart)
            const totalSubscribers = campaignData.reduce((sum, campaign) => sum + (campaign
                .total_subscribers_user || 0), 0);
            const totalGuests = campaignData.reduce((sum, campaign) => sum + (campaign.total_guest_user || 0), 0);

            const ctx3 = document.getElementById('userDistributionChart').getContext('2d');
            new Chart(ctx3, {
                type: 'doughnut',
                data: {
                    labels: ['کاربران عضو', 'کاربران مهمان'],
                    datasets: [{
                        data: [totalSubscribers, totalGuests],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)'
                        ],
                        borderColor: [
                            'rgba(59, 130, 246, 1)',
                            'rgba(16, 185, 129, 1)'
                        ],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: true,
                    aspectRatio: 1,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // Click Performance Chart (Bar Chart)
            const uniqueClicks = campaignData.map(campaign => campaign.count_unique_click || 0);
            const totalClicks = campaignData.map(campaign => campaign.count_click || 0);

            const ctx4 = document.getElementById('clickPerformanceChart').getContext('2d');
            new Chart(ctx4, {
                type: 'bar',
                data: {
                    labels: campaignLabels,
                    datasets: [{
                        label: 'کلیک یونیک',
                        data: uniqueClicks,
                        backgroundColor: 'rgba(245, 158, 11, 0.8)',
                        borderColor: 'rgba(245, 158, 11, 1)',
                        borderWidth: 1
                    }, {
                        label: 'کل کلیک‌ها',
                        data: totalClicks,
                        backgroundColor: 'rgba(239, 68, 68, 0.8)',
                        borderColor: 'rgba(239, 68, 68, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: true,
                    aspectRatio: 2,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return new Intl.NumberFormat('fa-IR').format(value);
                                }
                            }
                        }
                    }
                }
            });

            // Budget vs Revenue Chart (Mixed Chart)
            const budgetData = campaignData.map(campaign => campaign.campaign_budget || 0);
            const revenueData = campaignData.map(campaign => campaign.sum_transactions || 0);

            const ctx5 = document.getElementById('budgetRevenueChart').getContext('2d');
            new Chart(ctx5, {
                type: 'bar',
                data: {
                    labels: campaignLabels,
                    datasets: [{
                        label: 'بودجه کمپین (تومان)',
                        data: budgetData,
                        backgroundColor: 'rgba(239, 68, 68, 0.6)',
                        borderColor: 'rgba(239, 68, 68, 1)',
                        borderWidth: 1,
                        yAxisID: 'y'
                    }, {
                        label: 'درآمد واقعی (تومان)',
                        data: revenueData,
                        backgroundColor: 'rgba(16, 185, 129, 0.6)',
                        borderColor: 'rgba(16, 185, 129, 1)',
                        borderWidth: 1,
                        yAxisID: 'y'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: true,
                    aspectRatio: 1.8,
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    label += new Intl.NumberFormat('fa-IR').format(context.parsed.y) +
                                        ' تومان';
                                    return label;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return new Intl.NumberFormat('fa-IR').format(value) + ' تومان';
                                }
                            }
                        }
                    }
                }
            });

            // ROI Chart (Bar Chart)
            const roiData = campaignData.map(campaign => {
                const budget = campaign.campaign_budget || 0;
                const revenue = campaign.sum_transactions || 0;
                if (budget === 0) return 0;
                return ((revenue - budget) / budget * 100).toFixed(2);
            });

            const ctx6 = document.getElementById('roiChart').getContext('2d');
            new Chart(ctx6, {
                type: 'bar',
                data: {
                    labels: campaignLabels,
                    datasets: [{
                        label: 'ROI (%)',
                        data: roiData,
                        backgroundColor: roiData.map(roi => roi >= 0 ? 'rgba(16, 185, 129, 0.8)' :
                            'rgba(239, 68, 68, 0.8)'),
                        borderColor: roiData.map(roi => roi >= 0 ? 'rgba(16, 185, 129, 1)' :
                            'rgba(239, 68, 68, 1)'),
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: true,
                    aspectRatio: 2,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return 'ROI: ' + context.parsed.y + '%';
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    }
                }
            });

            console.log('All charts initialized successfully!');
        }

        // Toggle charts visibility
        function toggleCharts() {
            const container = document.getElementById('chartsContainer');
            const button = document.getElementById('chartsToggle');

            if (container.style.display === 'none') {
                container.style.display = 'grid';
                button.textContent = 'مخفی کردن چارت‌ها';
            } else {
                container.style.display = 'none';
                button.textContent = 'نمایش چارت‌ها';
            }
        }
    </script>
@endpush
