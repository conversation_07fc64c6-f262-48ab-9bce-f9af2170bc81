<div class="relative mx-auto max-h-dvh w-full max-w-md overflow-y-auto rounded-xl bg-white shadow-xl">
    <!-- Header ثابت -->
    <div
        class="mb-3 flex justify-between p-5 dark:border-gray-800"
        wire:ignore
    >
        <div class="flex items-center gap-2">
            <span class="flex h-8 w-8 items-center justify-center rounded-full p-1 text-green-600 dark:bg-gray-900">
                <svg
                    class="size-6"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                >
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75"
                    />
                </svg>

            </span>
            <h1 class="text-base font-bold text-gray-800 dark:font-normal dark:text-gray-100 md:text-xl">ویرایش سوال
                متداول</h1>
        </div>
        <button
            class="items-right mr-auto inline-flex h-8 w-8 rounded-lg bg-transparent p-1.5 text-sm transition-all hover:bg-gray-200 hover:text-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 dark:hover:text-white"
            type="button"
            x-on:click="setShowPropertyTo(false)"
        >
            <svg
                class="h-5 w-5"
                aria-hidden="true"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
            >
                <path
                    fill-rule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clip-rule="evenodd"
                ></path>
            </svg>
            <span class="sr-only">Close modal</span>
        </button>
    </div>

    <!-- محتوای اسکرول شونده -->
    <form
        class="px-3 !pt-0 md:p-5"
        wire:submit="update"
    >

        <div class="flex flex-col gap-2 text-right">
            <div class="mb-8">
                <label
                    class="mb-2 block text-sm text-gray-700 dark:text-white"
                    for="question"
                >عنوان سوال:</label>
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2.5 text-sm text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="question"
                    type="text"
                    wire:model="question"
                >
                @error('question')
                    <span class="text-sm text-red-500">{{ $message }}</span>
                @enderror
            </div>

            <div>
                <label
                    class="mb-2 block text-sm text-gray-700 dark:text-white"
                    for="answer"
                >پاسخ:</label>
                <textarea
                    class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2.5 text-sm text-gray-900 focus:border-red-500 focus:ring-red-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-500 dark:focus:ring-red-500"
                    id="answer"
                    rows="8"
                    wire:model="answer"
                ></textarea>
                @error('answer')
                    <span class="text-sm text-red-500">{{ $message }}</span>
                @enderror
            </div>
            <button
                class="my-4 rounded-lg bg-red-500 px-6 py-3 text-white transition-all hover:bg-red-600 disabled:bg-gray-100 disabled:text-gray-400"
                type="submit"
                wire:target="changeBalanceWallet"
                wire:loading.attr="disabled"
            >
                <span class="flex items-center justify-center gap-2">
                    <svg
                        class="inline h-4 w-4 animate-spin text-red-600 dark:text-red-500"
                        role="status"
                        aria-hidden="true"
                        wire:loading
                        wire:target="update"
                        viewBox="0 0 100 101"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="#E5E7EB"
                        />
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentColor"
                        />
                    </svg>
                    <span class="text-sm">بروزرسانی اطلاعات</span>
                </span>
            </button>
    </form>
</div>
