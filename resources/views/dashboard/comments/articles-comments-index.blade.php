@extends('layouts.dashboard')

@section('title', '- مرور و مدیریت نظرات مقالات')

@section('content')
    <main>

        <div class="mb-16 max-md:px-3">
            <div class="relative">
                <div class="mb-3 gap-3 max-md:flex max-md:items-center max-md:overflow-x-auto md:grid md:grid-cols-4">

                    <div
                        class="relative w-full shrink-0 overflow-hidden border-b-2 border-b-blue-500 bg-white p-6 shadow-md">
                        <div class="flex flex-col gap-2">
                            <span class="text-2xl font-bold">
                                {{ formatMoney(\App\Models\Comment::whereIn('commentable_type', ['App\Models\Content\Article', 'App\Models\Article'])->whereNull('parent_id')->count()) }}
                            </span>
                            <p class="text-base text-gray-500">تعداد کل نظرات مقالات و سرویس ها</p>
                        </div>

                        <svg
                            class="absolute left-4 top-4 size-16 text-gray-200"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                d="M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z"
                            />
                        </svg>

                    </div>

                    <div
                        class="relative w-full shrink-0 overflow-hidden border-b-2 border-b-yellow-300 bg-white p-6 shadow-md">
                        <div class="flex flex-col gap-2">
                            <span class="text-2xl font-bold">
                                {{ formatMoney(\App\Models\Comment::whereIn('commentable_type', ['App\Models\Content\Article', 'App\Models\Article'])->whereNull('parent_id')->where('status', \App\Enums\StatusEnum::PENDING->value)->count()) }}

                            </span>
                            <p class="text-base">تعداد نظرات درحال بررسی</p>
                        </div>

                        <svg
                            class="absolute left-4 top-4 size-16 text-gray-200"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                d="M2.25 12.76c0 1.6 1.123 2.994 2.707 3.227 1.087.16 2.185.283 3.293.369V21l4.076-4.076a1.526 1.526 0 0 1 1.037-.443 48.282 48.282 0 0 0 5.68-.494c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z"
                            />
                        </svg>

                    </div>

                    <div
                        class="relative w-full shrink-0 overflow-hidden border-b-2 border-b-red-500 bg-white p-6 shadow-md">
                        <div class="flex flex-col gap-2">
                            <span class="text-2xl font-bold">
                                {{ formatMoney(\App\Models\Comment::whereIn('commentable_type', ['App\Models\Content\Article', 'App\Models\Article'])->whereNull('parent_id')->where('status', \App\Enums\StatusEnum::REJECTED->value)->count()) }}

                            </span>
                            <p class="text-base text-red-500">تعداد نظرات رد شده</p>
                        </div>
                        <svg
                            class="absolute left-4 top-4 size-16 text-gray-200"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                d="M6 18 18 6M6 6l12 12"
                            />
                        </svg>

                    </div>

                    <div
                        class="relative w-full shrink-0 overflow-hidden border-b-2 border-green-600 bg-white p-6 shadow-md">
                        <div class="flex flex-col gap-2">
                            <span class="text-2xl font-bold">
                                {{ formatMoney(\App\Models\Comment::whereIn('commentable_type', ['App\Models\Content\Article', 'App\Models\Article'])->whereNull('parent_id')->where('status', \App\Enums\StatusEnum::CONFIRMED->value)->count()) }}

                            </span>
                            <p class="text-base text-green-500">تعداد نظرات منتشر شده</p>
                        </div>
                        <svg
                            class="absolute left-4 top-4 size-16 text-gray-200"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                d="m4.5 12.75 6 6 9-13.5"
                            />
                        </svg>

                    </div>

                </div>
                <livewire:dashboard.comments.comments-articles-index lazy />
            </div>
        </div>

    </main>

@endsection
