@extends('dashboard.layouts.app')

@section('title', 'مدیریت سایت‌مپ')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">مدیریت سایت‌مپ</h3>
                </div>
                <div class="card-body">
                    
                    <!-- Action Buttons -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <button type="button" class="btn btn-primary" id="generateSitemap">
                                <i class="fas fa-sync"></i> تولید فایل‌های سایت‌مپ
                            </button>
                            <button type="button" class="btn btn-success" id="uploadFtp">
                                <i class="fas fa-upload"></i> آپلود به FTP
                            </button>
                            <button type="button" class="btn btn-info" id="testFtp">
                                <i class="fas fa-network-wired"></i> تست اتصال FTP
                            </button>
                        </div>
                    </div>

                    <!-- Sitemap Files Status -->
                    <div class="row">
                        <div class="col-md-12">
                            <h5>وضعیت فایل‌های سایت‌مپ</h5>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>نام فایل</th>
                                            <th>وضعیت</th>
                                            <th>اندازه</th>
                                            <th>آخرین بروزرسانی</th>
                                            <th>عملیات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($sitemapFiles as $filename => $info)
                                        <tr>
                                            <td>{{ $filename }}</td>
                                            <td>
                                                @if($info['exists'])
                                                    <span class="badge badge-success">موجود</span>
                                                @else
                                                    <span class="badge badge-danger">وجود ندارد</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($info['exists'])
                                                    {{ number_format($info['size'] / 1024, 2) }} KB
                                                @else
                                                    -
                                                @endif
                                            </td>
                                            <td>
                                                @if($info['exists'] && $info['modified'])
                                                    {{ \Carbon\Carbon::createFromTimestamp($info['modified'])->format('Y-m-d H:i:s') }}
                                                @else
                                                    -
                                                @endif
                                            </td>
                                            <td>
                                                @if($info['exists'])
                                                    <a href="{{ route('sitemap.view', basename($filename)) }}" 
                                                       class="btn btn-sm btn-info" target="_blank">
                                                        <i class="fas fa-eye"></i> مشاهده
                                                    </a>
                                                    <a href="{{ route('sitemap.download', basename($filename)) }}" 
                                                       class="btn btn-sm btn-secondary">
                                                        <i class="fas fa-download"></i> دانلود
                                                    </a>
                                                    <a href="{{ $info['url'] }}" 
                                                       class="btn btn-sm btn-primary" target="_blank">
                                                        <i class="fas fa-external-link-alt"></i> لینک عمومی
                                                    </a>
                                                @else
                                                    <span class="text-muted">فایل وجود ندارد</span>
                                                @endif
                                            </td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Information -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> اطلاعات مهم:</h6>
                                <ul class="mb-0">
                                    <li>فایل‌های سایت‌مپ هر ساعت به صورت خودکار بروزرسانی می‌شوند</li>
                                    <li>فایل sitemap.xml اصلی شامل لینک به فایل‌های articles.xml و services.xml است</li>
                                    <li>برای فعال‌سازی آپلود FTP، متغیرهای محیطی مربوطه را در فایل .env تنظیم کنید</li>
                                    <li>فایل‌ها در پوشه public و public/sitemaps ذخیره می‌شوند</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border" role="status">
                    <span class="sr-only">در حال پردازش...</span>
                </div>
                <p class="mt-2">در حال پردازش...</p>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    
    // Generate Sitemap
    $('#generateSitemap').click(function() {
        $('#loadingModal').modal('show');
        
        $.ajax({
            url: '{{ route("sitemap.generate") }}',
            type: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                $('#loadingModal').modal('hide');
                if (response.success) {
                    toastr.success(response.message);
                    setTimeout(function() {
                        location.reload();
                    }, 2000);
                } else {
                    toastr.error(response.message);
                }
            },
            error: function(xhr) {
                $('#loadingModal').modal('hide');
                toastr.error('خطا در تولید فایل‌های سایت‌مپ');
            }
        });
    });

    // Upload to FTP
    $('#uploadFtp').click(function() {
        $('#loadingModal').modal('show');
        
        $.ajax({
            url: '{{ route("sitemap.upload-ftp") }}',
            type: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                $('#loadingModal').modal('hide');
                if (response.success) {
                    toastr.success(response.message);
                } else {
                    toastr.error(response.message);
                }
            },
            error: function(xhr) {
                $('#loadingModal').modal('hide');
                let message = xhr.responseJSON?.message || 'خطا در آپلود فایل‌ها';
                toastr.error(message);
            }
        });
    });

    // Test FTP Connection
    $('#testFtp').click(function() {
        $('#loadingModal').modal('show');
        
        $.ajax({
            url: '{{ route("sitemap.test-ftp") }}',
            type: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                $('#loadingModal').modal('hide');
                if (response.success) {
                    toastr.success(response.message);
                } else {
                    toastr.error(response.message);
                }
            },
            error: function(xhr) {
                $('#loadingModal').modal('hide');
                let message = xhr.responseJSON?.message || 'خطا در تست اتصال FTP';
                toastr.error(message);
            }
        });
    });
    
});
</script>
@endpush
