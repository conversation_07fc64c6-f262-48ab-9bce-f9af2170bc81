<?php

namespace Database\Seeders;

use App\Models\Permission;
use App\Models\Role;
use Illuminate\Database\Seeder;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $roles = [
            [
                'name' => 'super_admin',
                'label' => 'مدیر اصلی',
                'level' => 'admin',
            ],
            [
                'name' => 'writer',
                'label' => 'نویسنده',
                'level' => 'admin',
            ],
            [
                'name' => 'accountant',
                'label' => 'حسابدار',
                'level' => 'admin',
            ],
            [
                'name' => 'support',
                'label' => 'پشتیبان',
                'level' => 'admin',
            ],
        ];

        foreach ($roles as $roleData) {
            $role = Role::create([
                'name' => $roleData['name'],
                'label' => $roleData['label'],
                'level' => $roleData['level'],
            ]);

            Permission::create([
                'name' => $roleData['name'],
                'label' => $roleData['label'],
            ]);
        }
    }
}
