<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تست چارت‌های UTM</title>
    <script src="/assets/js/chart.js"></script>
    <style>
        body {
            font-family: 'Tahoma', sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .chart-container {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h3 {
            color: #333;
            margin-bottom: 20px;
        }
        canvas {
            max-height: 400px;
        }
    </style>
</head>
<body>
    <h1>تست چارت‌های گزارش UTM</h1>
    
    <div class="chart-container">
        <h3>عملکرد کمپین‌ها (مبلغ تراکنش)</h3>
        <canvas id="campaignPerformanceChart"></canvas>
    </div>

    <div class="chart-container">
        <h3>نرخ تبدیل (CR) کمپین‌ها</h3>
        <canvas id="conversionRateChart"></canvas>
    </div>

    <div class="chart-container">
        <h3>توزیع کاربران (عضو/مهمان)</h3>
        <canvas id="userDistributionChart"></canvas>
    </div>

    <script>
        // Sample data for testing
        const campaignData = [
            {
                utm_campaign: 'کمپین تابستانی',
                sum_transactions: 15000000,
                count_transactions: 45,
                total_subscribers_user: 120,
                total_guest_user: 80,
                cr_per_member: 12.5,
                cr_per_transaction: 8.3,
                count_unique_click: 1500,
                count_click: 2200,
                campaign_budget: 12000000
            },
            {
                utm_campaign: 'کمپین پاییزی',
                sum_transactions: 22000000,
                count_transactions: 67,
                total_subscribers_user: 180,
                total_guest_user: 95,
                cr_per_member: 15.2,
                cr_per_transaction: 11.1,
                count_unique_click: 2100,
                count_click: 3200,
                campaign_budget: 18000000
            },
            {
                utm_campaign: 'کمپین زمستانی',
                sum_transactions: 8500000,
                count_transactions: 28,
                total_subscribers_user: 75,
                total_guest_user: 45,
                cr_per_member: 9.8,
                cr_per_transaction: 6.2,
                count_unique_click: 900,
                count_click: 1400,
                campaign_budget: 10000000
            }
        ];

        window.addEventListener('load', function() {
            setTimeout(initCharts, 500);
        });

        function initCharts() {
            if (typeof Chart === 'undefined') {
                console.error('Chart.js is not loaded');
                return;
            }

            console.log('Initializing charts with sample data:', campaignData);

            // Campaign Performance Chart
            const campaignLabels = campaignData.map(campaign => campaign.utm_campaign);
            const campaignAmounts = campaignData.map(campaign => campaign.sum_transactions);
            
            const ctx1 = document.getElementById('campaignPerformanceChart').getContext('2d');
            new Chart(ctx1, {
                type: 'bar',
                data: {
                    labels: campaignLabels,
                    datasets: [{
                        label: 'مبلغ تراکنش (تومان)',
                        data: campaignAmounts,
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)'
                        ],
                        borderColor: [
                            'rgba(59, 130, 246, 1)',
                            'rgba(16, 185, 129, 1)',
                            'rgba(245, 158, 11, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return new Intl.NumberFormat('fa-IR').format(value) + ' تومان';
                                }
                            }
                        }
                    }
                }
            });

            // Conversion Rate Chart
            const crPerMember = campaignData.map(campaign => campaign.cr_per_member);
            const crPerTransaction = campaignData.map(campaign => campaign.cr_per_transaction);
            
            const ctx2 = document.getElementById('conversionRateChart').getContext('2d');
            new Chart(ctx2, {
                type: 'line',
                data: {
                    labels: campaignLabels,
                    datasets: [{
                        label: 'CR/عضو (%)',
                        data: crPerMember,
                        borderColor: 'rgba(59, 130, 246, 1)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        fill: false,
                        tension: 0.3
                    }, {
                        label: 'CR/تراکنش (%)',
                        data: crPerTransaction,
                        borderColor: 'rgba(16, 185, 129, 1)',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        fill: false,
                        tension: 0.3
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    }
                }
            });

            // User Distribution Chart
            const totalSubscribers = campaignData.reduce((sum, campaign) => sum + campaign.total_subscribers_user, 0);
            const totalGuests = campaignData.reduce((sum, campaign) => sum + campaign.total_guest_user, 0);
            
            const ctx3 = document.getElementById('userDistributionChart').getContext('2d');
            new Chart(ctx3, {
                type: 'doughnut',
                data: {
                    labels: ['کاربران عضو', 'کاربران مهمان'],
                    datasets: [{
                        data: [totalSubscribers, totalGuests],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)'
                        ],
                        borderColor: [
                            'rgba(59, 130, 246, 1)',
                            'rgba(16, 185, 129, 1)'
                        ],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            console.log('Charts initialized successfully!');
        }
    </script>
</body>
</html>
