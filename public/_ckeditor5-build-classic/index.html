<!DOCTYPE html>
<!--
Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
-->
<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
	<link type="text/css" href="sample/css/sample.css" rel="stylesheet" media="screen" />
	<title>CKEditor 5 – classic editor build sample</title>
</head>
<body>

<header>
	<div class="centered">
		<h1><a href="https://ckeditor.com/ckeditor-5"><img src="sample/img/logo.svg" alt="WYSIWYG editor - CKEditor 5" /></a></h1>

		<input type="checkbox" id="menu-toggle" />
		<label for="menu-toggle"></label>
		  
		<nav>
			<ul>
				<li><a href="https://ckeditor.com/ckeditor-5">Project homepage</a></li>
				<li><a href="https://ckeditor.com/docs/">Documentation</a></li>
				<li><a href="https://github.com/ckeditor/ckeditor5">GitHub</a></li>
			</ul>
		</nav>
	</div>
</header>

<main>
	<div class="message">
		<div class="centered">
			<h1>Congratulations!</h1>

			<p>If you can see CKEditor below, it means that the installation succeeded. You can now try out your new editor version, see its features,
			and check some of the most useful <a href="#references">resources recommended below</a>.</p>
		</div>
	</div>
	<div class="centered">
		<div id="editor">
			<h2>The three greatest things you learn from traveling</h2>

			<p>Like all the great things on earth traveling teaches us by example. Here are some of the most precious lessons I’ve learned over the years of traveling.</p>

			<h3>Appreciation of diversity</h3>

			<p>Getting used to an entirely different culture can be challenging. While it’s also nice to learn about cultures online or from books, nothing comes close to experiencing <a href="https://en.wikipedia.org/wiki/Cultural_diversity">cultural diversity</a> in person. You learn to appreciate each and every single one of the differences while you become more culturally fluid.</p>

			<figure class="image image-style-side"><img src="sample/img/umbrellas.jpg" alt="Three Monks walking on ancient temple.">
				<figcaption>Leaving your comfort zone might lead you to such beautiful sceneries like this one.</figcaption>
			</figure>

			<h3>Confidence</h3>

			<p>Going to a new place can be quite terrifying. While change and uncertainty makes us scared, traveling teaches us how ridiculous it is to be afraid of something before it happens. The moment you face your fear and see there was nothing to be afraid of, is the moment you discover bliss.</p>
		</div>

		<div id="references">
			<section>
				<h2>Configure the editor</h2>
				<p>CKEditor 5 is configurable so you can change many of its aspects (like the <a href="https://ckeditor.com/docs/ckeditor5/latest/builds/guides/integration/configuration.html#toolbar-setup">toolbar</a>) to get most of the editor in your project.</p>
				<p><a href="https://ckeditor.com/docs/ckeditor5/latest/builds/guides/integration/configuration.html">Learn how to configure</a></p>
			</section>

			<section>
				<h2>Discover the features</h2>
				<p>CKEditor 5 comes with plenty of rich text editing features. Most of them are available out of the box in your build.</p>
				<p><a href="https://ckeditor.com/docs/ckeditor5/latest/features/index.html">Discover rich text editor features</a></p>				</section>

			<section>
				<h2>Discover editor builds</h2>
				<p>There are other editor builds you can use in your project. They offer a different user interface and features but they all share the same solid core of CKEditor 5.</p>
				<p><a href="https://ckeditor.com/docs/ckeditor5/latest/builds/guides/overview.html">Discover the builds</a></p>
			</section>

			<section>
				<h2>Real-time collaboration</h2>
				<p>CKEditor 5 Collaboration Features let you customize any CKEditor 5 build to include real-time collaborative editing and commenting features and tailor them to your needs.</p>
				<p><a href="https://ckeditor.com/docs/ckeditor5/latest/features/collaboration/collaboration.html">Real-time collaboration overview</a></p>
			</section>

			<section>
				<h2>Create your own rich text editor</h2>
				<p>CKEditor 5 is a rich text editing framework that allows you to create your own editor using the building blocks it offers. You can customize existing builds or create a new one from scratch.</p>
				<p><a href="https://ckeditor.com/docs/ckeditor5/latest/builds/guides/development/custom-builds.html">Create your own build</a></p>
			</section>

			<section>
				<h2>Integration with the frameworks</h2>
				<p>CKEditor 5 supports the most popular web frameworks like React, Angular or Vue.js &mdash; get the full benefit of CKEditor 5 in your project using official integrations.</p>
				<p><a href="https://ckeditor.com/docs/ckeditor5/latest/builds/guides/integration/frameworks/overview.html">Browse the integrations</a></p>
			</section>	
		</div>
	</div>
</main>

<footer>
	<div>
		<p>CKEditor 5 – The text editor for the Internet – <a href="https://ckeditor.com/ckeditor-5">https://ckeditor.com/ckeditor-5</a></p>
		<p>Copyright © 2003-2023, <a href="https://cksource.com/">CKSource</a> – Frederico Knabben. All rights reserved.</p>
	</div>
</footer>

<script src="ckeditor.js"></script>

<script>
	ClassicEditor
		.create( document.querySelector( '#editor' ), {
			// toolbar: [ 'heading', '|', 'bold', 'italic', 'link' ]
		} )
		.then( editor => {
			window.editor = editor;
		} )
		.catch( err => {
			console.error( err.stack );
		} );
</script>

</body>
</html>
