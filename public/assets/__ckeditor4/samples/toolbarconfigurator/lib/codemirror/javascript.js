﻿(function(t){"object"==typeof exports&&"object"==typeof module?t(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define(["../../lib/codemirror"],t):t(CodeMirror)})(function(t){t.defineMode("javascript",function(Qa,x){var m,u,e,Z,L;function r(a,c,b){T=a;aa=b;return c}function F(a,c){var b=a.next();if('"'==b||"'"==b)return c.tokenize=Ra(b),c.tokenize(a,c);if("."==b&&a.match(/^\d[\d_]*(?:[eE][+\-]?[\d_]+)?/))return r("number","number");if("."==b&&a.match(".."))return r("spread",
"meta");if(/[\[\]{}\(\),;\:\.]/.test(b))return r(b);if("\x3d"==b&&a.eat("\x3e"))return r("\x3d\x3e","operator");if("0"==b&&a.match(/^(?:x[\dA-Fa-f_]+|o[0-7_]+|b[01_]+)n?/))return r("number","number");if(/\d/.test(b))return a.match(/^[\d_]*(?:n|(?:\.[\d_]*)?(?:[eE][+\-]?[\d_]+)?)?/),r("number","number");if("/"==b){if(a.eat("*"))return c.tokenize=ba,ba(a,c);if(a.eat("/"))return a.skipToEnd(),r("comment","comment");if(wa(a,c,1)){a:for(var b=!1,d,e=!1;null!=(d=a.next());){if(!b){if("/"==d&&!e)break a;
"["==d?e=!0:e&&"]"==d&&(e=!1)}b=!b&&"\\"==d}a.match(/^\b(([gimyus])(?![gimyus]*\2))+\b/);return r("regexp","string-2")}a.eat("\x3d");return r("operator","operator",a.current())}if("`"==b)return c.tokenize=U,U(a,c);if("#"==b&&"!"==a.peek())return a.skipToEnd(),r("meta","meta");if("#"==b&&a.eatWhile(ca))return r("variable","property");if("\x3c"==b&&a.match("!--")||"-"==b&&a.match("-\x3e")&&!/\S/.test(a.string.slice(0,a.start)))return a.skipToEnd(),r("comment","comment");if(xa.test(b))return"\x3e"==
b&&c.lexical&&"\x3e"==c.lexical.type||(a.eat("\x3d")?"!"!=b&&"\x3d"!=b||a.eat("\x3d"):/[<>*+\-|&?]/.test(b)&&(a.eat(b),"\x3e"==b&&a.eat(b))),"?"==b&&a.eat(".")?r("."):r("operator","operator",a.current());if(ca.test(b)){a.eatWhile(ca);b=a.current();if("."!=c.lastType){if(ya.propertyIsEnumerable(b))return d=ya[b],r(d.type,d.style,b);if("async"==b&&a.match(/^(\s|\/\*([^*]|\*(?!\/))*?\*\/)*[\[\(\w]/,!1))return r("async","keyword",b)}return r("variable","variable",b)}}function Ra(a){return function(c,
b){var d=!1,e;if(da&&"@"==c.peek()&&c.match(Sa))return b.tokenize=F,r("jsonld-keyword","meta");for(;null!=(e=c.next())&&(e!=a||d);)d=!d&&"\\"==e;d||(b.tokenize=F);return r("string","string")}}function ba(a,c){for(var b=!1,d;d=a.next();){if("/"==d&&b){c.tokenize=F;break}b="*"==d}return r("comment","comment")}function U(a,c){for(var b=!1,d;null!=(d=a.next());){if(!b&&("`"==d||"$"==d&&a.eat("{"))){c.tokenize=F;break}b=!b&&"\\"==d}return r("quasi","string-2",a.current())}function oa(a,c){c.fatArrowAt&&
(c.fatArrowAt=null);var b=a.string.indexOf("\x3d\x3e",a.start);if(!(0>b)){if(p){var d=/:\s*(?:\w+(?:<[^>]*>|\[\])?|\{[^}]*\})\s*$/.exec(a.string.slice(a.start,b));d&&(b=d.index)}for(var d=0,e=!1,b=b-1;0<=b;--b){var h=a.string.charAt(b),g="([{}])".indexOf(h);if(0<=g&&3>g){if(!d){++b;break}if(0==--d){"("==h&&(e=!0);break}}else if(3<=g&&6>g)++d;else if(ca.test(h))e=!0;else if(/["'\/`]/.test(h))for(;;--b){if(0==b)return;if(a.string.charAt(b-1)==h&&"\\"!=a.string.charAt(b-2)){b--;break}}else if(e&&!d){++b;
break}}e&&!d&&(c.fatArrowAt=b)}}function za(a,c,b,d,e,h){this.indented=a;this.column=c;this.type=b;this.prev=e;this.info=h;null!=d&&(this.align=d)}function Aa(a,b,f,d,h){var g=a.cc;m=a;u=h;e=null;Z=g;L=b;a.lexical.hasOwnProperty("align")||(a.lexical.align=!0);for(;;)if((g.length?g.pop():G?q:v)(f,d)){for(;g.length&&g[g.length-1].lex;)g.pop()();if(e)return e;if(f="variable"==f)a:if(Ba){for(f=a.localVars;f;f=f.next)if(f.name==d){f=!0;break a}for(a=a.context;a;a=a.prev)for(f=a.vars;f;f=f.next)if(f.name==
d){f=!0;break a}f=void 0}else f=!1;return f?"variable-2":b}}function g(){for(var a=arguments.length-1;0<=a;a--)Z.push(arguments[a])}function b(){g.apply(null,arguments);return!0}function pa(a,b){for(var f=b;f;f=f.next)if(f.name==a)return!0;return!1}function M(a){var b=m;e="def";if(Ba){if(b.context)if("var"==b.lexical.info&&b.context&&b.context.block){var f=Ca(a,b.context);if(null!=f){b.context=f;return}}else if(!pa(a,b.localVars)){b.localVars=new V(a,b.localVars);return}x.globalVars&&!pa(a,b.globalVars)&&
(b.globalVars=new V(a,b.globalVars))}}function Ca(a,b){if(b){if(b.block){var f=Ca(a,b.prev);return f?f==b.prev?b:new W(f,b.vars,!0):null}return pa(a,b.vars)?b:new W(b.prev,new V(a,b.vars),!1)}return null}function ea(a){return"public"==a||"private"==a||"protected"==a||"abstract"==a||"readonly"==a}function W(a,b,f){this.prev=a;this.vars=b;this.block=f}function V(a,b){this.name=a;this.next=b}function H(){m.context=new W(m.context,m.localVars,!1);m.localVars=Ta}function fa(){m.context=new W(m.context,
m.localVars,!0);m.localVars=null}function z(){m.localVars=m.context.vars;m.context=m.context.prev}function k(a,b){var f=function(){var d=m,f=d.indented;if("stat"==d.lexical.type)f=d.lexical.indented;else for(var e=d.lexical;e&&")"==e.type&&e.align;e=e.prev)f=e.indented;d.lexical=new za(f,u.column(),a,null,d.lexical,b)};f.lex=!0;return f}function h(){var a=m;a.lexical.prev&&(")"==a.lexical.type&&(a.indented=a.lexical.indented),a.lexical=a.lexical.prev)}function l(a){function c(f){return f==a?b():";"==
a||"}"==f||")"==f||"]"==f?g():b(c)}return c}function v(a,c){return"var"==a?b(k("vardef",c),qa,l(";"),h):"keyword a"==a?b(k("form"),ra,v,h):"keyword b"==a?b(k("form"),v,h):"keyword d"==a?u.match(/^\s*$/,!1)?b():b(k("stat"),N,l(";"),h):"debugger"==a?b(l(";")):"{"==a?b(k("}"),fa,ga,h,z):";"==a?b():"if"==a?("else"==m.lexical.info&&m.cc[m.cc.length-1]==h&&m.cc.pop()(),b(k("form"),ra,v,h,Da)):"function"==a?b(D):"for"==a?b(k("form"),fa,Ea,v,z,h):"class"==a||p&&"interface"==c?(e="keyword",b(k("form","class"==
a?a:c),Fa,h)):"variable"==a?p&&"declare"==c?(e="keyword",b(v)):p&&("module"==c||"enum"==c||"type"==c)&&u.match(/^\s*\w/,!1)?(e="keyword","enum"==c?b(Ga):"type"==c?b(Ha,l("operator"),n,l(";")):b(k("form"),A,l("{"),k("}"),ga,h,h)):p&&"namespace"==c?(e="keyword",b(k("form"),q,v,h)):p&&"abstract"==c?(e="keyword",b(v)):b(k("stat"),Ua):"switch"==a?b(k("form"),ra,l("{"),k("}","switch"),fa,ga,h,h,z):"case"==a?b(q,l(":")):"default"==a?b(l(":")):"catch"==a?b(k("form"),H,Va,v,h,z):"export"==a?b(k("stat"),Wa,
h):"import"==a?b(k("stat"),Xa,h):"async"==a?b(v):"@"==c?b(q,v):g(k("stat"),q,l(";"),h)}function Va(a){if("("==a)return b(I,l(")"))}function q(a,b){return Ia(a,b,!1)}function y(a,b){return Ia(a,b,!0)}function ra(a){return"("!=a?g():b(k(")"),N,l(")"),h)}function Ia(a,c,f){if(m.fatArrowAt==u.start){var d=f?Ja:Ka;if("("==a)return b(H,k(")"),w(I,")"),h,l("\x3d\x3e"),d,z);if("variable"==a)return g(H,A,l("\x3d\x3e"),d,z)}d=f?O:J;return Ya.hasOwnProperty(a)?b(d):"function"==a?b(D,d):"class"==a||p&&"interface"==
c?(e="keyword",b(k("form"),Za,h)):"keyword c"==a||"async"==a?b(f?y:q):"("==a?b(k(")"),N,l(")"),h,d):"operator"==a||"spread"==a?b(f?y:q):"["==a?b(k("]"),$a,h,d):"{"==a?X(ha,"}",null,d):"quasi"==a?g(ia,d):"new"==a?b(ab(f)):b()}function N(a){return a.match(/[;\}\)\],]/)?g():g(q)}function J(a,c){return","==a?b(N):O(a,c,!1)}function O(a,c,f){var d=0==f?J:O,na=0==f?q:y;if("\x3d\x3e"==a)return b(H,f?Ja:Ka,z);if("operator"==a)return/\+\+|--/.test(c)||p&&"!"==c?b(d):p&&"\x3c"==c&&u.match(/^([^<>]|<[^<>]*>)*>\s*\(/,
!1)?b(k("\x3e"),w(n,"\x3e"),h,d):"?"==c?b(q,l(":"),na):b(na);if("quasi"==a)return g(ia,d);if(";"!=a){if("("==a)return X(y,")","call",d);if("."==a)return b(bb,d);if("["==a)return b(k("]"),N,l("]"),h,d);if(p&&"as"==c)return e="keyword",b(n,d);if("regexp"==a)return m.lastType=e="operator",u.backUp(u.pos-u.start-1),b(na)}}function ia(a,c){return"quasi"!=a?g():"${"!=c.slice(c.length-2)?b(ia):b(N,cb)}function cb(a){if("}"==a)return e="string-2",m.tokenize=U,b(ia)}function Ka(a){oa(u,m);return g("{"==a?
v:q)}function Ja(a){oa(u,m);return g("{"==a?v:y)}function ab(a){return function(c){return"."==c?b(a?db:eb):"variable"==c&&p?b(fb,a?O:J):g(a?y:q)}}function eb(a,c){if("target"==c)return e="keyword",b(J)}function db(a,c){if("target"==c)return e="keyword",b(O)}function Ua(a){return":"==a?b(h,v):g(J,l(";"),h)}function bb(a){if("variable"==a)return e="property",b()}function ha(a,c){if("async"==a)return e="property",b(ha);if("variable"==a||"keyword"==L){e="property";if("get"==c||"set"==c)return b(gb);var f;
p&&m.fatArrowAt==u.start&&(f=u.match(/^\s*:\s*/,!1))&&(m.fatArrowAt=u.pos+f[0].length);return b(K)}if("number"==a||"string"==a)return e=da?"property":L+" property",b(K);if("jsonld-keyword"==a)return b(K);if(p&&ea(c))return e="keyword",b(ha);if("["==a)return b(q,P,l("]"),K);if("spread"==a)return b(y,K);if("*"==c)return e="keyword",b(ha);if(":"==a)return g(K)}function gb(a){if("variable"!=a)return g(K);e="property";return b(D)}function K(a){if(":"==a)return b(y);if("("==a)return g(D)}function w(a,c,
f){function d(e,h){if(f?-1<f.indexOf(e):","==e){var k=m.lexical;"call"==k.info&&(k.pos=(k.pos||0)+1);return b(function(b,d){return b==c||d==c?g():g(a)},d)}return e==c||h==c?b():f&&-1<f.indexOf(";")?g(a):b(l(c))}return function(f,e){return f==c||e==c?b():g(a,d)}}function X(a,c,f){for(var d=3;d<arguments.length;d++)Z.push(arguments[d]);return b(k(c,f),w(a,c),h)}function ga(a){return"}"==a?b():g(v,ga)}function P(a,c){if(p){if(":"==a)return b(n);if("?"==c)return b(P)}}function hb(a,c){if(p&&(":"==a||
"in"==c))return b(n)}function La(a){if(p&&":"==a)return u.match(/^\s*\w+\s+is\b/,!1)?b(q,ib,n):b(n)}function ib(a,c){if("is"==c)return e="keyword",b()}function n(a,c){if("keyof"==c||"typeof"==c||"infer"==c||"readonly"==c)return e="keyword",b("typeof"==c?y:n);if("variable"==a||"void"==c)return e="type",b(B);if("|"==c||"\x26"==c)return b(n);if("string"==a||"number"==a||"atom"==a)return b(B);if("["==a)return b(k("]"),w(n,"]",","),h,B);if("{"==a)return b(k("}"),sa,h,B);if("("==a)return b(w(ta,")"),jb,
B);if("\x3c"==a)return b(w(n,"\x3e"),n);if("quasi"==a)return g(ua,B)}function jb(a){if("\x3d\x3e"==a)return b(n)}function sa(a){return a.match(/[\}\)\]]/)?b():","==a||";"==a?b(sa):g(Y,sa)}function Y(a,c){if("variable"==a||"keyword"==L)return e="property",b(Y);if("?"==c||"number"==a||"string"==a)return b(Y);if(":"==a)return b(n);if("["==a)return b(l("variable"),hb,l("]"),Y);if("("==a)return g(Q,Y);if(!a.match(/[;\}\)\],]/))return b()}function ua(a,c){return"quasi"!=a?g():"${"!=c.slice(c.length-2)?
b(ua):b(n,kb)}function kb(a){if("}"==a)return e="string-2",m.tokenize=U,b(ua)}function ta(a,c){return"variable"==a&&u.match(/^\s*[?:]/,!1)||"?"==c?b(ta):":"==a?b(n):"spread"==a?b(ta):g(n)}function B(a,c){if("\x3c"==c)return b(k("\x3e"),w(n,"\x3e"),h,B);if("|"==c||"."==a||"\x26"==c)return b(n);if("["==a)return b(n,l("]"),B);if("extends"==c||"implements"==c)return e="keyword",b(n);if("?"==c)return b(n,l(":"),n)}function fb(a,c){if("\x3c"==c)return b(k("\x3e"),w(n,"\x3e"),h,B)}function ja(){return g(n,
lb)}function lb(a,c){if("\x3d"==c)return b(n)}function qa(a,c){return"enum"==c?(e="keyword",b(Ga)):g(A,P,E,mb)}function A(a,c){if(p&&ea(c))return e="keyword",b(A);if("variable"==a)return M(c),b();if("spread"==a)return b(A);if("["==a)return X(nb,"]");if("{"==a)return X(Ma,"}")}function Ma(a,c){if("variable"==a&&!u.match(/^\s*:/,!1))return M(c),b(E);"variable"==a&&(e="property");return"spread"==a?b(A):"}"==a?g():"["==a?b(q,l("]"),l(":"),Ma):b(l(":"),A,E)}function nb(){return g(A,E)}function E(a,c){if("\x3d"==
c)return b(y)}function mb(a){if(","==a)return b(qa)}function Da(a,c){if("keyword b"==a&&"else"==c)return b(k("form","else"),v,h)}function Ea(a,c){if("await"==c)return b(Ea);if("("==a)return b(k(")"),ob,h)}function ob(a){return"var"==a?b(qa,R):"variable"==a?b(R):g(R)}function R(a,c){return")"==a?b():";"==a?b(R):"in"==c||"of"==c?(e="keyword",b(q,R)):g(q,R)}function D(a,c){if("*"==c)return e="keyword",b(D);if("variable"==a)return M(c),b(D);if("("==a)return b(H,k(")"),w(I,")"),h,La,v,z);if(p&&"\x3c"==
c)return b(k("\x3e"),w(ja,"\x3e"),h,D)}function Q(a,c){if("*"==c)return e="keyword",b(Q);if("variable"==a)return M(c),b(Q);if("("==a)return b(H,k(")"),w(I,")"),h,La,z);if(p&&"\x3c"==c)return b(k("\x3e"),w(ja,"\x3e"),h,Q)}function Ha(a,c){if("keyword"==a||"variable"==a)return e="type",b(Ha);if("\x3c"==c)return b(k("\x3e"),w(ja,"\x3e"),h)}function I(a,c){"@"==c&&b(q,I);return"spread"==a?b(I):p&&ea(c)?(e="keyword",b(I)):p&&"this"==a?b(P,E):g(A,P,E)}function Za(a,b){return"variable"==a?Fa(a,b):ka(a,b)}
function Fa(a,c){if("variable"==a)return M(c),b(ka)}function ka(a,c){if("\x3c"==c)return b(k("\x3e"),w(ja,"\x3e"),h,ka);if("extends"==c||"implements"==c||p&&","==a)return"implements"==c&&(e="keyword"),b(p?n:q,ka);if("{"==a)return b(k("}"),C,h)}function C(a,c){if("async"==a||"variable"==a&&("static"==c||"get"==c||"set"==c||p&&ea(c))&&u.match(/^\s+#?[\w$\xa1-\uffff]/,!1))return e="keyword",b(C);if("variable"==a||"keyword"==L)return e="property",b(la,C);if("number"==a||"string"==a)return b(la,C);if("["==
a)return b(q,P,l("]"),la,C);if("*"==c)return e="keyword",b(C);if(p&&"("==a)return g(Q,C);if(";"==a||","==a)return b(C);if("}"==a)return b();if("@"==c)return b(q,C)}function la(a,c){if("!"==c||"?"==c)return b(la);if(":"==a)return b(n,E);if("\x3d"==c)return b(y);var f=m.lexical.prev;return g(f&&"interface"==f.info?Q:D)}function Wa(a,c){return"*"==c?(e="keyword",b(va,l(";"))):"default"==c?(e="keyword",b(q,l(";"))):"{"==a?b(w(Na,"}"),va,l(";")):g(v)}function Na(a,c){if("as"==c)return e="keyword",b(l("variable"));
if("variable"==a)return g(y,Na)}function Xa(a){return"string"==a?b():"("==a?g(q):"."==a?g(J):g(ma,Oa,va)}function ma(a,c){if("{"==a)return X(ma,"}");"variable"==a&&M(c);"*"==c&&(e="keyword");return b(pb)}function Oa(a){if(","==a)return b(ma,Oa)}function pb(a,c){if("as"==c)return e="keyword",b(ma)}function va(a,c){if("from"==c)return e="keyword",b(q)}function $a(a){return"]"==a?b():g(w(y,"]"))}function Ga(){return g(k("form"),A,l("{"),k("}"),w(qb,"}"),h,h)}function qb(){return g(A,E)}function wa(a,
b,f){return b.tokenize==F&&/^(?:operator|sof|keyword [bcd]|case|new|export|default|spread|[\[{}\(,;:]|=>)$/.test(b.lastType)||"quasi"==b.lastType&&/\{\s*$/.test(a.string.slice(0,a.pos-(f||0)))}var S=Qa.indentUnit,Pa=x.statementIndent,da=x.jsonld,G=x.json||da,Ba=!1!==x.trackScope,p=x.typescript,ca=x.wordCharacters||/[\w$\xa1-\uffff]/,ya=function(){function a(a){return{type:a,style:"keyword"}}var b=a("keyword a"),f=a("keyword b"),d=a("keyword c"),e=a("keyword d"),g=a("operator"),h={type:"atom",style:"atom"};
return{"if":a("if"),"while":b,"with":b,"else":f,"do":f,"try":f,"finally":f,"return":e,"break":e,"continue":e,"new":a("new"),"delete":d,"void":d,"throw":d,"debugger":a("debugger"),"var":a("var"),"const":a("var"),let:a("var"),"function":a("function"),"catch":a("catch"),"for":a("for"),"switch":a("switch"),"case":a("case"),"default":a("default"),"in":g,"typeof":g,"instanceof":g,"true":h,"false":h,"null":h,undefined:h,NaN:h,Infinity:h,"this":a("this"),"class":a("class"),"super":a("atom"),yield:d,"export":a("export"),
"import":a("import"),"extends":d,await:d}}(),xa=/[+\-*&%=<>!?|~^@]/,Sa=/^@(context|id|value|language|type|container|list|set|reverse|index|base|vocab|graph)"/,T,aa,Ya={atom:!0,number:!0,variable:!0,string:!0,regexp:!0,"this":!0,"import":!0,"jsonld-keyword":!0};Z=e=m=null;L=u=void 0;var Ta=new V("this",new V("arguments",null));H.lex=fa.lex=!0;z.lex=!0;h.lex=!0;return{startState:function(a){a={tokenize:F,lastType:"sof",cc:[],lexical:new za((a||0)-S,0,"block",!1),localVars:x.localVars,context:x.localVars&&
new W(null,null,!1),indented:a||0};x.globalVars&&"object"==typeof x.globalVars&&(a.globalVars=x.globalVars);return a},token:function(a,b){a.sol()&&(b.lexical.hasOwnProperty("align")||(b.lexical.align=!1),b.indented=a.indentation(),oa(a,b));if(b.tokenize!=ba&&a.eatSpace())return null;var f=b.tokenize(a,b);if("comment"==T)return f;b.lastType="operator"!=T||"++"!=aa&&"--"!=aa?T:"incdec";return Aa(b,f,T,aa,a)},indent:function(a,b){if(a.tokenize==ba||a.tokenize==U)return t.Pass;if(a.tokenize!=F)return 0;
var f=b&&b.charAt(0),d=a.lexical,e;if(!/^\s*else\b/.test(b))for(var g=a.cc.length-1;0<=g;--g){var k=a.cc[g];if(k==h)d=d.prev;else if(k!=Da&&k!=z)break}for(;!("stat"!=d.type&&"form"!=d.type||"}"!=f&&(!(e=a.cc[a.cc.length-1])||e!=J&&e!=O||/^[,\.=+\-*:?[\(]/.test(b)));)d=d.prev;Pa&&")"==d.type&&"stat"==d.prev.type&&(d=d.prev);e=d.type;g=f==e;return"vardef"==e?d.indented+("operator"==a.lastType||","==a.lastType?d.info.length+1:0):"form"==e&&"{"==f?d.indented:"form"==e?d.indented+S:"stat"==e?(f=d.indented,
d="operator"==a.lastType||","==a.lastType||xa.test(b.charAt(0))||/[,.]/.test(b.charAt(0)),f+(d?Pa||S:0)):"switch"!=d.info||g||0==x.doubleIndentSwitch?d.align?d.column+(g?0:1):d.indented+(g?0:S):d.indented+(/^(?:case|default)\b/.test(b)?S:2*S)},electricInput:/^\s*(?:case .*?:|default:|\{|\})$/,blockCommentStart:G?null:"/*",blockCommentEnd:G?null:"*/",blockCommentContinue:G?null:" * ",lineComment:G?null:"//",fold:"brace",closeBrackets:"()[]{}''\"\"``",helperType:G?"json":"javascript",jsonldMode:da,
jsonMode:G,expressionAllowed:wa,skipExpression:function(a){Aa(a,"atom","atom","true",new t.StringStream("",2,null))}}});t.registerHelper("wordChars","javascript",/[\w$]/);t.defineMIME("text/javascript","javascript");t.defineMIME("text/ecmascript","javascript");t.defineMIME("application/javascript","javascript");t.defineMIME("application/x-javascript","javascript");t.defineMIME("application/ecmascript","javascript");t.defineMIME("application/json",{name:"javascript",json:!0});t.defineMIME("application/x-json",
{name:"javascript",json:!0});t.defineMIME("application/manifest+json",{name:"javascript",json:!0});t.defineMIME("application/ld+json",{name:"javascript",jsonld:!0});t.defineMIME("text/typescript",{name:"javascript",typescript:!0});t.defineMIME("application/typescript",{name:"javascript",typescript:!0})});