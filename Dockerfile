########## Stage 1: Build frontend with Vite ##########
FROM node:20-bullseye-slim AS frontend
WORKDIR /app

RUN apt-get update && apt-get install -y python3 make g++ \
 && rm -rf /var/lib/apt/lists/*

COPY package*.json ./
RUN npm ci

COPY . .

ENV NODE_ENV=production
RUN npm run build


########## Stage 2: PHP + Nginx + Laravel ##########
FROM webdevops/php-nginx:8.4-alpine

ENV WEB_DOCUMENT_ROOT=/app/public
ENV PHP_DATE_TIMEZONE="Asia/Tehran"
WORKDIR /app

COPY composer*.json ./
RUN composer install --no-dev --no-interaction --prefer-dist --optimize-autoloader || true

COPY --chown=application:application . .
COPY --from=frontend /app/public ./public

RUN composer install --no-dev --no-interaction --prefer-dist --optimize-autoloader \
 && php artisan config:cache \
 && php artisan route:cache \
 && php artisan view:cache \
 && chown -R application:application /app
