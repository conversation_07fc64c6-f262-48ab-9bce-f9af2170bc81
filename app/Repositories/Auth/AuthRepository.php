<?php

namespace App\Repositories\Auth;

use App\Models\RefreshToken;
use App\Models\Token;
use App\Models\User;
use App\Models\UserToken;

class AuthRepository implements AuthRepositoryInterface
{
    public function findLatestActiveToken(string $phone, string $source)
    {
        return UserToken::where('phone', $phone)->where('status', 0)->where('source', $source)->latest()->first();
    }

    public function createUserToken(array $data)
    {
        return UserToken::create($data);
    }

    public function findUserByPhone(string $phone, string $source)
    {
        return User::where('phone', $phone)->where('source', $source)->with(['roles.permissions'])->latest()->first();
    }

    public function createUser(array $data)
    {
        return User::create($data);
    }

    public function expireOldTokens($user)
    {
        $tokenIds = $user->tokens->pluck('id');

        if ($tokenIds->isNotEmpty()) {
            Token::whereIn('_id', $tokenIds)->update(['revoked' => true]);
            RefreshToken::whereIn('access_token_id', $tokenIds)->update(['revoked' => true]);
        }
    }
}
