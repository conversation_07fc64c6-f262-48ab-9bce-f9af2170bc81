<?php

namespace App\Repositories;

use App\Models\RefreshToken;
use App\Models\Token;
use App\Models\User;
use App\Repositories\Interfaces\UserRepositoryInterface;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Lara<PERSON>\Passport\PersonalAccessTokenResult;

class UserRepository implements UserRepositoryInterface
{
    public function findKhodroyarUserByIdAndPhone($id, $phone): ?User
    {
        return User::where('source', 'khodroyar')
            ->whereId($id)
            ->where('phone', $phone)
            ->first();
    }

    public function findKhodroyarUserByToken($token): ?User
    {
        $userTokenTransfer = DB::connection('mongodb')
            ->table('users_token_transfers')
            ->where('token', $token)
            ->where('status', 'pending')
            ->first();

        if ($userTokenTransfer !== null) {

            // $userTokenTransfer->status = 'accept';
            // $userTokenTransfer->save();

            return $this->findKhodroyarUserByIdAndPhone($userTokenTransfer->user_id, $userTokenTransfer->phone);
        }

        return null;
    }

    public function findKhodroxUserByPhone($phone): ?User
    {
        return User::where('source', 'khodrox')
            ->where('phone', $phone)
            ->first();
    }

    public function createKhodroxUser(array $data): User
    {
        return User::create(array_merge($data, [
            'source' => 'khodrox',
            'withdrawalPendingBalance' => 0,
        ]));
    }

    public function resetBalance(User $user): void
    {
        $user->balance = 0;
        $user->save();
    }

    public function revokeTokens(User $user): void
    {
        $tokens = $user->tokens->pluck('id');
        if ($tokens->isNotEmpty()) {
            Token::whereIn('_id', $tokens)->update(['revoked' => true]);
            RefreshToken::whereIn('access_token_id', $tokens)->update(['revoked' => true]);
        }
    }

    public function generateToken(User $user): PersonalAccessTokenResult
    {
        Auth::login($user, true);

        return $user->createToken('khodrox Token');
    }
}
