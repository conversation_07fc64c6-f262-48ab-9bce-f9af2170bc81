<?php

namespace App\Repositories;

use App\Models\Category;
use App\Repositories\Interfaces\CategoryRepositoryInterface;

class CategoryRepository implements CategoryRepositoryInterface
{
    public function getAllWithArticles(string $source, bool $active)
    {
        return Category::with('articles')
            ->where('source', $source)
            ->where('active', $active)
            ->get();
    }
}
