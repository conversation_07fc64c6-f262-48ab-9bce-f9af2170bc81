<?php

namespace App\Repositories\Interfaces;

use App\Models\User;

interface UserRepositoryInterface
{
    public function findKhodroyarUserByIdAndPhone($id, $phone): ?User;

    public function findKhodroyarUserByToken($tokne): ?User;

    public function findKhodroxUserByPhone($phone): ?User;

    public function createKhodroxUser(array $data): User;

    public function resetBalance(User $user): void;

    public function revokeTokens(User $user): void;

    public function generateToken(User $user): \Laravel\Passport\PersonalAccessTokenResult;
}
