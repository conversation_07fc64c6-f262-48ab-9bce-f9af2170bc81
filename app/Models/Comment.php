<?php

namespace App\Models;

use App\Enums\StatusEnum;
use App\Traits\Comment\CommentAttributesTrait;
use App\Traits\Comment\CommentRelationsTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;


class Comment extends Model
{
    protected $connection = 'pgsql';

    public function commentable(): MorphTo
    {
        return $this->morphTo();
    }

    public function getShamsiDateAttribute()
    {
        return shamsiDate($this->created_at);
    }


    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function parent()
    {
        return $this->belongsTo(Comment::class, 'parent_id');
    }

    public function replies()
    {
        return $this->hasMany(Comment::class, 'parent_id');
    }

    protected $fillable = [
        'body',
        'user_id',
        'parent_id',
        'ip_address',
        'user_agent',
        'has_bought',
        'commentable_type',
        'commentable_id',
        'status',
        'rate'
    ];

    protected $casts = [
        'has_bought' => 'boolean',
        'rate' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'status' => StatusEnum::class,
    ];

}
