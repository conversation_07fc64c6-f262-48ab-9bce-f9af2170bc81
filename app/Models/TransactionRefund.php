<?php

namespace App\Models;

use MongoDB\Laravel\Eloquent\Model;

class TransactionRefund extends Model
{
    protected $connection = 'mongodb';

    protected $collection = 'transaction_refund';

    protected $fillable = [
        'phone',
        'card',
        'gate',
        'source',
        'terminal_id',
        'refound_amount',
        'transactions',
        'transaction_3_latest_amount',
        'balance',
        'after_balance',
        'method',
        'description',
        'status_request',
        'status_gate',
        'pause',
        'locked',
        'request_count',
        'refId',
    ];

    public function transactionResultRefund()
    {
        return $this->hasMany(TransactionResultRefund::class, 'transaction_id', 'id');
    }

    public function hasTerminalId()
    {
        return $this->transactionResultRefund->some(function ($item) {
            return isset($item->result['data']['resource']['terminal_id']);
        });
    }
}
