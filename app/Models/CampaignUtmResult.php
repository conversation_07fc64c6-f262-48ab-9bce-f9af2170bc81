<?php

namespace App\Models;

use MongoDB\Laravel\Eloquent\Model;

class CampaignUtmResult extends Model
{
    protected $connection = 'mongodb';

    protected $fillable = [
        'utm_campaign',
        'utm_source',
        'utm_medium',
        'start_date',
        'end_date',
        'sum_transactions',
        'count_transactions',
        'total_subscribers_user',
        'total_guest_user',
        'ctr',
        'cr',
        'cr_per_member',
        'cr_per_transaction',
        'count_click',
        'count_unique_click',
        'impression',
        'campaign_budget',
    ];
}
