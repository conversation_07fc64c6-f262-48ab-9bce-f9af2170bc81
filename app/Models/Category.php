<?php

namespace App\Models;

use MongoDB\Laravel\Eloquent\Model;

class Category extends Model
{
    protected $connection = 'mongodb';

    protected $collection = 'categories';

    protected $fillable = [
        'title',
        'title_eng',
        'title_article',
        'slug',
        'path',
        'source',
        'description',
        'view_count',
        'image_url',
        'active',

        'meta_search',
        'meta_title',
        'canonical',
        'meta_description',
    ];

    public function articles()
    {
        return $this->hasMany(Article::class, 'category_id');
    }
}
