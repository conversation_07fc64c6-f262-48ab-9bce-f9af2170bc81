<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;
use MongoDB\Laravel\Eloquent\Model;

class Article extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'user_id',
        'article_id',
        'archive',
        'title',
        'slug',
        'page',
        'category_id',
        'description',
        'author_id',
        'meta_title',
        'meta_description',
        'meta_tags',
        'view_count',
        'schema',
        'status',
        'faqs',
        'datePublishedEdit',
        'datePublished',
        'meta_search',
        'canonical',
        'article_replace_id',
        'active',
        'type',
        'sidebar',
        'ads',
        'ads_toggle',
        'study_time',
    ];

    // protected $casts = [
    //     'ads' => 'array',
    // ];

    protected $casts = [
        '_id' => 'string',
    ];


    public function category()
    {
        return $this->belongsTo(Category::class, 'category_id');
    }

    public function author()
    {
        return $this->belongsTo(User::class, 'user_id', '_id')->withDefault([
            'fullname' => 'نویسنده ناشناس',
        ]);
    }

    public function galleries()
    {
        return $this->morphMany(Gallery::class, 'galleryable');
    }

    public function parentArticle()
    {
        return $this->belongsTo(Article::class, 'article_id');
    }

    public function getAncestors($limit = 10000)
    {
        $ancestors = collect();
        $current = $this;

        $visited = collect();

        while ($current->parentArticle && $ancestors->count() < $limit) {
            $current = $current->parentArticle;

            if ($visited->contains($current->id)) {
                break;
            }

            $visited->push($current->id);
            $ancestors->push($current);
        }

        return $ancestors->sortByDesc('created_at');
    }
}
