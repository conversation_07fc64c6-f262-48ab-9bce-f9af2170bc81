<?php

namespace App\Models;

use MongoDB\Laravel\Eloquent\Model;

class Transaction extends Model
{
    public function transactionResultRefund()
    {
        return $this->hasMany(TransactionResultRefund::class, 'transaction_id', 'id');
    }

    protected $connection = 'mongodb';

    protected $collection = 'transactions';

    protected $fillable = [
        '_id',
        'gate',
        'source',
        'phone',
        'authority',
        'paymentCode',
        'amount',
        'card_pan',
        'card_hash',
        'fee_type',
        'ip',
        'agent',
        'type',
        'plaque',
        'details',
        'status',
        'result_payment',
        'gateVisit',
        'name',
        'familyName',
        'cardNumber',
        'cardHashPan',
        'iban',
        'settlementCode',
        'clientRefId',
        'refId',
        'bankTransferRefrence',
        'isReturned',
        'creationTime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'phone', 'phone')->latest();
    }

    public function userBalance()
    {
        return $this->belongsTo(User::class, 'phone', 'phone')
            ->whereColumn('transactions.source', 'users.source');
    }
}
