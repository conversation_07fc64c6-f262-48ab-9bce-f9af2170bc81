<?php

namespace App\Models;

use MongoDB\Laravel\Eloquent\Model;

class TransactionResultRefund extends Model
{
    protected $connection = 'mongodb';

    protected $collection = 'transaction_result_refund';

    protected $fillable = [
        'transaction_id',
        'phone',
        'result',
    ];

    public function transaction()
    {
        return $this->belongsTo(TransactionRefund::class, 'transaction_id', 'id');
    }
}
