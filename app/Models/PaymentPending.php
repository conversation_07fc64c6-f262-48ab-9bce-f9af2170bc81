<?php

namespace App\Models;

use MongoDB\Laravel\Eloquent\Model;

class PaymentPending extends Model
{
    protected $connection = 'mongodb';

    protected $collection = 'payment_pendings';

    protected $fillable = [
        'user_id',
        'phone',
        'authority',
        'paymentCode',
        'amount',
        'successURL',
        'failureURL',
        'source',
        'fee_type',
        'fee',
        'ip',
        'agent',
        'header',
        'type',
        'details',
        'plaque',
        'ref_id',
        'card_pan',
        'card_hash',
        'status',
        'result_payment',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'phone', 'phone')->latest();
    }
}
