<?php

namespace App\Models;

use MongoDB\Laravel\Eloquent\Model;

class Setting extends Model
{
    protected $collection = 'settings';

    protected $fillable = [
        'key',
        'value',
        'title',
    ];

    public static function isActive(string $key): bool
    {
        return cache()->remember("setting_active_{$key}", now()->addMinutes(10), function () use ($key) {
            return self::where('key', $key)->value('value') === 'ACTIVE';
        });
    }
}
