<?php

namespace App\Models;

use Illuminate\Support\Collection;
use MongoDB\Laravel\Eloquent\Model;

class TransactionUserQabzino extends Model
{
    protected $connection = 'mongodb';

    protected $collection = 'transaction_user_qabzino';

    protected $fillable = [
        'phone',
        'amount',
        'bill_id',
        'payment_id',
        'paid',
        'description',
        'status',
        'trace_number',
        'transaction_date_time',
        'result',
        'payment_key',
    ];

    public function relatedPhones(): Collection
    {
        $billId = $this->bill_id;
        $paymentId = $this->payment_id;

        return InquiryResult::where(function ($query) use ($billId, $paymentId) {
            $query->where('result.Parameters.BillID', $billId)
                ->where('result.Parameters.PaymentID', $paymentId);
        })
            ->orWhere(function ($query) use ($billId, $paymentId) {
                $query->where('result.Parameters.Details', 'exists', true)
                    ->where('result.Parameters.Details', 'elemMatch', [
                        'BillID' => $billId,
                        'PaymentID' => $paymentId,
                    ]);
            })
            ->pluck('phone')
            ->unique()
            ->values(); // optional: reindex the collection
    }
}
