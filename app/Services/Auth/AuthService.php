<?php

namespace App\Services\Auth;

use App\Jobs\AuthSMSJob;
use App\Repositories\Auth\AuthRepositoryInterface;
use App\Services\MeliPayamak as ServiceMeliPayamak;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AuthService implements AuthServiceInterface
{
    public function __construct(
        protected AuthRepositoryInterface $authRepository
    ) {
    }

    public function authenticate(Request $request)
    {
        $phone = faTOen(trim(preg_replace('/\s+/', '', $request->phone)));
        $app = app('authenticated_application');

        $tokenNumber = app()->isProduction() ? rand(10000, 99999) : '12345';
        // $tokenNumber = rand(10000, 99999);
        $accessToken = str()->random(600);

        $this->authRepository->createUserToken([
            'access_token' => $accessToken,
            'token' => $tokenNumber,
            'phone' => $phone,
            'status' => 0,
            'source' => $app->source,
            'ip' => $request->ip(),
            'agent' => $request->userAgent(),
            'header' => $request->header('X-Application-Token'),
        ]);

        if (app()->isProduction()) {

            if ($phone != "09103528525") {
                (new ServiceMeliPayamak)->sendAuthOtp($tokenNumber, $phone, $app->pattern_login);
            } else {
                $this->authRepository->createUserToken([
                    'access_token' => $accessToken,
                    'token' => 12345,
                    'phone' => $phone,
                    'status' => 0,
                    'source' => $app->source,
                    'ip' => $request->ip(),
                    'agent' => $request->userAgent(),
                    'header' => $request->header('X-Application-Token'),
                ]);
            }
            // AuthSMSJob::dispatch($tokenNumber, $phone, $app->pattern_login, $app->source);
        }

        return [
            'token' => $accessToken,
            'code' => $tokenNumber,
            'expire_time' => 180,
        ];
    }

    public function verify(Request $request)
    {
        $source = app('authenticated_application')->source;

        $phone = faTOen(trim(preg_replace('/\s+/', '', $request->phone)));

        $tokenCheck = $this->authRepository->findLatestActiveToken($phone, $source);

        if (
            $tokenCheck &&
            $tokenCheck->access_token === $request->token &&
            (int) $tokenCheck->token === (int) $request->code
        ) {
            if (now()->diffInMinutes($tokenCheck->created_at) > 10) {
                $tokenCheck->status = 1;
                $tokenCheck->save();

                return ['error' => 'expired'];
            }

            $user = $this->authRepository->findUserByPhone($phone, $source) ??
                $this->authRepository->createUser([
                    'phone' => $phone,
                    'source' => $source,
                    'balance' => 0,
                    'withdrawalPendingBalance' => 0,
                    'createdAt' => now(),
                    'depositVisit' => false,
                    'gateVisit' => false,
                    'ip' => $request->ip(),
                    'agent' => $request->userAgent(),
                    'header' => $request->header('X-Application-Token'),
                ]);

            Auth::login($user, true);

            if (isset($user->phone) && $user->phone != '09103528525') {
                $this->authRepository->expireOldTokens($user);
            }

            $token = $user->createToken($source . ' Token');

            $tokenCheck->status = 1;
            $tokenCheck->save();

            return [
                'access_token' => $token->accessToken,
                'token_type' => 'Bearer',
                'balance' => $user->balance,
                'expire_at' => Carbon::parse($token->token->expire_at)->timestamp,
            ];
        }

        return ['error' => 'invalid'];
    }

    public function logout()
    {
        if (Auth::check()) {
            $this->authRepository->expireOldTokens(auth()->user());
        }
    }
}
