<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Exception;
use App\Jobs\GenerateSitemapJob;

class FtpSitemapService
{
    private $ftpConnection;
    private $ftpHost;
    private $ftpUsername;
    private $ftpPassword;
    private $ftpPort;
    private $ftpPath;

    public function __construct()
    {
        $this->ftpHost = env('FTP_SITEMAP_HOST');
        $this->ftpUsername = env('FTP_SITEMAP_USERNAME');
        $this->ftpPassword = env('FTP_SITEMAP_PASSWORD');
        $this->ftpPort = env('FTP_SITEMAP_PORT', 21);
        $this->ftpPath = env('FTP_SITEMAP_PATH', '/');
    }

    /**
     * Connect to FTP server
     */
    public function connect()
    {
        try {
            $this->ftpConnection = ftp_connect($this->ftpHost, $this->ftpPort);

            if (!$this->ftpConnection) {
                throw new Exception("نمی‌توان به سرور FTP متصل شد");
            }

            $login = ftp_login($this->ftpConnection, $this->ftpUsername, $this->ftpPassword);

            if (!$login) {
                throw new Exception("خطا در ورود به سرور FTP");
            }

            // Set passive mode
            ftp_pasv($this->ftpConnection, true);

            Log::info("اتصال به سرور FTP برقرار شد");

            return true;

        } catch (Exception $e) {
            Log::error("خطا در اتصال به FTP: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Upload sitemap files to FTP server
     */
    public function uploadSitemapFiles()
    {
        try {
            if (!$this->ftpConnection) {
                $this->connect();
            }

            $basePath = \App\Jobs\GenerateSitemapJob::SITEMAP_BASE_PATH;
            $files = [
                $basePath . '/sitemap.xml' => storage_path($basePath . '/sitemap.xml'),
                $basePath . '/articles.xml' => storage_path($basePath . '/articles.xml'),
                $basePath . '/services.xml' => storage_path($basePath . '/services.xml')
            ];

            $uploadedFiles = [];

            foreach ($files as $remotePath => $localPath) {
                if (file_exists($localPath)) {
                    $fullRemotePath = rtrim($this->ftpPath, '/') . '/' . $remotePath;

                    // Create directory if needed
                    if (strpos($remotePath, '/') !== false) {
                        $remoteDir = dirname($fullRemotePath);
                        $this->createRemoteDirectory($remoteDir);
                    }

                    $upload = ftp_put($this->ftpConnection, $fullRemotePath, $localPath, FTP_BINARY);

                    if ($upload) {
                        $uploadedFiles[] = $remotePath;
                        Log::info("فایل {$remotePath} با موفقیت آپلود شد");
                    } else {
                        Log::error("خطا در آپلود فایل {$remotePath}");
                    }
                } else {
                    Log::warning("فایل {$localPath} وجود ندارد");
                }
            }

            Log::info("آپلود فایل‌های سایت‌مپ تکمیل شد. فایل‌های آپلود شده: " . implode(', ', $uploadedFiles));

            return $uploadedFiles;

        } catch (Exception $e) {
            Log::error("خطا در آپلود فایل‌های سایت‌مپ: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Create remote directory if it doesn't exist
     */
    private function createRemoteDirectory($path)
    {
        $parts = explode('/', trim($path, '/'));
        $currentPath = $this->ftpPath;

        foreach ($parts as $part) {
            if (empty($part))
                continue;

            $currentPath = rtrim($currentPath, '/') . '/' . $part;

            // Try to change to directory, if it fails, create it
            if (!@ftp_chdir($this->ftpConnection, $currentPath)) {
                if (@ftp_mkdir($this->ftpConnection, $currentPath)) {
                    Log::info("دایرکتوری {$currentPath} ایجاد شد");
                } else {
                    Log::warning("نمی‌توان دایرکتوری {$currentPath} را ایجاد کرد");
                }
            }
        }

        // Return to base path
        ftp_chdir($this->ftpConnection, $this->ftpPath);
    }

    /**
     * Test FTP connection
     */
    public function testConnection()
    {
        try {
            $this->connect();
            $this->disconnect();
            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Disconnect from FTP server
     */
    public function disconnect()
    {
        if ($this->ftpConnection) {
            ftp_close($this->ftpConnection);
            $this->ftpConnection = null;
            Log::info("اتصال FTP قطع شد");
        }
    }

    /**
     * Get FTP connection status
     */
    public function isConnected()
    {
        return $this->ftpConnection !== null;
    }

    /**
     * Upload single file
     */
    public function uploadFile($localPath, $remotePath)
    {
        try {
            if (!$this->ftpConnection) {
                $this->connect();
            }

            if (!file_exists($localPath)) {
                throw new Exception("فایل محلی وجود ندارد: {$localPath}");
            }

            $fullRemotePath = rtrim($this->ftpPath, '/') . '/' . ltrim($remotePath, '/');

            // Create directory if needed
            if (strpos($remotePath, '/') !== false) {
                $remoteDir = dirname($fullRemotePath);
                $this->createRemoteDirectory($remoteDir);
            }

            $upload = ftp_put($this->ftpConnection, $fullRemotePath, $localPath, FTP_BINARY);

            if ($upload) {
                Log::info("فایل {$remotePath} با موفقیت آپلود شد");
                return true;
            } else {
                throw new Exception("خطا در آپلود فایل {$remotePath}");
            }

        } catch (Exception $e) {
            Log::error("خطا در آپلود فایل: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Destructor - ensure connection is closed
     */
    public function __destruct()
    {
        $this->disconnect();
    }
}
