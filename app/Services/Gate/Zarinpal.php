<?php

namespace App\Services\Gate;

use App\Models\PaymentPending;
use App\Models\Setting;
use App\Models\Transaction;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use SoapClient;

class <PERSON>arinpal
{
    protected $ZARINPAL_MERCHANT_ID;
    protected $ZARINPAL_MERCHANT_ID_NAGHLIEH;

    protected $ZARINPAL_PAYMENT_URL;

    protected $ZARINPAL_SANDBOX_PAYMENT_URL;
    protected $KHODROX_CALLBACK_PAYMENT_HUB;

    public function __construct()
    {
        $this->ZARINPAL_MERCHANT_ID = env('ZARINPAL_MERCHANT_ID');
        $this->ZARINPAL_MERCHANT_ID_NAGHLIEH = env('ZARINPAL_MERCHANT_ID_NAGHLIEH');
        $this->ZARINPAL_PAYMENT_URL = env('ZARINPAL_PAYMENT_URL');
        $this->ZARINPAL_SANDBOX_PAYMENT_URL = env('ZARINPAL_SANDBOX_PAYMENT_URL');
        $this->KHODROX_CALLBACK_PAYMENT_HUB = env('KHODROX_CALLBACK_PAYMENT_HUB');

    }

    public function handle($request, array $data)
    {

        $order = PaymentPending::create([
            'user_id' => $data['user_id'],
            'phone' => $data['mobile'],
            'source' => $data['source'],
            'authority' => null,
            'paymentCode' => null,
            'amount' => str_replace(',', '', $data['amount']),
            'successURL' => $data['callback_url'],
            'failureURL' => $data['callback_url'],
            'ref_id' => null,
            'card_pan' => null,
            'card_hash' => null,
            'fee_type' => null,
            'fee' => null,
            'ip' => $request->ip(),
            'agent' => $request->userAgent(),
            'header' => $request->header('X-Application-Token'),
            'type' => $data['type'],
            'plaque' => $data['plaque'],
            'details' => $data['details'],
            'status' => 'pending',
            'result_payment' => null,
        ]);

        if (app()->isProduction()) {

            $client = new SoapClient($this->ZARINPAL_PAYMENT_URL . '/pg/services/WebGate/wsdl', ['encoding' => 'UTF-8']);

            $setting = Setting::where('key', 'redirect_payment_and_verfication_khodrox')->first();
            $callbackUrl = $setting?->value == 'ACTIVE' ? $this->KHODROX_CALLBACK_PAYMENT_HUB : $data['callback_url'];


            $merchentId = $this->ZARINPAL_MERCHANT_ID;

            if (!empty($data['source']) && $data['source'] === 'naghlie') {
                $merchentId = $this->ZARINPAL_MERCHANT_ID_NAGHLIEH;
                $callbackUrl = $data['callback_url'];
            }

            Log::info('callback', [
                'callbackUrl' => $callbackUrl,
                'paymentHubCallback' => $this->KHODROX_CALLBACK_PAYMENT_HUB,
                'dataCallbackUrl' => $data['callback_url'],
                $data['source']
            ]);

            $responseJson = $client->PaymentRequest([
                'MerchantID' => $merchentId,
                'Amount' => str_replace(',', '', $data['amount']),
                'Description' => $data['description'],
                'Mobile' => $data['mobile'],
                'CallbackURL' => $callbackUrl,
            ]);

            if (isset($responseJson->Authority)) {
                PaymentPending::whereId($order->id)->update([
                    'authority' => $responseJson->Authority,
                ]);

                return [
                    'status' => 100,
                    'payment_url' => $this->ZARINPAL_PAYMENT_URL . '/pg/StartPay/' . $responseJson->Authority,
                ];
            }

            return [
                'status' => isset($responseJson->data) ? $responseJson->data->code : -100,
                'errors' => isset($responseJson->errors) ? $responseJson->errors : $responseJson->message,
            ];

        } else {
            $setting = Setting::where('key', 'redirect_payment_and_verfication_khodrox')->first();
            $callbackUrl = $setting?->value == 'ACTIVE' ? $this->KHODROX_CALLBACK_PAYMENT_HUB : $data['callback_url'];

            $merchentId = $this->ZARINPAL_MERCHANT_ID;

            if (!empty($data['source']) && $data['source'] === 'naghlie') {
                $merchentId = $this->ZARINPAL_MERCHANT_ID_NAGHLIEH;
                $callbackUrl = $data['callback_url'];
            }

            $result = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ])->post($this->ZARINPAL_SANDBOX_PAYMENT_URL . '/pg/v4/payment/request.json', [
                        'merchant_id' => $merchentId,
                        'amount' => str_replace(',', '', $data['amount']),
                        'currency' => 'IRT',
                        'description' => $data['description'],
                        'callback_url' => $callbackUrl,
                        'mobile' => $data['mobile'],
                        'email' => $data['email'],
                        'order_id' => $order->id,
                    ]);

            $responseJson = json_decode($result);

            if (isset($responseJson?->data?->authority)) {
                PaymentPending::whereId($order->id)->update([
                    'authority' => $responseJson?->data?->authority,
                ]);

                return [
                    'status' => 100,
                    'payment_url' => $this->ZARINPAL_SANDBOX_PAYMENT_URL . '/pg/StartPay/' . $responseJson?->data?->authority,
                ];
            }

            return [
                'status' => isset($responseJson?->data) ? $responseJson?->data?->code ?? -100 : -100,
                'errors' => isset($responseJson?->errors)
                    ? array_merge(
                        (array) $responseJson->errors,
                        ['message' => $this->getStatus($responseJson->errors->code ?? null)]
                    )
                    : $responseJson?->message,
            ];
        }

    }

    public function verfication($request, $data) // Make the method non-static
    {

        $payment = PaymentPending::where('authority', $data['authority'])->first();

        $user_id = auth()?->id() ?? $payment->user_id;
        $payment = PaymentPending::where('user_id', $user_id)->where('authority', $data['authority'])->first();

        if ($payment) {

            if (app()->isProduction()) {
                try {
                    $client = new SoapClient($this->ZARINPAL_PAYMENT_URL . '/pg/services/WebGate/wsdl', ['encoding' => 'UTF-8']);

                    $merchentId = $this->ZARINPAL_MERCHANT_ID;


                    if (!empty($payment->source) && $payment->source === 'naghlie') {
                        $merchentId = $this->ZARINPAL_MERCHANT_ID_NAGHLIEH;
                    }

                    $result = $client->PaymentVerification([
                        'MerchantID' => $merchentId,
                        'Authority' => $data['authority'],
                        'Amount' => $payment->amount,
                    ]);

                    if (isset($result->Status) && $result->Status == 100) {

                        $payment->ref_id = $result->RefID;
                        $payment->status = 'success';
                        $payment->result_payment = $result;
                        $payment->save();

                        $payment = PaymentPending::where('user_id', $user_id)->where('authority', $data['authority'])->first();

                        Transaction::create([
                            'gate' => 'zarinpal',
                            'source' => $payment->source,
                            'phone' => $payment->phone,
                            'authority' => $payment->authority,
                            'paymentCode' => $payment->paymentCode,
                            'amount' => $payment->amount,
                            'card_pan' => $payment->card_pan,
                            'refId' => $payment->ref_id,
                            'card_hash' => $payment->card_hash,
                            'fee_type' => $payment->fee_type,
                            'ip' => $payment->ip,
                            'agent' => $payment->agent,
                            'type' => $payment->type,
                            'plaque' => $payment->plaque,
                            'details' => $payment->details,
                            'status' => $payment->status,
                            'result_payment' => $payment->result_payment,
                        ]);

                        return [
                            'status' => $result?->Status,
                            'amount' => $payment?->amount ?? 0,
                            'type' => $payment?->type ?? null,
                            'refId' => $result?->RefID,
                            'createAt' => shamsiDate($payment->created_at),
                            'details' => $payment?->details ?? null,
                            'plaque' => $payment?->plaque ?? null,
                            'createdAt' => todays(),
                            'errors' => isset($result->errors) ? $result->errors : [],
                            'callbackUrl' => $payment->successURL
                        ];

                    } else {
                        return [
                            'status' => $result?->Status,
                            'amount' => $payment?->amount ?? 0,
                            'type' => $payment?->type ?? null,
                            'refId' => $result?->RefID,
                            'createAt' => shamsiDate($payment->created_at),
                            'details' => $payment?->details ?? null,
                            'plaque' => $payment?->plaque ?? null,
                            'createdAt' => todays(),
                            'errors' => isset($result->errors) ? $result->errors : [],
                            'callbackUrl' => $payment->successURL
                        ];
                    }
                } catch (\SoapFault $e) {

                    return response()->json([
                        'success' => false,
                        'message' => 'Connection error: ' . $e->getMessage(),
                    ]);
                }

                // return [
                //     'status' => $result->Status,
                //     'errors' => [
                //         'message' => 'پرداخت ناموفق بود یا قبلاً تایید نشده است.'
                //     ],
                // ];

            } else {

                $mercchentId = $this->ZARINPAL_MERCHANT_ID;

                if (!empty($payment->source) && $payment->source === 'naghlie') {
                    $merchentId = $this->ZARINPAL_MERCHANT_ID_NAGHLIEH;
                }

                $response = Http::withHeaders([
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                ])->post($this->ZARINPAL_SANDBOX_PAYMENT_URL . '/pg/v4/payment/verify.json', [
                            'merchant_id' => $mercchentId,
                            'authority' => $data['authority'],
                            'amount' => $payment?->amount,
                        ]);

                $responseJson = $response->json();

                if (isset($responseJson['data']['code']) && $responseJson['data']['code'] == 100) {

                    $payment = PaymentPending::where('user_id', $user_id)->where('authority', $data['authority'])->first();
                    if ($payment) {

                        $payment->ref_id = $responseJson['data']['ref_id'];
                        $payment->card_pan = $responseJson['data']['card_pan'];
                        $payment->card_hash = $responseJson['data']['card_hash'];
                        $payment->fee_type = $responseJson['data']['fee_type'];
                        $payment->fee = $responseJson['data']['fee'];
                        $payment->status = 'success';
                        $payment->result_payment = $responseJson;
                        $payment->save();

                        $payment = PaymentPending::where('user_id', $user_id)->where('authority', $data['authority'])->first();

                        Transaction::create([
                            'gate' => 'zarinpal',
                            'source' => $payment->source,
                            'phone' => $payment->phone,
                            'authority' => $payment->authority,
                            'paymentCode' => $payment->paymentCode,
                            'amount' => $payment->amount,
                            'card_pan' => $payment->card_pan,
                            'refId' => $payment->ref_id,
                            'card_hash' => $payment->card_hash,
                            'fee_type' => $payment->fee_type,
                            'ip' => $payment->ip,
                            'agent' => $payment->agent,
                            'type' => $payment->type,
                            'plaque' => $payment->plaque,
                            'details' => $payment->details,
                            'status' => $payment->status,
                            'result_payment' => $payment->result_payment,
                        ]);

                        return [
                            'status' => $responseJson['data']['code'],
                            'amount' => $payment?->amount ?? 0,
                            'type' => $payment?->type ?? null,
                            'refId' => $responseJson['data']['ref_id'],
                            'createAt' => dateTimeToday(),
                            'details' => $payment?->details ?? null,
                            'plaque' => $payment?->plaque ?? null,
                            'errors' => isset($responseJson['errors']) ? $responseJson['errors'] : [],
                            'callbackUrl' => $payment->successURL
                        ];
                    }

                    $payment->result_payment == $responseJson;
                    $payment->save();

                    return [
                        'status' => '400',
                        'errors' => [
                            'validation' => 'اطلاعات ارسال شده اشتباه می باشد. در صورت تکرار اکانت شما غیرفعال میگردد',
                        ],
                    ];

                }

                return [
                    'status' => $responseJson['Status'] ?? -51,
                    'amount' => $payment?->amount ?? 0,
                    'type' => $payment?->type ?? null,
                    'refId' => $responseJson['RefID'] ?? null,
                    'createAt' => shamsiDate($payment->created_at),
                    'details' => $payment?->details ?? null,
                    'plaque' => $payment?->plaque ?? null,
                    'errors' => isset($responseJson['errors']) ? $responseJson['errors'] : [],
                    'callbackUrl' => $payment->successURL
                ];

            }

        }

        return [
            'status' => '400',
            'errors' => [
                'validation' => 'اطلاعات ارسال شده اشتباه می باشد. در صورت تکرار اکانت شما غیرفعال میگردد',
            ],
        ];

    }

    private function getStatus($statusCode)
    {
        switch ($statusCode) {
            case -9:
                return 'خطای اعتبار سنجی: مقدارهای ضروری مانند مرچنت کد، آدرس بازگشت، توضیحات یا مبلغ پرداختی ممکن است نادرست باشند.';
            case -10:
                return 'ای پی یا مرچنت كد پذیرنده صحیح نیست.';
            case -11:
                return 'مرچنت کد فعال نیست، پذیرنده مشکل خود را به امور مشتریان زرین‌پال ارجاع دهد.';
            case -12:
                return 'تلاش بیش از حد در یک بازه زمانی کوتاه، لطفا بعدا تلاش کنید.';
            case -15:
                return 'درگاه پرداخت به حالت تعلیق در آمده است، پذیرنده مشکل خود را به امور مشتریان زرین‌پال ارجاع دهد.';
            case -16:
                return 'سطح تایید پذیرنده پایین‌تر از سطح نقره‌ای است.';
            case -17:
                return 'محدودیت پذیرنده در سطح آبی.';
            case 100:
                return 'عملیات موفق.';
            case -30:
                return 'پذیرنده اجازه دسترسی به سرویس تسویه اشتراکی شناور را ندارد.';
            case -31:
                return 'حساب بانکی تسویه را به پنل اضافه کنید.';
            case -32:
                return 'مبلغ وارد شده از مبلغ کل تراکنش بیشتر است.';
            case -33:
                return 'درصدهای وارد شده صحیح نیست.';
            case -34:
                return 'مبلغ وارد شده از مبلغ کل تراکنش بیشتر است.';
            case -35:
                return 'تعداد افراد دریافت‌کننده تسهیم بیش از حد مجاز است.';
            case -36:
                return 'حداقل مبلغ جهت تسهیم باید ۱۰,۰۰۰ ریال باشد.';
            case -37:
                return 'یک یا چند شماره شبای وارد شده برای تسهیم از سمت بانک غیر فعال است.';
            case -38:
                return 'خطا٬عدم تعریف صحیح شبا٬لطفا دقایقی دیگر تلاش کنید.';
            case -39:
                return 'خطایی رخ داده است، لطفا با امور مشتریان زرین‌پال تماس بگیرید.';
            case -40:
                return 'پارامترهای اضافی نامعتبر هستند، مقدار expire_in معتبر نیست.';
            case -41:
                return 'حداکثر مبلغ پرداختی ۱۰۰ میلیون تومان است.';
            case -50:
                return 'مبلغ پرداخت شده با مقدار مبلغ ارسال شده در متد وریفای متفاوت است.';
            case -51:
                return 'پرداخت ناموفق.';
            case -52:
                return 'خطای غیر منتظره‌ای رخ داده است، پذیرنده مشکل خود را به امور مشتریان زرین‌پال ارجاع دهد.';
            case -53:
                return 'پرداخت متعلق به این مرچنت کد نیست.';
            case -54:
                return 'اتوریتی نامعتبر است.';
            case -55:
                return 'تراکنش مورد نظر یافت نشد.';
            case -60:
                return 'امکان ریورس کردن تراکنش با بانک وجود ندارد.';
            case -61:
                return 'تراکنش موفق نیست یا قبلا ریورس شده است.';
            case -62:
                return 'آی پی درگاه تنظیم نشده است.';
            case -63:
                return 'حداکثر زمان (۳۰ دقیقه) برای ریورس کردن این تراکنش منقضی شده است.';
            case 101:
                return 'تراکنش وریفای شده است.';
            default:
                return 'خطای ناشناخته، لطفا با پشتیبانی تماس بگیرید.';
        }
    }
}
