<?php

namespace App\Services;

use App\Repositories\Interfaces\UserRepositoryInterface;
use App\Traits\InquiryTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;

class TransferService
{
    use InquiryTrait;

    public function __construct(protected UserRepositoryInterface $userRepo) {}

    public function handleTransfer(Request $request, $app)
    {
        $withDetails = (bool) $request->input('withDetails');
        $type = $request->input('type');
        $plateLeft = $request->input('details.plaque.plateLeft');
        $plateMiddle = $request->input('details.plaque.plateMiddle');
        $plateRight = $request->input('details.plaque.plateRight');
        $plateAlphabet = $request->input('details.plaque.plateAlphabet');
        $phoneNumber = $request->input('details.phoneNumber');
        $nationalCode = $request->input('details.nationalCode');
        $token = $request->get('token');
        $phone = $request->get('phone');

        $user = $this->userRepo->findKhodroyarUserByToken($token);
        if (! $user) {
            return [false, 'کاربر یافت نشد'];
        }

        $newUser = $this->userRepo->findKhodroxUserByPhone($request->get('phone'));

        if (! $newUser) {
            $newUser = $this->userRepo->createKhodroxUser([
                'phone' => $user->phone,
                'balance' => 0,
                'ip' => $request->ip(),
                'agent' => $request->userAgent(),
                'header' => $request->header('X-Application-Token'),
            ]);
        } else {
            // if ($user->balance != 0) {
            //     // $newUser->balance = $newUser->balance + $user->balance;
            //     // $newUser->balance = $user->balance;
            //     // $newUser->save();
            // }

        }

        // $this->userRepo->resetBalance($user);
        // $this->userRepo->revokeTokens($newUser);
        $resultToken = $this->userRepo->generateToken($newUser);

        return [true, [
            'message' => 'با موفقیت لاگین شدین',
            'access_token' => $resultToken->accessToken,
            'token_type' => 'Bearer',
            'balance' => $user->balance,
            'withDetails' => $withDetails,
            'details' => [
                'type' => $type,
                'plaque' => [
                    'plateLeft' => $plateLeft,
                    'plateMiddle' => $plateMiddle,
                    'plateRight' => $plateRight,
                    'plateAlphabet' => $plateAlphabet,
                ],
                'phoneNumber' => $phoneNumber,
                'nationalCode' => $nationalCode,
            ],
            'expire_at' => Carbon::parse($resultToken->token->expire_at)->timestamp,
            // 'redirect_link' => 'https://khodrox.com/login?left='.$plateLeft.'&alphabet='.$plateAlphabet.'&middle='.$plateMiddle.'&right='.$plateRight.'&isMotor=false'.'&withDetails='.$withDetails.'&inquiry=true',
        ]];

    }
}
