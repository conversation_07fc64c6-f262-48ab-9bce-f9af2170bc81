<?php

namespace App\Services;

use SoapClient;

class MeliPayamak
{
    protected $MELI_PAYAMAK_USERNAME;

    protected $MELI_PAYAMAK_PASSWORD;

    public function __construct()
    {
        $this->MELI_PAYAMAK_USERNAME = env('MELI_PAYAMAK_USERNAME');
        $this->MELI_PAYAMAK_PASSWORD = env('MELI_PAYAMAK_PASSWORD');
    }

    private function sendSMS($text, $to, $bodyId)
    {

        $data = [
            'username' => $this->MELI_PAYAMAK_USERNAME,
            'password' => $this->MELI_PAYAMAK_PASSWORD,
            'text' => $text,
            'to' => $to,
            'bodyId' => $bodyId,
        ];

        ini_set('soap.wsdl_cache_enabled', '0');
        $sms = new SoapClient('https://api.payamak-panel.com/post/send.asmx?wsdl', ['encoding' => 'UTF-8']);

        $send_Result = $sms->SendByBaseNumber($data)->SendByBaseNumberResult;

        return $send_Result;
    }

    public function sendAuthOtp($code, $phone, $pattern)
    {
        return $this->sendSMS([$code], $phone, $pattern);
    }


    public function sendBalanceCharge($code, $phone, $pattern)
    {
        return $this->sendSMS([$code], $phone, $pattern);
    }
    public function getCredit()
    {
        $data = [
            'username' => $this->MELI_PAYAMAK_USERNAME,
            'password' => $this->MELI_PAYAMAK_PASSWORD,
        ];

        ini_set('soap.wsdl_cache_enabled', '0');
        $sms = new SoapClient('http://api.payamak-panel.com/post/Send.asmx?wsdl', ['encoding' => 'UTF-8']);

        $result = $sms->GetCredit($data);

        return $result->GetCreditResult;
        // return isset($result->GetCreditResult) ? formatMoney(round($result->GetCreditResult) * 10) : 0;
    }
}
