<?php

namespace App\Services;

use Illuminate\Support\Facades\Storage;

class ImageHelper
{
    public static function generateImage(array $data, $filename = 'output.png', $imagePath = null)
    {
        // مسیر ذخیره فایل در storage
        $storagePath = storage_path("app/public/{$filename}");

        // ابعاد تصویر
        $imageWidth = 400;
        $imageHeight = 400;

        // ایجاد تصویر سفید
        $image = imagecreatetruecolor($imageWidth, $imageHeight);
        $white = imagecolorallocate($image, 255, 255, 255);
        $black = imagecolorallocate($image, 0, 0, 0);
        imagefilledrectangle($image, 0, 0, $imageWidth, $imageHeight, $white);

        // اضافه کردن عکس به تصویر (اگر مسیر عکس داده شده باشد)
        if ($imagePath && file_exists($imagePath)) {
            $ext = pathinfo($imagePath, PATHINFO_EXTENSION);

            // خواندن عکس با توجه به فرمت
            switch (strtolower($ext)) {
                case 'jpg':
                case 'jpeg':
                    $uploadedImage = imagecreatefromjpeg($imagePath);
                    break;
                case 'png':
                    $uploadedImage = imagecreatefrompng($imagePath);
                    break;
                default:
                    throw new \Exception("Unsupported image format: {$ext}");
            }

            // ابعاد عکس
            $imageX = 210; // موقعیت X
            $imageY = 250; // موقعیت Y
            $imageWidthResized = 150; // عرض عکس در تصویر نهایی
            $imageHeightResized = 100; // ارتفاع عکس در تصویر نهایی

            // تغییر اندازه عکس و کپی کردن در تصویر اصلی
            imagecopyresampled(
                $image,
                $uploadedImage,
                $imageX,
                $imageY,
                0,
                0,
                $imageWidthResized,
                $imageHeightResized,
                imagesx($uploadedImage),
                imagesy($uploadedImage)
            );

            // آزاد کردن حافظه مربوط به عکس
            imagedestroy($uploadedImage);
        }

        // تنظیم فونت و موقعیت متن
        $fontFile = public_path('/assets/fonts/IRANSansWeb.ttf'); // مسیر فایل فونت
        if (! file_exists($fontFile)) {
            throw new \Exception('Font file not found. Please add IRANSansWeb.ttf in the public/fonts directory.');
        }

        $fontSize = 15;
        $textColor = imagecolorallocate($image, 0, 0, 0); // رنگ مشکی برای متن (پررنگ)
        $textAngle = 0; // زاویه صفر یعنی متن افقی خواهد بود
        $xPosition = 20; // موقعیت X برای شروع متن
        $yPosition = 60; // موقعیت Y برای شروع متن

        // اضافه کردن متن به تصویر
        foreach ($data as $key => $value) {
            $text = "{$key}: {$value}";
            imagettftext($image, $fontSize, $textAngle, $xPosition, $yPosition, $textColor, $fontFile, $text);
            $yPosition += 40; // فاصله بین خطوط
        }

        // ذخیره تصویر در مسیر storage
        imagepng($image, $storagePath);
        imagedestroy($image);

        // بازگرداندن URL فایل ذخیره‌شده
        return Storage::url($filename);  // اینجا URL فایل برگشت داده می‌شود
    }
}
