<?php

namespace App\Services\Actions\InquiryCar;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class InquiryCarResponseAction
{
    public static function handleCarWithDetailsHttpClient($InquiryResult, $traceNumber): array
    {
        $url = env('QABZINO_CAR_WITH_DETAILS_INQUIRY');

        return self::handleCarHttpClientRequest($url, $InquiryResult, $traceNumber);
    }

    public static function handleCarHttpClient($InquiryResult, $traceNumber): array
    {
        $url = env('QABZINO_CAR_NOT_DETAILS_INQUIRY');

        return self::handleCarHttpClientRequest($url, $InquiryResult, $traceNumber);
    }

    public static function handleCarHttpClientRequest(string $url, $InquiryResult, $traceNumber)
    {

        $qabzinoToken = env('QABZINO_TOKEN');

        $dataRequest = [
            'Identity' => [
                'Token' => $qabzinoToken,
            ],
            'Parameters' => [
                'Alphabet' => (string) $InquiryResult->plaque_alphabet,
                'Left' => (string) $InquiryResult->plaque_left,
                'Mid' => (string) $InquiryResult->plaque_mid,
                'MobileNumber' => $InquiryResult->detail_phone != null && $InquiryResult->detail_phone != "" && $InquiryResult->detail_phone != false && $InquiryResult->detail_phone != 1 && $InquiryResult->detail_phone != 'null' ? $InquiryResult->detail_phone : '09397192230',
                'NationalID' => $InquiryResult->detail_national_id != null && $InquiryResult->detail_national_id != "" && $InquiryResult->detail_national_id != false && $InquiryResult->detail_national_id != 1 && $InquiryResult->detail_national_id != 'null' ? $InquiryResult->detail_national_id : '',
                // 'NationalID' => $InquiryResult->detail_national_id ?? '',
                'Right' => (string) $InquiryResult->plaque_right,
                'TraceNumber' => (string) $traceNumber,
                'WalletIdentifier' => '09397192230',
            ],
        ];

        $response = Http::post($url, $dataRequest);
        $responseData = $response->json();

        // Log::debug($responseData);

        return $responseData;

    }
}
