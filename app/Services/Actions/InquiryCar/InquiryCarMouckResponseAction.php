<?php

namespace App\Services\Actions\InquiryCar;

class InquiryCarMouckResponseAction
{
    public static function handleWithDetails($data)
    {
        return [
            'type' => 'car',
            'with_detail_type' => true,
            'amount' => '2,300,000',
            'bill_id' => '7258466500197',
            'payment_id' => '368025707',
            'complaint_status' => '',
            'complaint_code' => '',
            'plaque' => $data['data']['plaque'],
            'plaque_details' => isset($data['data']['details']) ? $data['data']['details'] : [],
            'details' => [
                [
                    'unique_id' => '3cde042430fceb925f0408dd48302ab4',
                    'amount' => '100,000',
                    'bill_id' => '8727094800290',
                    'city' => 'اصفهان',
                    'date_time' => '10/25/2024 11:03:00',
                    'delivery' => 'دوربین',
                    'has_image' => true,
                    'location' => 'اصفهان دستجرديبعد سيتي سنتر ج ش',
                    'officer_identification_code' => '',
                    'payment_id' => '100024351',
                    'serial_number' => '032587270948',
                    'type' => 'تجاوز از سرعت مجاز تا 10 کيلومتر',
                    'type_code' => '032587270948',
                    'payment_url' => 'https://apilara.liara.run/payment?BillID=7258466500197&PaymentID=368025707',
                ],
                [
                    'unique_id' => '3cde042430fceb925f0408dd48302ab4',
                    'amount' => '200,000',
                    'bill_id' => '6187868700291',
                    'city' => 'اصفهان',
                    'date_time' => '08/09/2024 17:15:00',
                    'delivery' => 'دوربین',
                    'has_image' => true,
                    'location' => 'اصفهان دستجرديبعد سيتي سنتر ج ش',
                    'officer_identification_code' => '',
                    'payment_id' => '100024351',
                    'serial_number' => '032587270948',
                    'type' => 'تجاوز از سرعت مجاز تا 10 کيلومتر',
                    'type_code' => '032587270948',
                    'payment_url' => 'https://apilara.liara.run/payment?BillID=7258466500197&PaymentID=368025707',
                ],
                [
                    'unique_id' => '3cde042430fceb925f0408dd48302ab4',
                    'amount' => '300,000',
                    'bill_id' => '8727094800290',
                    'city' => 'اصفهان',
                    'date_time' => '10/25/2024 11:03:00',
                    'delivery' => 'دوربین',
                    'has_image' => true,
                    'location' => 'اصفهان دستجرديبعد سيتي سنتر ج ش',
                    'officer_identification_code' => '',
                    'payment_id' => '100024351',
                    'serial_number' => '032587270948',
                    'type' => 'تجاوز از سرعت مجاز تا 10 کيلومتر',
                    'type_code' => '032587270948',
                    'payment_url' => 'https://apilara.liara.run/payment?BillID=7258466500197&PaymentID=368025707',
                ],
            ],
            'payment_url' => 'https://apilara.liara.run/payment?BillID=7258466500197&PaymentID=368025707',
            'trace_number' => $data->id,
            'date_inquiry' => shamsiDate($data->created_at),
        ];
    }

    public static function handleNoDetails($data)
    {
        return [
            'type' => 'car',
            'with_detail_type' => false,
            'amount' => '3,600,000',
            'bill_id' => '7258466500197',
            'payment_id' => '368025707',
            'complaint_status' => '-',
            'complaint_code' => '-',
            'plaque' => $data['data']['plaque'],
            'plaque_details' => isset($data['data']['details']) ? $data['data']['details'] : [],
            'payment_url' => 'https://apilara.liara.run/payment?BillID=7258466500197&PaymentID=368025707',
            'trace_number' => $data->id,
            'date_inquiry' => shamsiDate($data->created_at),
        ];
    }
}
