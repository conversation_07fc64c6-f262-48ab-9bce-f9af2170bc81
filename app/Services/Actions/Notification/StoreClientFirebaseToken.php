<?php

namespace App\Services\Actions\Notification;

use App\Models\FirebaseToken;

class StoreClientFirebaseToken
{
    public function handle(array $data): FirebaseToken
    {

        return FirebaseToken::create([
            'app' => $data['app'],
            'agent' => $data['agent'],
            'ip' => $data['ip'],
            'path' => $data['path'],
            'token' => $data['token'],
            'status' => $data['status'],
        ]);
    }
}
