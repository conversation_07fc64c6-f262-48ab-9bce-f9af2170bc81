<?php

namespace App\Services\Actions\InquiryMotor;

use App\Models\InquiryResult;
use App\Models\User;
use Illuminate\Support\Facades\Http;

class InquiryMotorResponseAction
{
    public static function handleMotorWithDetailsHttpClient($InquiryResult, $traceNumber): array
    {
        $url = env('QABZINO_MOTOR_WITH_DETAILS_INQUIRY');

        return self::handleMotorHttpClientRequest($url, $InquiryResult, $traceNumber);
    }

    public static function handleMotorHttpClient($InquiryResult, $traceNumber): array
    {
        $url = env('QABZINO_MOTOR_NOT_DETAILS_INQUIRY');

        return self::handleMotorHttpClientRequest($url, $InquiryResult, $traceNumber);
    }

    public static function handleMotorHttpClientRequest(string $url, $InquiryResult, $traceNumber)
    {

        $app = app('authenticated_application');
        $qabzinoToken = env('QABZINO_TOKEN');

        $dataRequest = [
            'Identity' => [
                'Token' => $qabzinoToken,
            ],
            'Parameters' => [
                'Alphabet' => (string) $InquiryResult->plaque_alphabet,
                'Left' => (int) $InquiryResult->plaque_left,
                'Mid' => (string) $InquiryResult->plaque_mid,
                'MobileNumber' => $InquiryResult->detail_phone != null && $InquiryResult->detail_phone != "" && $InquiryResult->detail_phone != false && $InquiryResult->detail_phone != 1 && $InquiryResult->detail_phone != 'null' ? $InquiryResult->detail_phone : '09397192230',
                'NationalID' => $InquiryResult->detail_national_id != null && $InquiryResult->detail_national_id != "" && $InquiryResult->detail_national_id != false && $InquiryResult->detail_national_id != 1 && $InquiryResult->detail_national_id != 'null' ? $InquiryResult->detail_national_id : '',
                'Right' => (int) $InquiryResult->plaque_right,
                'TraceNumber' => (string) $traceNumber,
                'WalletIdentifier' => (string) $InquiryResult->phone,
            ],
        ];

        // return $dataRequest;

        $response = Http::post($url, $dataRequest);
        $responseData = $response->json();

        // return $responseData;

        $inquiryFind = InquiryResult::where('user_id', auth()->id())
            ->where('source', $app->source)
            ->where('trace_number', $traceNumber)
            ->first();

        if ($inquiryFind) {

            // $this->withdrawMoney();

            $inquiryFind->status = 'success';
            $inquiryFind->result = $responseData;
            $inquiryFind->save();

            // $AMOUNT_PAYMENT_REQUEST_CAR = env('AMOUNT_PAYMENT_REQUEST_CAR');
            // $user = User::whereId(auth()->id())->where('source', $app->source)->first();
            // auth()->user()->balance = str_replace(',', '', auth()->user()->balance) - str_replace(',', '', $AMOUNT_PAYMENT_REQUEST_CAR);

        }

        return $responseData;
    }
}
