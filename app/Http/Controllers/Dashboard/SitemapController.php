<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Jobs\GenerateSitemapJob;
use App\Services\FtpSitemapService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Exception;

class SitemapController extends Controller
{
    /**
     * Display sitemap management page
     */
    public function index()
    {
        $sitemapFiles = $this->getSitemapFilesInfo();
        
        return view('dashboard.sitemap.index', compact('sitemapFiles'));
    }

    /**
     * Generate all sitemap files manually
     */
    public function generate()
    {
        try {
            GenerateSitemapJob::dispatch();
            
            return response()->json([
                'success' => true,
                'message' => 'تولید فایل‌های سایت‌مپ در صف قرار گرفت'
            ]);
            
        } catch (Exception $e) {
            Log::error('خطا در تولید دستی سایت‌مپ: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'خطا در تولید فایل‌های سایت‌مپ'
            ], 500);
        }
    }

    /**
     * Upload sitemap files to FTP
     */
    public function uploadToFtp()
    {
        try {
            if (!env('FTP_SITEMAP_HOST')) {
                return response()->json([
                    'success' => false,
                    'message' => 'تنظیمات FTP مشخص نشده است'
                ], 400);
            }

            $ftpService = new FtpSitemapService();
            $uploadedFiles = $ftpService->uploadSitemapFiles();
            
            return response()->json([
                'success' => true,
                'message' => 'فایل‌ها با موفقیت آپلود شدند',
                'files' => $uploadedFiles
            ]);
            
        } catch (Exception $e) {
            Log::error('خطا در آپلود FTP: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'خطا در آپلود فایل‌ها: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test FTP connection
     */
    public function testFtp()
    {
        try {
            if (!env('FTP_SITEMAP_HOST')) {
                return response()->json([
                    'success' => false,
                    'message' => 'تنظیمات FTP مشخص نشده است'
                ], 400);
            }

            $ftpService = new FtpSitemapService();
            $result = $ftpService->testConnection();
            
            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => 'اتصال FTP موفقیت‌آمیز بود'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'خطا در اتصال FTP'
                ], 500);
            }
            
        } catch (Exception $e) {
            Log::error('خطا در تست FTP: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'خطا در تست اتصال: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get sitemap files information
     */
    private function getSitemapFilesInfo()
    {
        $files = [
            'sitemap.xml' => public_path('sitemap.xml'),
            'sitemaps/articles.xml' => public_path('sitemaps/articles.xml'),
            'sitemaps/services.xml' => public_path('sitemaps/services.xml')
        ];

        $filesInfo = [];
        
        foreach ($files as $name => $path) {
            $filesInfo[$name] = [
                'exists' => file_exists($path),
                'size' => file_exists($path) ? filesize($path) : 0,
                'modified' => file_exists($path) ? filemtime($path) : null,
                'url' => url($name)
            ];
        }

        return $filesInfo;
    }

    /**
     * Download sitemap file
     */
    public function download($filename)
    {
        $allowedFiles = ['sitemap.xml', 'articles.xml', 'services.xml'];
        
        if (!in_array($filename, $allowedFiles)) {
            abort(404);
        }

        $path = $filename === 'sitemap.xml' 
            ? public_path('sitemap.xml')
            : public_path("sitemaps/{$filename}");

        if (!file_exists($path)) {
            abort(404, 'فایل سایت‌مپ وجود ندارد');
        }

        return response()->download($path);
    }

    /**
     * View sitemap file content
     */
    public function view($filename)
    {
        $allowedFiles = ['sitemap.xml', 'articles.xml', 'services.xml'];
        
        if (!in_array($filename, $allowedFiles)) {
            abort(404);
        }

        $path = $filename === 'sitemap.xml' 
            ? public_path('sitemap.xml')
            : public_path("sitemaps/{$filename}");

        if (!file_exists($path)) {
            abort(404, 'فایل سایت‌مپ وجود ندارد');
        }

        $content = file_get_contents($path);
        
        return response($content, 200, [
            'Content-Type' => 'application/xml; charset=utf-8'
        ]);
    }
}
