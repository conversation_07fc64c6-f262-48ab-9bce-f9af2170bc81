<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Symfony\Component\HttpFoundation\Response;

class BaseController extends Controller
{
    /**
     * success response method.
     *
     * @return \Illuminate\Http\Response
     */
    public function sendResponse($result, $message, $status = Response::HTTP_OK)
    {
        $response = [
            'success' => true,
            'data' => $message,
            'status' => $status,
        ];

        return response()->json($response, $status);
    }

    /**
     * return error response.
     *
     * @return \Illuminate\Http\Response
     */
    public function sendError($error, $errorMessages = [], $code = Response::HTTP_NOT_FOUND)
    {
        $response = [
            'success' => false,
        ];

        if (! empty($errorMessages)) {
            $response['data'] = $errorMessages;
        }

        $response['status'] = $code;

        return response()->json($response, $code);
    }
}
