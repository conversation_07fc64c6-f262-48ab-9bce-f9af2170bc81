<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Api\BaseController;
use App\Models\UtmConvertion;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;

class UtmController extends BaseController
{
    // public function __construct()
    // {
    //     $this->middleware('auth:api');
    // }

    public function index(Request $request)
    {

        $user = auth('api')->user();
        $origin = $request->headers->get('origin') ?? $request->headers->get('referer') ?? '';
        $utmUrl = $request->utm ?? '';
        $utmParams = [];

        $cookiesHeader = $request->headers->get('UTM_DATA');
        $cookies = json_decode($cookiesHeader, true);

        if (isset($cookies) && $cookies != null) {
            foreach ($cookies as $cookieName => $cookieValue) {

                $page = $request->input('page');

                if (! Str::startsWith($page, 'http://localhost')) {
                    // parse_str($cookieValue, $utmParams);

                    UtmConvertion::create([
                        'user_id' => $user?->id,
                        'u_trace' => $request->u_trace ?? '',
                        'phone' => $user?->phone,
                        'type' => $user ? 'user' : 'guest',
                        'page' => $request->input('page', ''),
                        'origin' => $origin,
                        'ip' => $request->ip(),
                        'agent' => $request->userAgent(),
                        'cookie_key' => $cookieName,
                        'cookie_value' => $cookieValue,
                        'description' => 'ثبت UTM از UTM_DATA header',
                    ]);

                }
            }

            // return response()->json([
            //     'message' => 'UTM Save',
            // ]);

        }

        if ($utmUrl && str_contains($utmUrl, '?')) {
            $queryString = parse_url($utmUrl, PHP_URL_QUERY);
            parse_str($queryString, $utmParams);

            $cookieName = collect($utmParams)
                ->map(fn ($val, $key) => $key.'_'.str_replace(' ', '_', $val))
                ->implode('_');

            $cookieValue = http_build_query($utmParams, '', '&', PHP_QUERY_RFC3986);
            $cookie = cookie($cookieName, $cookieValue, 2880);

            UtmConvertion::create([
                'user_id' => $user?->id,
                'u_trace' => $request->u_trace ?? '',
                'phone' => $user?->phone,
                'type' => $user ? 'user' : 'guest',
                'page' => $request->page ?? '',
                'origin' => $origin,
                'ip' => $request->ip(),
                'agent' => $request->userAgent(),
                'cookie_key' => 'query',
                'cookie_value' => $utmUrl,
                'description' => 'ثبت UTM از query',
            ]);

            return $this->sendResponse('utm', [
                'message' => 'UTM saved from query',
                'utm' => [
                    'utm_'.$cookieName => $cookieValue,
                ],
            ], Response::HTTP_OK)->cookie('utm_'.$cookieName, $cookieValue, 1440, null, null, config('app.env') === 'production', true, 'Strict');

        }

        return response()->json([
            'message' => 'not found UTM',
        ]);

    }
}
