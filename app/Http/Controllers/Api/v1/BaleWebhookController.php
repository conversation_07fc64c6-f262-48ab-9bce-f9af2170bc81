<?php

namespace App\Http\Controllers\Api\v1;
use App\Http\Controllers\Api\BaseController;
use EFive\Bale\Laravel\Facades\Bale;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class BaleWebhookController extends BaseController
{
    public function handle(Request $req)
    {
        $u = $req->input('message');
        $chatId = $u['chat']['id'] ?? null;
        $text = trim($u['text'] ?? '');

        if (!$chatId) {
            return response()->json();
        }

        $state = Cache::get("state:$chatId", 'menu');

        switch ($state) {
            case 'menu':
                return $this->sendMenu($chatId);
            case 'await_car_plate':
                Cache::forget("state:$chatId");

                return $this->processCarPlate($chatId, $text);
            case 'await_motor_plate':
                Cache::forget("state:$chatId");

                return $this->processMotorPlate($chatId, $text);
            case 'await_topup_amount':
                Cache::forget("state:$chatId");

                return $this->processTopUpAmount($chatId, $text);
            default:
                return $this->sendMenu($chatId);
        }
    }

    protected function sendMenu($chatId)
    {
        Cache::put("state:$chatId", 'menu');
        Bale::sendMessage([
            'chat_id' => $chatId,
            'text' => 'لطفاً یکی را انتخاب کنید:',
            'keyboard' => [
                [['text' => 'خلافی خودرو'], ['text' => 'خلافی موتور']],
                [['text' => 'شارژ حساب'], ['text' => 'موجودی']],
                [['text' => 'تاریخچه استعلام‌ها']],
            ],
        ]);

        return response()->json();
    }

    protected function processCarPlate($chatId, $plate)
    {
        if (preg_match('/^\d{2}-\d{3}-و-\d{2}$/', $plate)) {
            $msg = "پلاک خودرو معتبر است: $plate";
            // ارسال نمونه عکس
            Bale::sendPhoto([
                'chat_id' => $chatId,
                'photo' => 'https://example.com/sample_car_plate.jpg',
                'caption' => 'لطفاً پلاک خود را به همین شکل وارد کنید.',
            ]);
        } else {
            $msg = 'فرمت پلاک خودرو نامعتبر است. لطفاً به فرمت 24-183-و-18 وارد کنید.';
        }
        Bale::sendMessage(['chat_id' => $chatId, 'text' => $msg]);

        return response()->json();
    }

    protected function processMotorPlate($chatId, $plate)
    {
        if (preg_match('/^\d{5}-\d{2}$/', $plate)) {
            $msg = "پلاک موتور معتبر است: $plate";
            // ارسال نمونه عکس
            Bale::sendPhoto([
                'chat_id' => $chatId,
                'photo' => 'https://example.com/sample_motor_plate.jpg',
                'caption' => 'لطفاً پلاک موتور را به همین شکل وارد کنید.',
            ]);
        } else {
            $msg = 'فرمت پلاک موتور نامعتبر است. لطفاً به فرمت 19326-24 وارد کنید.';
        }
        Bale::sendMessage(['chat_id' => $chatId, 'text' => $msg]);

        return response()->json();
    }

    protected function processTopUpAmount($chatId, $text)
    {
        if (is_numeric($text) && $text >= 100000 && $text <= 1000000) {
            $msg = "درخواست شارژ به مبلغ: $text تومان با موفقیت ثبت شد.";
        } else {
            $msg = 'عدد نامعتبر است! لطفاً مبلغ بین 100,000 تا 1,000,000 تومان وارد کن.';
            // دوباره در انتظار
            Cache::put("state:$chatId", 'await_topup_amount');
            Bale::sendMessage(['chat_id' => $chatId, 'text' => $msg]);

            return response()->json();
        }
        Bale::sendMessage(['chat_id' => $chatId, 'text' => $msg]);

        return response()->json();
    }

    // ... بررسی موجودی و تاریخچه مشابه ...
}
