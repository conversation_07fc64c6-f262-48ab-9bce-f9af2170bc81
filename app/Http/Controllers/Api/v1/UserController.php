<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Api\BaseController;
use App\Http\Resources\UserResource;
use App\Models\User;
use Dan<PERSON><PERSON><PERSON>\LivewireRateLimiting\WithRateLimiting;
use Illuminate\Http\Request;

class UserController extends BaseController
{
    use WithRateLimiting;

    public function __construct()
    {
        $this->middleware('auth:api');
    }

    /**
     * @authenticated
     */
    public function index(Request $request)
    {
        $app = app('authenticated_application');
        $user = User::where('phone', auth()->user()->phone)->where('source', $app->source)->first();

        return $this->sendResponse('پروفایل کاربر', new UserResource($user));

    }
}
