<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Api\BaseController;
use App\Http\Resources\NotificationResource;
use App\Services\Actions\Notification\StoreClientFirebaseToken;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

class PushNotificationController extends BaseController
{
    public function storeClientToken(Request $request)
    {
        try {
            $data = $request->validate([
                'path' => 'required',
                'token' => 'nullable|string',
                'app' => 'required',
                'status' => 'required',
                'ip' => 'nullable',
                'agent' => 'nullable',
            ]);

            $data['ip'] = $data['ip'] ?? $request->ip();

            $data['agent'] = $data['agent'] ?? $request->userAgent();

            $log = (new StoreClientFirebaseToken)->handle($data);

            return $this->sendResponse('Token stored successfully', new NotificationResource($log));

        } catch (ValidationException $e) {

            return $this->sendError('خطای اعتبارسنجی', [
                'message' => 'لطفاً موارد زیر را اصلاح کنید.',
                'errors' => $e->errors(),
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }
    }
}
