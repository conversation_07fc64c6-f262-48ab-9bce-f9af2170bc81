<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Api\BaseController;
use App\Http\Resources\CategoriesResource;
use App\Repositories\Interfaces\CategoryRepositoryInterface;

class CategoryController extends BaseController
{
    protected $categoryRepo;

    public function __construct(CategoryRepositoryInterface $categoryRepo)
    {
        $this->categoryRepo = $categoryRepo;
    }

    public function index()
    {
        $categories = $this->categoryRepo->getAllWithArticles('khodrox', true);

        return $this->sendResponse('نتیجه درخواست', CategoriesResource::collection($categories));
    }
}
