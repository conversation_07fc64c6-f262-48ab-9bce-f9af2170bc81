<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Api\BaseController;
use App\Http\Resources\InquiryHistory;
use App\Models\InquiryResult;
use App\Traits\InquiryTrait;
use <PERSON><PERSON><PERSON><PERSON>\LivewireRateLimiting\WithRateLimiting;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

class InquiryController extends BaseController
{
    use InquiryTrait;
    use WithRateLimiting;

    public function __construct()
    {
        $this->middleware('auth:api');

    }

    /**
     * @authenticated
     */
    public function index(Request $request, $type)
    {

        $app = app('authenticated_application');
        $user = auth()->id();

        if (isset($user)) {
            try {

                $data = $this->validateInquiry($request, $type);

            } catch (ValidationException $e) {

                return $this->sendError('خطای اعتبارسنجی', [
                    'message' => 'لطفاً موارد زیر را اصلاح کنید.',
                    'errors' => $e->errors(),
                ], Response::HTTP_UNPROCESSABLE_ENTITY);
            }

            $result = $this->checkInquiryDatabase($data, $type, $app, $request);

            if (isset($result)) {
                return $result;
            }

            return $this->inquiryStart($type, $request, $data);
        }

        return $this->sendError('خلافی', ['message' => 'شما به این بخش دسترسی ندارید لطفاْ وارد شوید'], Response::HTTP_UNAUTHORIZED);

    }

    /**
     * @authenticated
     */
    public function show(string $tranceNumber)
    {

        // $app = app('authenticated_application');
        $result = InquiryResult::where('id', $tranceNumber)
            // ->where('source', $app->source)
            // ->where('user_id', auth()->id())
            ->where('status', 'success')
            // ->where('result', '!=', null)
            ->latest()
            ->first();

        if ($result) {
            if ($result->type == 'car') {

                return $this->showResultWithTraceCar($result);
            }

            return $this->showResultWithTraceMotor($result);

        }

        return $this->sendError('خلافی', ['message' => 'اطلاعات در سرویس یافت نگردید. میتوانید درخواست خود را مجدد بفرستین'], Response::HTTP_EXPECTATION_FAILED);
    }

    /**
     * @authenticated
     */
    public function view()
    {
        // $AMOUNT_PAYMENT_REQUEST_CAR = (float) str_replace(',', '', env('AMOUNT_PAYMENT_REQUEST_CAR'));
        // $USER_BALANCE = (float) str_replace(',', '', auth()->user()->balance);

        // if ($USER_BALANCE <= $AMOUNT_PAYMENT_REQUEST_CAR) {
        //     return $this->sendError('خلافی', ['message' => 'موجودی شما برای دریافت استعلام خلافی کافی نمی باشد'], Response::HTTP_PAYMENT_REQUIRED);
        // }

        $app = app('authenticated_application');
        // $HistoryInquiryResult = InquiryResult::select('type', 'detail_phone', 'detail_national_id', 'status', 'created_at', 'data.plaque', 'source')->where('status', 'success')->latest()->get();
        $HistoryInquiryResult = InquiryResult::select('type', 'detail_phone', 'detail_national_id', 'status', 'created_at', 'data.plaque', 'source')
            ->where('source', $app->source)->where('user_id', auth()->id())
            ->where('status', 'success')
            ->whereIn('type', ['car', 'motor'])
            ->latest()->get();

        return $this->sendResponse(
            'نتیجه استعلام خلافی',
            InquiryHistory::collection($HistoryInquiryResult),
        );
    }
}
