<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Api\BaseController;
use App\Services\Auth\AuthServiceInterface;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class AuthController extends BaseController
{
    public function __construct(protected AuthServiceInterface $authService)
    {
        $this->middleware('auth:api', ['except' => ['auth', 'verification']]);
    }

    public function auth(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|regex:/^09[0-9]{9}$/',
        ]);

        if ($validator->fails()) {
            return $this->sendError('خطای اعتبارسنجی', ['message' => 'شماره موبایل معتبر نیست']);
        }

        $result = $this->authService->authenticate($request);

        return $this->sendResponse('ارسال کد اعتبارسنجی', [
            'message' => 'کد ارسال شد',
            'token' => $result['token'],
            'expire_time' => $result['expire_time'],
        ]);
    }

    public function verification(Request $request)
    {
        $result = $this->authService->verify($request);

        if (isset($result['error'])) {
            if ($result['error'] === 'expired') {
                return $this->sendError('خطا', ['message' => 'توکن منقضی شده است']);
            }

            return $this->sendError('خطا', ['message' => 'اطلاعات اشتباه است']);
        }

        return $this->sendResponse('ورود موفق', $result)->cookie('access_token', $result['access_token'], 1440, null, null, config('app.env') === 'production', true, 'Strict');
    }

    public function logout()
    {
        $this->authService->logout();

        return $this->sendResponse('خروج موفق', ['message' => 'با موفقیت خارج شدید']);
    }
}
