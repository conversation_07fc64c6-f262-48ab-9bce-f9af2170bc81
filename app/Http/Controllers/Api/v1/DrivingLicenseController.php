<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Api\BaseController;
use App\Http\Controllers\Controller;
use App\Http\Resources\DrivingLicenseStatusResource;
use App\Http\Resources\LicenseStatusResource;
use App\Models\InquiryResult;
use App\Models\ServiceHealthLog;
use App\Models\User;
use App\Notifications\Channels\BaleChannel;
use App\Notifications\QabzinoServiceDown;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Notification;

class DrivingLicenseController extends BaseController
{
    public function __construct()
    {
        $this->middleware('auth:api');
    }


    public function show($traceNumber)
    {

        // return $traceNumber;
        $user = auth()->id();

        if (isset($user)) {

            $Inquiry = InquiryResult::whereId($traceNumber)->first();

            if ($Inquiry) {

                if (!empty($Inquiry->result['Parameters']['Licenses'])) {
                    $licenses = DrivingLicenseStatusResource::collection($Inquiry->result['Parameters']['Licenses']);

                    return $this->sendResponse(
                        'استعلام وضعیت گواهینامه رانندگی',
                        [
                            'message' => $Inquiry->result['Status']['Description'],
                            'traceNumber' => $traceNumber,
                            'result' => $licenses,
                        ],
                        Response::HTTP_OK
                    );
                }

                return $this->sendError(
                    $Inquiry->result['Status']['Description'],
                    [
                        // 'traceNumber' => $traceNumber,
                        'result' => [],
                    ],
                    Response::HTTP_UNPROCESSABLE_ENTITY
                );

            }

            return $this->sendError(
                'اطلاعاتی برای این شماره رهگیری یافت نشد.',
                [
                    // 'traceNumber' => $traceNumber,
                    'result' => [],
                ],
                Response::HTTP_NOT_FOUND
            );


        }

        return $this->sendError('استعلام وضعیت گواهینامه رانندگی', ['message' => 'شما به این بخش دسترسی ندارید لطفاْ وارد شوید'], Response::HTTP_UNAUTHORIZED);

    }

    public function store(Request $request)
    {
        $app = app('authenticated_application');
        $user = auth()->user();

        if (isset($user)) {
            try {

                $data = $request->validate([
                    'details' => 'nullable|array',
                    'details.phone' => ['nullable', 'regex:/^09\d{9}$/'], // must start with 09 and be 11 digits
                    'details.national_id' => ['nullable', 'digits:10'],   // must be exactly 10 digits
                ]);


            } catch (ValidationException $e) {

                return $this->sendError('خطای اعتبارسنجی', [
                    'message' => 'لطفاً موارد زیر را اصلاح کنید.',
                    'errors' => $e->errors(),
                ], Response::HTTP_UNPROCESSABLE_ENTITY);
            }

            $oldInquiry = InquiryResult::where('mobile_number', $data['details']['phone'])
                ->where('type', 'drivingLicense')
                ->where('phone', $user->phone)
                ->where('national_id', $data['details']['national_id'])
                ->whereNotNull('result.Parameters')
                ->where('created_at', '>=', now()->subDay())
                ->first();

            try {

                $statusCode = $oldInquiry->result['Status']['Code'] ?? null;
                if (in_array($statusCode, ['GM5031', 'GM5038', 'G00003'])) {

                } else {
                    if ($oldInquiry) {
                        return $this->sendResponse('استعلام وضعیت کارت و سند خودرو', [
                            'message' => $oldInquiry->result['Status']['Description'],
                            'traceNumber' => $oldInquiry->id,
                            // 'duplicate_request' => true,
                            // 'result' => new LicenseStatusResource($oldInquiry->result['Parameters']),
                            // 'repetitive' => true
                        ], Response::HTTP_OK);
                    }

                }
            } catch (\Exception $exception) {

            }

            $AMOUNT_NEGATIVE_POINTS_REQUEST_CAR = (int) env('AMOUNT_NEGATIVE_POINTS_REQUEST_CAR');

            $USER_AFTER_BALANCE = (float) str_replace(',', '', $user->balance);

            $userKhalafiyarTransfer = getTransferAppSetting();

            if ($userKhalafiyarTransfer) {
                $USER_AFTER_BALANCE = $USER_AFTER_BALANCE + getSourceBalance($user->phone, 'khodroyar') ?? 0;
            }

            if ($USER_AFTER_BALANCE < $AMOUNT_NEGATIVE_POINTS_REQUEST_CAR) {
                return $this->sendError('خلافی', [
                    'message' => 'موجودی شما کافی نمی باشد',
                    'amount' => $AMOUNT_NEGATIVE_POINTS_REQUEST_CAR,
                    'balance' => $USER_AFTER_BALANCE,
                    'unit' => 'تومان',
                ], Response::HTTP_PAYMENT_REQUIRED);
            }

            $traceNumber = Crypt::encrypt('track_number:' . $user->phone . '_license:' . rand(10000, 99999) . 'created_at:' . now());
            $InquiryResult = InquiryResult::create([
                'user_id' => auth()->id(),
                'balance' => $USER_AFTER_BALANCE,
                'balance_after' => $USER_AFTER_BALANCE,
                'source' => $app->source,
                'trace_number' => $traceNumber,
                'phone' => $user->phone,
                'type' => 'drivingLicense',
                'mobile_number' => $data['details']['phone'],
                'national_id' => $data['details']['national_id'],
                'result' => null,
                'status' => 'pending',
                'agent' => $request->userAgent(),
            ]);

            $qabzinoToken = env('QABZINO_TOKEN');
            $url = env('NAJI_SERVICE_DRIVING_LICENSE_STATUS_INQUIRY');

            $dataRequest = [
                'Identity' => [
                    'Token' => $qabzinoToken,
                ],
                'Parameters' => [
                    'MobileNumber' => (string) ($data['details']['phone'] ?? ''),
                    'NationalID' => (string) ($data['details']['national_id'] ?? ''),
                    'TraceNumber' => (string) $InquiryResult->id,
                ],
            ];


            $response = Http::post($url, $dataRequest);
            $result = $response->json();
            $InquiryResult->result = $result;
            $InquiryResult->status = 'success';
            $InquiryResult->save();

            $this->sendQabzinoServiceDown($result);

            if ($result['Status']['Code'] == 'G00000' || $result['Status']['Code'] == 'GM5031') {

                $this->withdrawMoney($AMOUNT_NEGATIVE_POINTS_REQUEST_CAR, $USER_AFTER_BALANCE);

                $InquiryResult->balance_after = $USER_AFTER_BALANCE - $AMOUNT_NEGATIVE_POINTS_REQUEST_CAR;
                $InquiryResult->save();

                return $this->sendResponse('استعلام وضعیت گواهینامه رانندگی', [
                    'message' => $result['Status']['Description'],
                    // 'duplicate_request' => false,
                    'traceNumber' => $InquiryResult->id,
                    // 'result' => new LicenseStatusResource($result['Parameters']),
                    // 'repetitive' => false
                ], Response::HTTP_OK);
            }


            return $this->sendError('استعلام وضعیت گواهینامه رانندگی', [
                'message' => $result['Status']['Description'],
                // 'traceNumber' => $InquiryResult->id,
                'result' => [],
            ], Response::HTTP_UNPROCESSABLE_ENTITY);



        }

        return $this->sendError('خلافی', ['message' => 'شما به این بخش دسترسی ندارید لطفاْ وارد شوید'], Response::HTTP_UNAUTHORIZED);
    }

    private function sendQabzinoServiceDown($result)
    {
        try {
            if (isset($result['Status']['Code']) && $result['Status']['Code'] == 'GM5004') {

                $phone = '09334332338';

                Cache::put('enabled_qabzino_service_down_job', true);

                $message = '🔴 قبضینو - سرویس استعلام وضعیت گواهینامه رانندگی با خطای «امکان برقراری ارتباط با سرویس دهنده وجود ندارد» مواجه شد' . ' - تاریخ ' . dateTimeToday();

                ServiceHealthLog::create([
                    'service' => 'qabzino',
                    'type' => 'disconnect',
                    'phone' => $phone,
                    'message' => $message,
                ]);

                Notification::route(BaleChannel::class, 'any_identifier')
                    ->notify(new QabzinoServiceDown($message));
            }
        } catch (\Exception $e) {

        }
    }

    private function withdrawMoney(float $AMOUNT_PAYMENT_REQUEST_CAR, float $USER_BALANCE)
    {
        $userKhalafiyarTransfer = getTransferAppSetting();

        if ($userKhalafiyarTransfer) {

            $userKhalafiuarBalance = getSourceBalance(auth()->user()->phone, 'khodroyar');

            if ($userKhalafiuarBalance < $AMOUNT_PAYMENT_REQUEST_CAR) {

                if ($USER_BALANCE < $AMOUNT_PAYMENT_REQUEST_CAR) {
                    return $this->sendError('خلافی', ['message' => 'موجودی شما برای دریافت استعلام خلافی کافی نمی باشد'], Response::HTTP_PAYMENT_REQUIRED);
                } elseif ($USER_BALANCE >= $AMOUNT_PAYMENT_REQUEST_CAR) {
                    $RESULT = $USER_BALANCE - $AMOUNT_PAYMENT_REQUEST_CAR;
                    User::whereId(auth()->id())->update([
                        'balance' => (float) $RESULT,
                    ]);

                    return true;
                }

                return $this->sendError('خلافی', ['message' => 'موجودی شما برای دریافت استعلام خلافی کافی نمی باشد'], Response::HTTP_PAYMENT_REQUIRED);

            } else {

                if ($userKhalafiuarBalance > $AMOUNT_PAYMENT_REQUEST_CAR) {
                    $RESULT = $userKhalafiuarBalance - $AMOUNT_PAYMENT_REQUEST_CAR;
                    User::where('phone', auth()->user()->phone)
                        ->where('source', 'khodroyar')->update([
                                'balance' => (float) $RESULT,
                            ]);

                    return true;
                }

                return $this->sendError('خلافی', ['message' => 'موجودی شما برای دریافت استعلام خلافی کافی نمی باشد'], Response::HTTP_PAYMENT_REQUIRED);
            }

        } else {
            if ($USER_BALANCE < $AMOUNT_PAYMENT_REQUEST_CAR) {
                return $this->sendError('خلافی', ['message' => 'موجودی شما برای دریافت استعلام خلافی کافی نمی باشد'], Response::HTTP_PAYMENT_REQUIRED);
            }

            $RESULT = $USER_BALANCE - $AMOUNT_PAYMENT_REQUEST_CAR;
            User::whereId(auth()->id())->update([
                'balance' => (float) $RESULT,
            ]);

            return true;
        }

    }

}
