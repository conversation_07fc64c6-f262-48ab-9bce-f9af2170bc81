<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Api\BaseController;
use App\Models\InquiryPictureResult;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

class InquiryPictureController extends BaseController
{
    public function __construct()
    {
        $this->middleware('auth:api');

    }


    public function show(Request $request)
    {
        $user = auth()->user();
        $request->validate(['inquiryId' => 'required']);
        $inquiryId = $request->get('inquiryId');
        $plate = $user->plate ?? $user->phone;
        $folderPath = "traffic_fines/{$plate}/";


        $fileNames = [
            'PlateImageUrl' => $inquiryId . '_plate.jpg',
            'VehicleImageUrl' => $inquiryId . '_vehicle.jpg',
        ];

        $allExist = true;
        foreach ($fileNames as $file) {
            if (!Storage::disk('public')->exists($folderPath . $file)) {
                $allExist = false;
                break;
            }
        }

        if ($allExist) {

            $responseData = [
                'Parameters' => [
                    'PlateImageUrl' => "https://dl.khodrox.com/storage/{$folderPath}{$fileNames['PlateImageUrl']}",
                    'VehicleImageUrl' => "https://dl.khodrox.com/storage/{$folderPath}{$fileNames['VehicleImageUrl']}",
                ],
                'Status' => [
                    'Code' => 'G00000',
                    'Description' => 'درخواست با موفقیت انجام شد.',
                ],
            ];

            return $this->sendResponse('دریافت تصاویر تخلفات', $responseData);
        }


        $qabzinoToken = env('QABZINO_TOKEN');
        $url = env('TRAFFIC_FINES_INQUIRY_GET_IMAGE');
        $dataRequest = [
            'Identity' => ['Token' => $qabzinoToken],
            'Parameters' => [
                'UniqueID' => $inquiryId,
                'WalletIdentifier' => $user->phone,
            ],
        ];

        $response = Http::post($url, $dataRequest);
        $responseData = $response->json();

        if (empty($responseData['Parameters']) || ($responseData['Status']['Code'] ?? '') === 'GM5041') {
            return $this->sendError('دریافت تصاویر تخلفات', $responseData, 404);
        }


        $AMOUNT_PAYMENT_REQUEST_IMAGE = (float) str_replace([',', ' '], '', env('AMOUNT_PAYMENT_REQUEST_IMAGE'));
        $USER_BALANCE = (float) str_replace([',', ' '], '', $user->balance);

        if ($USER_BALANCE < $AMOUNT_PAYMENT_REQUEST_IMAGE) {
            return $this->sendError('خلافی', ['message' => 'موجودی کافی نیست'], 402);
        }
        $this->withdrawMoney($AMOUNT_PAYMENT_REQUEST_IMAGE, $USER_BALANCE);


        foreach (['PlateImageUrl', 'VehicleImageUrl'] as $key) {
            if (!empty($responseData['Parameters'][$key])) {
                $imageContents = Http::get($responseData['Parameters'][$key])->body();
                Storage::disk('public')->put($folderPath . $fileNames[$key], $imageContents);
            }
        }


        $responseData['Parameters'] = [
            'PlateImageUrl' => "https://dl.khodrox.com/storage/{$folderPath}{$fileNames['PlateImageUrl']}",
            'VehicleImageUrl' => "https://dl.khodrox.com/storage/{$folderPath}{$fileNames['VehicleImageUrl']}",
        ];

        return $this->sendResponse('دریافت تصاویر تخلفات', $responseData);
    }


    private function withdrawMoney(float $AMOUNT_PAYMENT_REQUEST_IMAGE, float $USER_BALANCE)
    {
        if ($USER_BALANCE < $AMOUNT_PAYMENT_REQUEST_IMAGE) {
            return false;
        }

        $RESULT = $USER_BALANCE - $AMOUNT_PAYMENT_REQUEST_IMAGE;
        User::whereId(auth()->id())->update([
            'balance' => (float) $RESULT,
        ]);

        return true;
    }
}
