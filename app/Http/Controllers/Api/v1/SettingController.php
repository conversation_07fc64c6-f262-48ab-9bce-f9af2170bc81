<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Symfony\Component\HttpFoundation\Response;

class SettingController extends Controller
{
    public function index()
    {

        $services = Setting::pluck('value', 'key')->toArray();

        return response()->json([
            'success' => true,
            'data' => [
                'services' => $services,
            ],
            'status' => Response::HTTP_OK,
        ], Response::HTTP_OK);

    }
}
