<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Api\BaseController;
use App\Services\TransferService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Validator;

class DepositController extends BaseController
{
    public function __construct(protected TransferService $transferService) {}

    public function transferUserDeposit(Request $request)
    {
        $validator = Validator::make($request->all(), [
            // 'withDetails' => 'required',
            // 'plaque' => 'required',
            // 'userId' => 'required',
            'token' => 'required',
        ]);

        if ($validator->fails()) {
            return $this->sendError('خطای اعتبارسنجی', [
                'message' => 'لطفاً موارد زیر را اصلاح کنید.',
                'errors' => $validator->errors(),
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        $app = app('authenticated_application');

        [$success, $result] = $this->transferService->handleTransfer($request, $app);

        if ($success) {
            return $this->sendResponse('اطلاعات درست می باشد', $result)
                ->cookie('access_token', $result['access_token'], 1440, null, null, config('app.env') === 'production', true, 'Strict');
        }

        return $this->sendError('خلافی', ['message' => $result], Response::HTTP_UNAUTHORIZED);
    }
}
