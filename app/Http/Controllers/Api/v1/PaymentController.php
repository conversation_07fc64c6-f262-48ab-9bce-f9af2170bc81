<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Api\BaseController;
use App\Models\PaymentPending;
use App\Models\TransactionUserQabzino;
use App\Models\User;
use App\Models\UtmConvertion;
use App\Services\Gate\Zarinpal;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

class PaymentController extends BaseController
{
    public function __construct()
    {
        $this->middleware('auth:api')->except('verfication');
    }

    /**
     * @authenticated
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $app = app('authenticated_application');
        try {
            $request->validate([
                'amount' => 'required|numeric|min:100000|max:1000000',
                'callback_url' => 'required|string',

            ]);

        } catch (ValidationException $e) {

            $errors = $e->errors();

            $translatedErrors = [];
            foreach ($errors as $field => $messages) {
                $translatedErrors[$field] = array_map(function ($message) {
                    return $this->translateErrorMessage($message);
                }, $messages);
            }

            return $this->sendError('خطای اعتبارسنجی', [
                'message' => 'مبلغ واریزی بین 100 هزار تومان تا 1 میلیون تومان می باشد',
                'errors' => $translatedErrors,
            ], Response::HTTP_UNPROCESSABLE_ENTITY);

        }

        $callbackUrl = $request->input('callback_url');

        $pluqe = $request->input('plaque', '');

        $message = getService($app->source)['text'] ?? 'نامشخص';

        try {

            $utm = UtmConvertion::where('phone', auth()->user()->phone)
                ->where('cookie_key', 'like', 'utm_utm_source%')
                ->latest()->first();

            if (isset($utm) && $utm != null) {

                parse_str($utm->cookie_value, $utmParsed);

                $message .= ' | UTM_Trace = ' . ($utm?->u_trace ?? '');

                $message .= ' | UTM_SOURCE = ' . ($utmParsed['utm_source'] ?? '');

                $message .= ' | UTM_MEDIUM = ' . ($utmParsed['utm_medium'] ?? '');

                $message .= ' | UTM_TERM = ' . ($utmParsed['utm_term'] ?? '');

            }
        } catch (\Exception $e) {
            Log::error($e->getMessage());
        }

        $dataResult = [
            'user_id' => auth()->id(),
            'amount' => $request->input('amount'),
            'type' => $request->input('type', ''),
            'details' => $request->input('details', ''),
            'plaque' => $request->input('plaque', ''),
            'mobile' => auth()->user()->phone,
            'email' => null,
            'source' => $app->source,
            'callback_url' => $callbackUrl,
            'description' => $message,

        ];

        $zarinpal = new Zarinpal;
        $response = $zarinpal->handle($request, $dataResult);

        if ($response['status'] == 100) {
            return $this->sendResponse(
                'نتیجه درخواست',
                [
                    'message' => 'ایجاد لینک پرداخت موفقیت آمیز بود',
                    'payment_url' => $response['payment_url'],
                    'callback_url' => $dataResult['callback_url'],
                    'amount' => formatMoney($dataResult['amount']),
                ]
            );
        }

        return $this->sendError('خطای اعتبارسنجی', [
            'message' => 'لطفاً موارد زیر را اصلاح کنید.',
            'errors' => $response['errors'],
        ], Response::HTTP_UNPROCESSABLE_ENTITY);
    }

    /**
     * @authenticated
     */
    public function verfication(Request $request)
    {
        $app = app('authenticated_application');
        $payment = PaymentPending::where('authority', $request->get('Authority'))->first();

        if (!$request->get('Authority')) {
            return $this->sendError(
                'نتیجه درخواست',
                [
                    'message' => 'خطا در شناسایی کلید بازگشتی بانک',
                    'type' => null,
                    'details' => [],
                    'plaque' => [],
                    'callbackUrl' => null,
                ],
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        }

        if ($request->get('Status') === 'NOK') {

            return $this->sendError(
                'نتیجه درخواست',
                [
                    'message' => 'تراکنش ناموفق. در صورتی که پول از حساب شما کسر شده تا 72 ساعت آینده به حساب شما بازگشت داده میشود',
                    'type' => null,
                    'details' => [],
                    'plaque' => [],
                    'callbackUrl' => $payment->successURL
                ],
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        }


        if (isset($payment->result_payment['Status']) && $payment->result_payment['Status'] == 101 || isset($payment->result_payment['Status']) && $payment->result_payment['Status'] == 100) {
            $user = User::whereId($payment->user_id)->first();
            return $this->sendResponse(
                'نتیجه درخواست',
                [
                    'message' => 'پرداخت موفقیت آمیز بود',
                    'refId' => $payment->result_payment['RefID'] ?? null,
                    'type' => $payment->type,
                    'balance' => $user->balance,
                    'amount' => formatMoney($payment->amount),
                    'details' => $payment->details,
                    'plaque' => $payment->plaque,
                    'createdAt' => shamsiDate($payment->created_at),
                    'callbackUrl' => $payment->successURL,
                ]
            );
        }

        $data = [
            'authority' => $request->get('Authority'),
            'source' => $app->source,
        ];

        $zarinpal = new Zarinpal;
        $response = $zarinpal->verfication($request, $data);

        if ($response['status'] == '100') {

            $user = User::whereId($payment->user_id)->first();
            $balance = str_replace(',', '', $user?->balance);
            $amount = str_replace(',', '', $response['amount']);
            $total = (float) $balance + (float) $amount;

            if ($user) {
                $user->balance = $total;
                $user->save();
            }


            return $this->sendResponse(
                'نتیجه درخواست',
                [
                    'message' => 'پرداخت موفقیت آمیز بود',
                    'refId' => $response['refId'],
                    'type' => $response['type'],
                    'balance' => $total,
                    'amount' => $response['amount'],
                    'details' => $response['details'],
                    'plaque' => $response['plaque'],
                    'createdAt' => $response['createdAt'] ?? dateTimeToday(),
                    'callbackUrl' => $payment->successURL,
                ]
            );
        }




        return $this->sendError(
            'نتیجه درخواست',
            [
                'message' => 'تراکنش ناموفق. در صورتی که پول از حساب شما کسر شده تا 72 ساعت آینده به حساب شما بازگشت داده میشود',
                'type' => $response['type'] ?? 'car',
                'details' => $response['details'] ?? null,
                'plaque' => $response['plaque'] ?? null,
                'callbackUrl' => $payment->successURL,
            ],
            Response::HTTP_UNPROCESSABLE_ENTITY
        );

    }

    public function qabzinoPaymentVerification(Request $request)
    {

        $qabzinoToken = env('QABZINO_TOKEN');
        $qabzinoPaymentUrlResultCheck = env('QABZINO_PAYMENT_KEY_RESULT_CHECK');

        $dataRequest = [
            'Identity' => [
                'Token' => $qabzinoToken,
            ],
            'Parameters' => [
                'PaymentKey' => (string) $request->key,
            ],
        ];

        $response = Http::post($qabzinoPaymentUrlResultCheck, $dataRequest);
        $responseData = $response->json();



        if (isset($responseData['Parameters']['Bills'])) {
            $bill = $responseData['Parameters']['Bills'][0];

            $message = "پرداخت خلافی خودرو/موتور با موفقیت ثبت شده است ...";
            $message .= " لطفاً در این مدت از پرداخت مجدد خودداری کنید.";

            $description = $bill['Description'] ?? $message;

            // ست کردن Description داخل خود Bills
            $responseData['Parameters']['Bills'][0]['Description'] = $description;

            TransactionUserQabzino::create([
                'phone' => null,
                'payment_key' => (string) $request->key,
                'amount' => $bill['Amount'] ?? null,
                'bill_id' => $bill['BillID'] ?? null,
                'payment_id' => $bill['PaymentID'] ?? null,
                'paid' => $bill['Paid'] ?? null,
                'description' => $description,
                'status' => $bill['Paid'] ?? false,
                'trace_number' => $bill['TraceNumber'] ?? null,
                'transaction_date_time' => $bill['TransactionDateTime'] ?? null,
                'result' => $responseData,
            ]);
        } else {
            TransactionUserQabzino::create([
                'phone' => null,
                'payment_key' => (string) $request->key,
                'amount' => null,
                'bill_id' => null,
                'payment_id' => null,
                'paid' => null,
                'description' => null,
                'status' => false,
                'trace_number' => null,
                'transaction_date_time' => null,
                'result' => $responseData,
            ]);

            // 👇 وقتی Bills نیست، description رو null بذار
            $responseData['description'] = null;
        }

        return [
            'success' => true,
            'data' => $responseData,
            'status' => Response::HTTP_OK,
        ];

    }

    private function getStatus($statusCode)
    {
        switch ($statusCode) {
            case -9:
                return 'خطای اعتبار سنجی: مقدارهای ضروری مانند مرچنت کد، آدرس بازگشت، توضیحات یا مبلغ پرداختی ممکن است نادرست باشند.';
            case -10:
                return 'ای پی یا مرچنت كد پذیرنده صحیح نیست.';
            case -11:
                return 'مرچنت کد فعال نیست، پذیرنده مشکل خود را به امور مشتریان زرین‌پال ارجاع دهد.';
            case -12:
                return 'تلاش بیش از حد در یک بازه زمانی کوتاه، لطفا بعدا تلاش کنید.';
            case -15:
                return 'درگاه پرداخت به حالت تعلیق در آمده است، پذیرنده مشکل خود را به امور مشتریان زرین‌پال ارجاع دهد.';
            case -16:
                return 'سطح تایید پذیرنده پایین‌تر از سطح نقره‌ای است.';
            case -17:
                return 'محدودیت پذیرنده در سطح آبی.';
            case 100:
                return 'عملیات موفق.';
            case -30:
                return 'پذیرنده اجازه دسترسی به سرویس تسویه اشتراکی شناور را ندارد.';
            case -31:
                return 'حساب بانکی تسویه را به پنل اضافه کنید.';
            case -32:
                return 'مبلغ وارد شده از مبلغ کل تراکنش بیشتر است.';
            case -33:
                return 'درصدهای وارد شده صحیح نیست.';
            case -34:
                return 'مبلغ وارد شده از مبلغ کل تراکنش بیشتر است.';
            case -35:
                return 'تعداد افراد دریافت‌کننده تسهیم بیش از حد مجاز است.';
            case -36:
                return 'حداقل مبلغ جهت تسهیم باید ۱۰,۰۰۰ ریال باشد.';
            case -37:
                return 'یک یا چند شماره شبای وارد شده برای تسهیم از سمت بانک غیر فعال است.';
            case -38:
                return 'خطا٬عدم تعریف صحیح شبا٬لطفا دقایقی دیگر تلاش کنید.';
            case -39:
                return 'خطایی رخ داده است، لطفا با امور مشتریان زرین‌پال تماس بگیرید.';
            case -40:
                return 'پارامترهای اضافی نامعتبر هستند، مقدار expire_in معتبر نیست.';
            case -41:
                return 'حداکثر مبلغ پرداختی ۱۰۰ میلیون تومان است.';
            case -50:
                return 'مبلغ پرداخت شده با مقدار مبلغ ارسال شده در متد وریفای متفاوت است.';
            case -51:
                return 'پرداخت ناموفق.';
            case -52:
                return 'خطای غیر منتظره‌ای رخ داده است، پذیرنده مشکل خود را به امور مشتریان زرین‌پال ارجاع دهد.';
            case -53:
                return 'پرداخت متعلق به این مرچنت کد نیست.';
            case -54:
                return 'اتوریتی نامعتبر است.';
            case -55:
                return 'تراکنش مورد نظر یافت نشد.';
            case -60:
                return 'امکان ریورس کردن تراکنش با بانک وجود ندارد.';
            case -61:
                return 'تراکنش موفق نیست یا قبلا ریورس شده است.';
            case -62:
                return 'آی پی درگاه تنظیم نشده است.';
            case -63:
                return 'حداکثر زمان (۳۰ دقیقه) برای ریورس کردن این تراکنش منقضی شده است.';
            case 101:
                return 'تراکنش وریفای شده است.';
            default:
                return 'خطای ناشناخته، لطفا با پشتیبانی تماس بگیرید.';
        }
    }

    private function translateErrorMessage($message)
    {
        $translations = [
            'The plaque. left field must not be greater than 2.' => 'فیلد پلاک چپ نباید بیشتر از 2 باشد.',
            'The plaque. mid field must not be greater than 3.' => 'فیلد پلاک میانه نباید بیشتر از 3 باشد.',
            'The plaque. right field must be a number.' => 'فیلد پلاک راست باید عددی باشد.',
            'The plaque. right field must be at least 2.' => 'فیلد پلاک راست باید حداقل 2 باشد.',
        ];

        return $translations[$message] ?? $message;
    }
}
