<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Api\BaseController;
use App\Http\Resources\ChargeWalletResource;
use App\Models\User;
use <PERSON><PERSON><PERSON>rin\LivewireRateLimiting\WithRateLimiting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class WalletController extends BaseController
{
    use WithRateLimiting;

    public function __construct()
    {
        $this->middleware('auth:api');
    }

    /**
     * @authenticated
     */
    public function index(Request $request)
    {
        $app = app('authenticated_application');

        $user = Auth::user();

        if ($user->phone == '09397192230' || $user->phone == '09334332338') {

            $CurrentUser = User::where('phone', $request->get('phone'))
                ->where('source', $app->source)
                ->latest()
                ->first();

            if (! $CurrentUser) {
                return response()->json(['message' => 'کاربر یافت نشد.'], 404);
            }

            $charge_balance = str_replace(',', '', $request->get('balance'));

            $charge_balance = str_replace(',', '', $request->get('balance'));
            $newBalance = (float) $charge_balance;

            $CurrentUser->update([
                'balance' => $newBalance,
            ]);

            return $this->sendResponse('شارژ حساب', [
                new ChargeWalletResource($CurrentUser),
            ], 201);
        }

        return response()->json(['message' => 'شما اجازه تغییر موجودی را ندارید.'], 403);

    }
}
