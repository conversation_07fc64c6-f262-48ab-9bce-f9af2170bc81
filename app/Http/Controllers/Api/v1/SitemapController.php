<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Api\BaseController;
use App\Http\Controllers\Controller;
use App\Http\Resources\Sitemap;
use App\Models\Article;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SitemapController extends BaseController
{
    public function index($type)
    {
        switch ($type) {
            case 'articles':
                $responseData = Sitemap::collection(
                    Article::where('active', true)
                        ->where(function ($q) {
                            $q->whereNull('page')
                                ->orWhere('page', '');
                        })
                        ->where('status', 'published')
                        ->get()
                );
                return $this->sendResponse('articles', $responseData);

            case 'services':
                $responseData = Sitemap::collection(
                    Article::where('active', true)
                        ->whereNotNull('page')
                        ->where('page', '!=', '')
                        ->where('status', 'published')
                        ->get()
                );
                return $this->sendResponse('services', $responseData);

            default:
                return $this->sendError('خلافی', ['message' => 'آدرس مورد نظر برای سایت مپ پیدا نشد'], Response::HTTP_NOT_FOUND);
        }
    }

    /**
     * Generate main sitemap.xml file
     */
    public function generateMainSitemap()
    {
        $sitemaps = [
            [
                'loc' => url('sitemaps/articles.xml'),
                'lastmod' => now()->format('Y-m-d')
            ],
            [
                'loc' => url('sitemaps/services.xml'),
                'lastmod' => now()->format('Y-m-d')
            ]
        ];

        $xml = $this->generateSitemapIndexXml($sitemaps);

        // Save to public directory
        $path = public_path('sitemap.xml');
        file_put_contents($path, $xml);

        return response($xml, 200, ['Content-Type' => 'application/xml']);
    }

    /**
     * Generate articles sitemap
     */
    public function generateArticlesSitemap()
    {
        $articles = Article::where('active', true)
            ->where(function ($q) {
                $q->whereNull('page')
                    ->orWhere('page', '');
            })
            ->where('status', 'published')
            ->get();

        $urls = [];
        foreach ($articles as $article) {
            $urls[] = [
                'loc' => 'https://khodrox.com/blog/' . $article->slug,
                'lastmod' => $article->updated_at->format('Y-m-d'),
                'changefreq' => 'monthly',
                'priority' => '0.7'
            ];
        }

        $xml = $this->generateUrlsetXml($urls);

        // Save to public/sitemaps directory
        $this->ensureSitemapsDirectory();
        $path = public_path('sitemaps/articles.xml');
        file_put_contents($path, $xml);

        return response($xml, 200, ['Content-Type' => 'application/xml']);
    }

    /**
     * Generate services sitemap
     */
    public function generateServicesSitemap()
    {
        $services = Article::where('active', true)
            ->whereNotNull('page')
            ->where('page', '!=', '')
            ->where('status', 'published')
            ->get();

        $urls = [];
        foreach ($services as $service) {
            $urls[] = [
                'loc' => 'https://khodrox.com' . $service->page,
                'lastmod' => $service->updated_at->format('Y-m-d'),
                'changefreq' => 'weekly',
                'priority' => '0.8'
            ];
        }

        $xml = $this->generateUrlsetXml($urls);

        // Save to public/sitemaps directory
        $this->ensureSitemapsDirectory();
        $path = public_path('sitemaps/services.xml');
        file_put_contents($path, $xml);

        return response($xml, 200, ['Content-Type' => 'application/xml']);
    }

    /**
     * Generate all sitemaps at once
     */
    public function generateAllSitemaps()
    {
        $this->generateArticlesSitemap();
        $this->generateServicesSitemap();
        $this->generateMainSitemap();

        return response()->json([
            'message' => 'تمام فایل‌های سایت‌مپ با موفقیت تولید شدند',
            'files' => [
                'sitemap.xml',
                'sitemaps/articles.xml',
                'sitemaps/services.xml'
            ]
        ]);
    }

    /**
     * Generate sitemap index XML
     */
    private function generateSitemapIndexXml($sitemaps)
    {
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $xml .= '<sitemapindex xmlns="https://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

        foreach ($sitemaps as $sitemap) {
            $xml .= '  <sitemap>' . "\n";
            $xml .= '    <loc>' . htmlspecialchars($sitemap['loc']) . '</loc>' . "\n";
            $xml .= '    <lastmod>' . $sitemap['lastmod'] . '</lastmod>' . "\n";
            $xml .= '  </sitemap>' . "\n";
        }

        $xml .= '</sitemapindex>';

        return $xml;
    }

    /**
     * Generate urlset XML
     */
    private function generateUrlsetXml($urls)
    {
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $xml .= '<urlset xmlns="https://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

        foreach ($urls as $url) {
            $xml .= '  <url>' . "\n";
            $xml .= '    <loc>' . htmlspecialchars($url['loc']) . '</loc>' . "\n";
            $xml .= '    <lastmod>' . $url['lastmod'] . '</lastmod>' . "\n";
            $xml .= '    <changefreq>' . $url['changefreq'] . '</changefreq>' . "\n";
            $xml .= '    <priority>' . $url['priority'] . '</priority>' . "\n";
            $xml .= '  </url>' . "\n";
        }

        $xml .= '</urlset>';

        return $xml;
    }

    /**
     * Ensure sitemaps directory exists
     */
    private function ensureSitemapsDirectory()
    {
        $directory = public_path('sitemaps');
        if (!file_exists($directory)) {
            mkdir($directory, 0755, true);
        }
    }
}
