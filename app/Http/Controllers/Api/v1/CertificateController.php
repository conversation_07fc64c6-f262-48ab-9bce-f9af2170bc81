<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Api\BaseController;
use App\Http\Controllers\Controller;
use App\Models\InquiryResult;
use App\Models\ServiceHealthLog;
use App\Models\User;
use App\Notifications\Channels\BaleChannel;
use App\Notifications\QabzinoServiceDown;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Notification;

class CertificateController extends BaseController
{
    public function __construct()
    {
        $this->middleware('auth:api');
    }

    public function show($traceNumber)
    {

        // return $traceNumber;
        $user = auth()->id();

        if (isset($user)) {

            $Inquiry = InquiryResult::whereId($traceNumber)->where('phone', auth()->user()->phone)->first();

            if ($Inquiry) {
                return $this->sendResponse('استعلام نمره منفی گواهینامه', [
                    'message' => $Inquiry->result['Status']['Description'],
                    'traceNumber' => $traceNumber,
                    'LicenseNumber' => $Inquiry->license_number,
                    'result' => $Inquiry->result['Parameters'],
                    'repetitive' => true
                ], Response::HTTP_OK);
            }

            return $this->sendError('استعلام نمره منفی گواهینامه', [
                'message' => 'اطلاعاتی در سامانه یافت نگردید',
                'traceNumber' => $traceNumber,
                'result' => [],
            ], Response::HTTP_NOT_FOUND);

        }

        return $this->sendError('استعلام نمره منفی گواهینامه', ['message' => 'شما به این بخش دسترسی ندارید لطفاْ وارد شوید'], Response::HTTP_UNAUTHORIZED);

    }

    public function store(Request $request)
    {

        $app = app('authenticated_application');
        $user = auth()->user();

        if (isset($user)) {

            try {

                $data = $request->validate([
                    'details.license_number' => 'required|string',
                    'details.mobile_number' => 'required|string',
                    'details.national_id' => 'required|string',
                ]);

            } catch (ValidationException $e) {

                return $this->sendError('خطای اعتبارسنجی', [
                    'message' => 'لطفاً موارد زیر را اصلاح کنید.',
                    'errors' => $e->errors(),
                ], Response::HTTP_UNPROCESSABLE_ENTITY);
            }

            $oldInquiry = InquiryResult::where('mobile_number', $data['details']['mobile_number'])
                ->where('type', 'negativePoints')
                ->where('phone', $user->phone)
                ->where('national_id', $data['details']['national_id'])
                ->where('license_number', $data['details']['license_number'])
                ->whereNotNull('result.Parameters')
                ->where('created_at', '>=', now()->subDay())
                ->first();

            try {

                $statusCode = $oldInquiry->result['Status']['Code'] ?? null;
                if (in_array($statusCode, ['GM5031', 'GM5038', 'G00003'])) {

                } else {
                    if ($oldInquiry) {
                        return $this->sendResponse('استعلام نمره منفی گواهینامه', [
                            'message' => $oldInquiry->result['Status']['Description'],
                            'traceNumber' => $oldInquiry->id,
                            'LicenseNumber' => $oldInquiry->license_number,
                            'result' => $oldInquiry->result['Parameters'],
                            'repetitive' => true
                        ], Response::HTTP_OK);
                    }

                }
            } catch (\Exception $exception) {

            }



            $AMOUNT_NEGATIVE_POINTS_REQUEST_CAR = (int) env('AMOUNT_NEGATIVE_POINTS_REQUEST_CAR');

            $USER_AFTER_BALANCE = (float) str_replace(',', '', $user->balance);

            $userKhalafiyarTransfer = getTransferAppSetting();

            if ($userKhalafiyarTransfer) {
                $USER_AFTER_BALANCE = $USER_AFTER_BALANCE + getSourceBalance($user->phone, 'khodroyar') ?? 0;
            }

            if ($USER_AFTER_BALANCE < $AMOUNT_NEGATIVE_POINTS_REQUEST_CAR) {
                return $this->sendError('خلافی', [
                    'message' => 'موجودی شما کافی نمی باشد',
                    'amount' => $AMOUNT_NEGATIVE_POINTS_REQUEST_CAR,
                    'balance' => $USER_AFTER_BALANCE,
                    'unit' => 'تومان',
                ], Response::HTTP_PAYMENT_REQUIRED);
            }



            $traceNumber = Crypt::encrypt('track_number:' . $user->phone . '_negative_points:' . rand(10000, 99999) . 'created_at:' . now());
            $InquiryResult = InquiryResult::create([
                'user_id' => auth()->id(),
                'balance' => $USER_AFTER_BALANCE,
                'balance_after' => $USER_AFTER_BALANCE,
                'source' => $app->source,
                'trace_number' => $traceNumber,
                'phone' => $user->phone,
                'type' => 'negativePoints',
                'mobile_number' => $data['details']['mobile_number'],
                'national_id' => $data['details']['national_id'],
                'license_number' => $data['details']['license_number'],
                'result' => null,
                'status' => 'pending',
                'agent' => $request->userAgent(),
            ]);

            $qabzinoToken = env('QABZINO_TOKEN');
            $url = env('NAJI_SERVICE_DRIVING_LICENSE_NEGATIVE_POINT_INQUIRY');

            $dataRequest = [
                'Identity' => [
                    'Token' => $qabzinoToken,
                ],
                'Parameters' => [
                    'LicenseNumber' => (string) $data['details']['license_number'],
                    'MobileNumber' => (string) $data['details']['mobile_number'],
                    'NationalID' => (string) $data['details']['national_id'],
                    'TraceNumber' => $InquiryResult->id,
                ],
            ];

            $response = Http::post($url, $dataRequest);
            $result = $response->json();
            $InquiryResult->result = $result;
            $InquiryResult->status = 'success';
            $InquiryResult->save();

            $this->sendQabzinoServiceDown($result);

            if ($result['Status']['Code'] == 'G00000' || $result['Status']['Code'] == 'GM5031') {

                $this->withdrawMoney($AMOUNT_NEGATIVE_POINTS_REQUEST_CAR, $USER_AFTER_BALANCE);

                $InquiryResult->balance_after = $USER_AFTER_BALANCE - $AMOUNT_NEGATIVE_POINTS_REQUEST_CAR;
                $InquiryResult->save();

                return $this->sendResponse('استعلام نمره منفی گواهینامه', [
                    'message' => $result['Status']['Description'],
                    'traceNumber' => $InquiryResult->id,
                    'LicenseNumber' => (string) $data['details']['license_number'],
                    'result' => $result['Parameters'],
                    'repetitive' => false
                ], Response::HTTP_OK);
            }


            return $this->sendResponse('استعلام نمره منفی گواهینامه', [
                'message' => $result['Status']['Description'],
                'traceNumber' => $InquiryResult->id,
                'result' => $result['Parameters'],
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
            // return ['inquiryResult' => $InquiryResult->result, 'traceNumber' => $InquiryResult->trace_number];


        }

        return $this->sendError('استعلام نمره منفی گواهینامه', ['message' => 'شما به این بخش دسترسی ندارید لطفاْ وارد شوید'], Response::HTTP_UNAUTHORIZED);

    }

    private function withdrawMoney(float $AMOUNT_PAYMENT_REQUEST_CAR, float $USER_BALANCE)
    {
        $userKhalafiyarTransfer = getTransferAppSetting();

        if ($userKhalafiyarTransfer) {

            $userKhalafiuarBalance = getSourceBalance(auth()->user()->phone, 'khodroyar');

            if ($userKhalafiuarBalance < $AMOUNT_PAYMENT_REQUEST_CAR) {

                if ($USER_BALANCE < $AMOUNT_PAYMENT_REQUEST_CAR) {
                    return $this->sendError('خلافی', ['message' => 'موجودی شما برای دریافت استعلام خلافی کافی نمی باشد'], Response::HTTP_PAYMENT_REQUIRED);
                } elseif ($USER_BALANCE >= $AMOUNT_PAYMENT_REQUEST_CAR) {
                    $RESULT = $USER_BALANCE - $AMOUNT_PAYMENT_REQUEST_CAR;
                    User::whereId(auth()->id())->update([
                        'balance' => (float) $RESULT,
                    ]);

                    return true;
                }

                return $this->sendError('خلافی', ['message' => 'موجودی شما برای دریافت استعلام خلافی کافی نمی باشد'], Response::HTTP_PAYMENT_REQUIRED);

            } else {

                if ($userKhalafiuarBalance > $AMOUNT_PAYMENT_REQUEST_CAR) {
                    $RESULT = $userKhalafiuarBalance - $AMOUNT_PAYMENT_REQUEST_CAR;
                    User::where('phone', auth()->user()->phone)
                        ->where('source', 'khodroyar')->update([
                                'balance' => (float) $RESULT,
                            ]);

                    return true;
                }

                return $this->sendError('خلافی', ['message' => 'موجودی شما برای دریافت استعلام خلافی کافی نمی باشد'], Response::HTTP_PAYMENT_REQUIRED);
            }

        } else {
            if ($USER_BALANCE < $AMOUNT_PAYMENT_REQUEST_CAR) {
                return $this->sendError('خلافی', ['message' => 'موجودی شما برای دریافت استعلام خلافی کافی نمی باشد'], Response::HTTP_PAYMENT_REQUIRED);
            }

            $RESULT = $USER_BALANCE - $AMOUNT_PAYMENT_REQUEST_CAR;
            User::whereId(auth()->id())->update([
                'balance' => (float) $RESULT,
            ]);

            return true;
        }

    }

    private function sendQabzinoServiceDown($result)
    {
        try {
            if (isset($result['Status']['Code']) && $result['Status']['Code'] == 'GM5004') {

                $phone = '09334332338';

                Cache::put('enabled_qabzino_service_down_job', true);

                $message = '🔴 قبضینو - سرویس استعلام نمره منفی گواهینامه با خطای «امکان برقراری ارتباط با سرویس دهنده وجود ندارد» مواجه شد' . ' - تاریخ ' . dateTimeToday();

                ServiceHealthLog::create([
                    'service' => 'qabzino',
                    'type' => 'disconnect',
                    'phone' => $phone,
                    'message' => $message,
                ]);

                Notification::route(BaleChannel::class, 'any_identifier')
                    ->notify(new QabzinoServiceDown($message));
            }
        } catch (\Exception $e) {

        }
    }
}
