<?php

namespace App\Http\Controllers;

use App\Models\Article;
use App\Models\CampaignUtmResult;
// use App\Models\NodeHostChecker;
use App\Models\ChargedTransaction;
use App\Models\PaymentPending;
use App\Models\ReturnedTransaction;
use App\Models\TransactionUserQabzino;
use App\Models\User;
use App\Models\UtmConvertion;
use Carbon\Carbon;
use Hekmatinasser\Verta\Verta;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\DB;
use App\Models\Transaction;
use Illuminate\Support\Str;
use App\Jobs\GenerateSitemapJob;
use App\Services\FtpSitemapService;
use Exception;
use App\Services\MeliPayamak as ServiceMeliPayamak;
class TestController extends Controller
{
    public function index(Request $request)
    {
        // $count = \App\Models\InquiryResult::where('type', 'motor')->distinct('result.Parameters.PlateNumber')->count();


        // return formatMoney($count);

        // GenerateSitemapJob::dispatch();

        // TransactionUserQabzino::where('phone', null)
        //     ->chunk(5, function ($transactions) {
        //         foreach ($transactions as $transaction) {

        //             $relatedPhones = $transaction->relatedPhones();

        //             if ($relatedPhones->isNotEmpty()) {

        //                 $transaction->phone = $relatedPhones->implode(', ');
        //                 $transaction->save();
        //             }
        //         }
        //     });

        // $startDate = "1404/06/01";
        // $endDate = "1404/06/01";

        // $start = Verta::parse($startDate)->toCarbon()->startOfDay();
        // $end = Verta::parse($endDate)->toCarbon()->endOfDay();

        // // return PaymentPending::whereBetween('created_at', [$start, $end])->where('source', 'naghlie')->count();
        // return DB::table('paymentpendings')->whereBetween('createdAt', [$start, $end])->where('source', 'khodroyar')->count();
        // return Transaction::whereBetween('creationTime', [$start, $end])->where('source', 'khodroyar')->count();

        // $count = UtmConvertion::whereBetween('created_at', [$start, $end])
        //     ->where('page', 'like', 'https://naghlie.com/amount?message=%')
        //     ->select('u_trace')
        //     ->distinct()
        //     ->count();

        // return $count;


        // return User::where('source', 'naghlie')
        //     ->whereBetween('created_at', [$start, $end])
        //     ->count();



        // return (new ServiceMeliPayamak)->sendBalanceCharge(formatMoney('400,000'), '09137846242', '358843');

        // $s = (new ServiceMeliPayamak)->sendBalanceCharge('1000000', '09397192230', '358843');
        // die();

        // $transactions = Transaction::where('source', 'naghlie')
        //     ->where('created_at', '>=', now()->subDays(1))
        //     ->get();

        // $results = []; // برای ذخیره در آرایه

        // foreach ($transactions as $t) {
        //     $u = User::where('phone', $t->phone)
        //         ->where('balance', 0) // فقط کاربرانی که بالانس صفر دارن
        //         ->first();

        //     if ($u) {
        //         // ذخیره در جدول returned_transactions
        //         $saved = ReturnedTransaction::create([
        //             'user_id' => $u->id,
        //             'phone' => $u->phone,
        //             'balance' => $u->balance,
        //             'transaction_id' => $t->id,
        //             'amount' => $t->amount,
        //             'created_at_transaction' => $t->created_at,
        //         ]);

        //         // اضافه کردن به آرایه
        //         $results[] = $saved->toArray();
        //     }
        // }

        // // نمایش آرایه کامل
        // return response()->json($results);

        // $startDate = "1404/05/25";
        // $endDate = "1404/05/25";

        // $start = Verta::parse($startDate)->toCarbon()->startOfDay();
        // $end = Verta::parse($endDate)->toCarbon()->endOfDay();

        // $transactions = Transaction::where('source', 'naghlie')
        //     ->whereBetween('created_at', [$start, $end])
        //     ->with('user')
        //     ->get();

        // return $transactions->map(function ($transaction) {

        //     if ($transaction->user?->balance === 0) {
        //         dd($transaction->user->phone, $transaction->amount);
        //         User::where('phone', $transaction->phone)->where('source', 'naghlie')->update([
        //             'balance' => (float) str_replace(',', '', $transaction->amount),
        //         ]);
        //     }


        //     // return [
        //     //     'phone' => $transaction->user?->phone,
        //     //     'user_balance' => $transaction->user?->balance ?? '',
        //     //     'transaction' => $transaction->amount,
        //     // ];

        // });

        // return User::whereIn('phone', [
        //     '09127775301',
        //     '09131888339',
        //     '09374793909',
        //     '09123408831',
        //     '09125888185',
        //     '09124460430',
        //     '09126752180',
        //     '09124941789',
        //     '09175909156',
        //     '09354947747',
        //     '09165186062',
        //     '09128300699',
        //     '09127208968',
        //     '09015148873',
        //     '09175909156',
        //     '09917211840',
        //     '09122247891',
        //     '09192600839',
        //     '09126708016',
        //     '09030351816',
        //     '09229577051',
        //     '09193221070',
        //     '09125141556',
        //     '09121456210',
        //     '09125787737',
        //     '09121487753',
        //     '09939061970',
        //     '09016052556',
        //     '09933383270',
        //     '09057443430',
        //     '09981506944',
        //     '09120646930',
        //     '09163640073',
        //     '09364041804',
        //     '09127033428',
        //     '09036736504',
        //     '09192294571',
        //     '09387980751',
        //     '09036736504',
        //     '09192294571',
        //     '09387980751',
        //     '09151002201',
        //     '09184617088',
        //     '09036736504',
        //     '09016823024',
        //     '09184617088',
        //     '09163051157',
        //     '09905478979',
        //     '09056349207',
        //     '09135082176',
        //     '09026893477',
        //     '09331497913',
        //     '09356997990',
        //     '09179055506'

        // ])->pluck('balance')->toArray();



        // $transactions = Transaction::where('source', 'naghlie')
        //     ->where('created_at', '>=', now()->subDays(1))
        //     ->get();

        // foreach ($transactions as $t) {
        //     $u = User::where('phone', $t->phone)->first();

        //     if (!$u) {

        //         continue;
        //     }

        //     $balanceBefore = (float) str_replace(',', '', $u->balance);
        //     $amount = (float) str_replace(',', '', $t->amount);
        //     $smsSent = false;

        //     if ($balanceBefore < $amount) {
        //         $u->balance = (float) $balanceBefore + $amount;
        //         $u->save();

        //         (new ServiceMeliPayamak)->sendBalanceCharge(formatMoney($amount), $u->phone, '358843');
        //         $smsSent = true;
        //     }

        //     $balanceAfter = $u->balance;


        //     ChargedTransaction::create([
        //         'user_id' => $u->id,
        //         'phone' => $u->phone,
        //         'balance_before' => $balanceBefore,
        //         'amount' => $amount,
        //         'balance_after' => $balanceAfter,
        //         'sms_sent' => $smsSent,
        //     ]);
        // }


        // return ChargedTransaction::get()->toArray();






        // $articles = Article::where('status', 'draft')
        //     ->where('active', true)
        //     ->get()
        //     ->filter(function ($article) {
        //         if (empty($article->datePublished)) {
        //             return false; // اگر تاریخ نداره حذف بشه
        //         }

        //         $jalali = Verta::parse($article->datePublished);
        //         $gregorian = $jalali->datetime();

        //         return Carbon::now()->greaterThanOrEqualTo($gregorian);
        //     });



        // $unknowns = \App\Models\CampaignUtmResult::where('utm_source', 'unknown')->get();
        // $grouped = $unknowns->groupBy(function ($item) {
        //     return $item->utm_campaign . '|' . $item->utm_medium;
        // });

        // $aggregated = $grouped->map(function ($group) {
        //     return [
        //         'utm_campaign' => $group->first()->utm_campaign,
        //         'utm_medium' => $group->first()->utm_medium,
        //         'utm_source' => 'unknown',
        //         'sum_transactions' => $group->sum('sum_transactions'),
        //         'count_transactions' => $group->sum('count_transactions'),
        //         'total_subscribers_user' => $group->sum('total_subscribers_user'),
        //         'total_guest_user' => $group->sum('total_guest_user'),
        //         'count_click' => $group->sum('count_click'),
        //         'count_unique_click' => $group->sum('count_unique_click'),
        //         'impression' => $group->sum('impression'),
        //         'start_date' => $group->min('start_date'),
        //         'end_date' => $group->max('end_date'),
        //     ];
        // })->values();

        // \App\Models\CampaignUtmResult::where('utm_source', 'unknown')->delete();

        // foreach ($aggregated as $item) {
        //     \App\Models\CampaignUtmResult::updateOrCreate(
        //         [
        //             'utm_campaign' => $item['utm_campaign'],
        //             'utm_medium' => $item['utm_medium'],
        //             'utm_source' => $item['utm_source'],
        //         ],
        //         [
        //             'start_date' => $item['start_date'],
        //             'end_date' => $item['end_date'],
        //             'sum_transactions' => $item['sum_transactions'],
        //             'count_transactions' => $item['count_transactions'],
        //             'total_subscribers_user' => $item['total_subscribers_user'],
        //             'total_guest_user' => $item['total_guest_user'],
        //             'count_click' => $item['count_click'],
        //             'count_unique_click' => $item['count_unique_click'],
        //             'impression' => $item['impression'],
        //             'ctr' => $item['impression'] > 0 ? round($item['count_click'] / $item['impression'] * 100, 2) : 0,
        //             'cr' => $item['count_click'] > 0 ? round($item['count_transactions'] / $item['count_click'] * 100, 2) : 0,
        //             'campaign_budget' => 0,
        //             'cr_per_member' => $item['total_subscribers_user'] > 0
        //                 ? round(100 * (float) $item['total_subscribers_user'] / (float) $item['count_click'], 2)
        //                 : 0,
        //             'cr_per_transaction' => $item['count_transactions'] > 0
        //                 ? round(100 * (float) $item['count_transactions'] / (float) $item['count_click'], 2)
        //                 : 0,
        //         ]
        //     );
        // }

        // CampaignUtmResult::truncate();

        // $campaigns = [];

        // UtmConvertion::where(function ($query) {
        //     $query->where('cookie_key', 'like', 'utm_utm_source%')
        //         ->orWhere('cookie_value', 'like', 'https://khodrox.com/car-tickets?utm_source%');
        // })
        //     ->select('cookie_value', 'created_at', 'type', 'u_trace')
        //     ->orderByDesc('created_at')
        //     ->chunk(1000, function ($records) use (&$campaigns) {
        //         foreach ($records as $item) {
        //             $parsed = [];
        //             parse_str((string) $item->cookie_value, $parsed);

        //             $utm_campaign = $parsed['utm_campaign'] ?? 'unknown';
        //             $utm_source = $parsed['utm_source'] ?? 'unknown';
        //             $utm_medium = $parsed['utm_medium'] ?? 'unknown';

        //             $key = $utm_campaign . '|' . $utm_source . '|' . $utm_medium;

        //             if (!isset($campaigns[$key])) {
        //                 $campaigns[$key] = [
        //                     'utm_campaign' => $utm_campaign,
        //                     'utm_source' => $utm_source,
        //                     'utm_medium' => $utm_medium,
        //                     'start_date' => $item->created_at,
        //                     'end_date' => $item->created_at,
        //                     'count_click' => 0,
        //                     'impression' => 0,
        //                     'count_transactions' => 0,
        //                     'sum_transactions' => 0,
        //                     'total_subscribers_user' => [],
        //                     'total_guest_user' => [],
        //                     'unique_clicks' => [],
        //                 ];
        //             }

        //             // تاریخ شروع و پایان کمپین
        //             $campaigns[$key]['start_date'] = min($campaigns[$key]['start_date'], $item->created_at);
        //             $campaigns[$key]['end_date'] = max($campaigns[$key]['end_date'], $item->created_at);

        //             $campaigns[$key]['count_click']++;
        //             $campaigns[$key]['unique_clicks'][$item->u_trace] = true;

        //             if ($item->type === 'guest') {
        //                 $campaigns[$key]['total_guest_user'][$item->u_trace] = true;
        //             } else {
        //                 $campaigns[$key]['total_subscribers_user'][$item->u_trace] = true;
        //             }
        //         }
        //     });

        // foreach ($campaigns as &$data) {
        //     $subscriber_u_traces = array_keys($data['total_subscribers_user']);

        //     $phones = UtmConvertion::whereIn('u_trace', $subscriber_u_traces)
        //         ->where('type', 'user')
        //         ->whereNotNull('phone')
        //         ->pluck('phone')
        //         ->unique()
        //         ->toArray();

        //     $transactions = \App\Models\Transaction::whereIn('phone', $phones)
        //         ->where('created_at', '>=', $data['start_date'])
        //         ->where('created_at', '<=', $data['end_date'])
        //         ->get();

        //     $data['count_transactions'] = $transactions->count();
        //     $data['sum_transactions'] = $transactions->sum('amount');
        // }

        // foreach ($campaigns as $data) {
        //     CampaignUtmResult::updateOrCreate(
        //         [
        //             'utm_campaign' => $data['utm_campaign'],
        //             'utm_source' => $data['utm_source'],
        //             'utm_medium' => $data['utm_medium'],
        //         ],
        //         [
        //             'start_date' => $data['start_date'],
        //             'end_date' => $data['end_date'],
        //             'sum_transactions' => $data['sum_transactions'],
        //             'count_transactions' => $data['count_transactions'],
        //             'total_subscribers_user' => count($data['total_subscribers_user']),
        //             'total_guest_user' => count($data['total_guest_user']),
        //             'ctr' => $data['impression'] > 0 ? round(($data['count_click'] / $data['impression']) * 100, 2) : 0,
        //             'cr' => $data['count_click'] > 0 ? round(($data['count_transactions'] / $data['count_click']) * 100, 2) : 0,
        //             'count_click' => $data['count_click'],
        //             'count_unique_click' => count($data['unique_clicks']),
        //             'impression' => $data['impression'],
        //             'campaign_budget' => 0,
        //             'cr_per_member' => count($data['total_subscribers_user']) > 0
        //                 ? round(100 * (float) $data['total_subscribers_user'] / (float) $data['count_click'], 2)
        //                 : 0,

        //             'cr_per_transaction' => $data['count_transactions'] > 0
        //                 ? round(100 * (float) $data['count_transactions'] / (float) $data['count_click'], 2)
        //                 : 0,

        //         ]
        //     );
        // }

        // Cache::put('enabled_qabzino_service_down_job', false);
        // Cache::put('enabled_inquiryـrecoveredـqabzino', false);

        // UtmConvertion::where('page', 'like', 'http://localhost%')->delete();
        // $reports = UtmConvertion::where('page', 'like', 'http://localhost%')->get();
        // dd($reports->toArray());

        // $articles = Article::where('status', 'draft')
        //     ->where('active', true)
        //     ->get()
        //     ->filter(function ($article) {
        //         $jalali = Verta::parse($article->datePublished);
        //         $gregorian = $jalali->datetime();

        //         return Carbon::now()->greaterThanOrEqualTo($gregorian);
        //     });

        // return $articles->toArray();
        // return $this->getAllTerminalIds();
    }

    public function getTransactionsWithTerminalAndMobile($terminalId, $mobile)
    {
        $query = <<<'GRAPHQL'
            query Sessions(
                $terminal_id: ID,
                $mobile: CellNumber,
            ) {
                Session(
                    terminal_id: $terminal_id,
                    mobile: $mobile,
                ) {
                    id
                    status
                    amount
                    description
                    created_at
                }
            }
        GRAPHQL;

        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . env('ZARINPAL_ACCESS_TOKEN_KHODROX'),
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ])->post('https://next.zarinpal.com/api/v4/graphql', [
                    'query' => $query,
                    'variables' => [
                        'terminal_id' => $terminalId,
                        'mobile' => $mobile,
                        // شما می‌تونید بقیه پارامترها رو هم اضافه کنید در صورت نیاز
                    ],
                ]);

        if ($response->successful()) {
            return $response->json();
        } else {
            return [
                'status' => $response->status(),
                'error' => $response->body(),
            ];
        }
    }

    public function getAllTerminalIds()
    {
        $response = Http::withHeaders([
            // 'Authorization' => 'Bearer ' . env('ZARINPAL_ACCESS_TOKEN'),
            'Authorization' => 'Bearer ' . env('ZARINPAL_ACCESS_TOKEN_KHODROX'),
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ])->post('https://next.zarinpal.com/api/v4/graphql', [
                    'query' => '
               query{
                    Terminals {
                        id
                        status
                        preferred_bank_account_id
                        domain
                        support_phone
                        key
                        name
                        logo
                        created_at
                        updated_at
                        }
                    }
            ',
                ]);

        if ($response->successful()) {
            return $response->json();
            // پردازش داده‌های دریافت‌شده
        } else {
            // مدیریت خطا
            $status = $response->status();

            return $response->body();
        }
    }

    public function refoundZarinpalWithRefId($refId, $amount, $description = 'استرداد وجه', $method = 'PAYA')
    {
        $amount = str_replace(',', '', $amount);

        // return (float) $amount * 10;
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . env('ZARINPAL_ACCESS_TOKEN'),
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ])->post('https://next.zarinpal.com/api/v4/graphql', [
                    'query' => '
                mutation AddRefund($session_id: ID!, $amount: BigInteger!, $description: String, $method: InstantPayoutActionTypeEnum, $reason: RefundReasonEnum) {
                    resource: AddRefund(
                        session_id: $session_id
                        amount: $amount
                        description: $description
                        method: $method
                        reason: $reason
                    ) {
                        terminal_id
                        id
                        amount
                        timeline {
                            refund_amount
                            refund_time
                            refund_status
                        }
                    }
                }
            ',
                    'variables' => [
                        'session_id' => $refId,
                        'amount' => (float) $amount * 10,
                        'description' => $description,
                        'method' => $method,
                        'reason' => 'CUSTOMER_REQUEST',
                    ],
                ]);

        if ($response->successful()) {
            return $response->json();
        } else {
            $responseData = $response->json();

            if (isset($responseData['errors']) && count($responseData['errors']) > 0) {
                $errorMessage = $responseData['errors'][0]['fa_message'] ?? 'خطای نامشخص رخ داده است.';

                return response()->json(['error' => $errorMessage], $response->status());
            }

            return response()->json(['error' => 'خطای نامشخص رخ داده است.'], $response->status());
        }
    }

    public function getPaymentLinkQabzino()
    {
        $response = Http::post('https://core.inquiry.ayantech.ir/webservices/core.svc/ReportNewBillPayment', [
            'Identity' => [
                'Token' => env('QABZINO_TOKEN'),
            ],
            'Parameters' => [
                'Bills' => [
                    [
                        'BillID' => '8727094800290',
                        'PaymentID' => '100024351',
                    ],
                ],
                'MobileNumber' => '09334332338',
                'RedirectLink' => '',
                'RedirectLinkTitle' => 'پرداخت خلافی',
                'TerminalID' => 11,
                'TraceNumber' => '123',
                'UniqueKey' => '12345',
            ],
        ]);

        $result = $response->json();

        return isset($result['Parameters']['PaymentLink']) ? $result['Parameters']['PaymentLink'] : '#';
    }

    public function cleanBalanceForUsers()
    {
        $this->info('Cleaning balances...');

        User::chunk(500, function ($users) {
            foreach ($users as $user) {
                $original = $user->balance;

                $cleaned = str_replace(['٬', ','], '', $original);

                $floatBalance = (float) $cleaned;

                $user->balance = $floatBalance;
                $user->save();
            }
        });

        $this->info('✅ All balances cleaned and saved.');
    }

    public function getListRefund($filter = 'PAID')
    {

        $token = '***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
        $terminalId = '545784';

        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $token,
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ])->post('https://next.zarinpal.com/api/v4/graphql', [
                    'query' => '
        query GetSession($terminalId: ID!, $filter: FilterEnum!) {
            Session(terminal_id: $terminalId, filter: $filter) {
                session_tries {
                    id
                    session_id
                    payment_id
                    payer_ip
                    init_time
                    verify_time
                    status
                    rrn
                    card_pan
                    created_at

                }
                description
                amount
                fee
                payer_info {
                    name
                    mobile
                    email
                    order_id
                    card_holder_account_number
                    description
                    zarin_link_id
                    card_holder_name
                    card_holder_iban
                }
                refund {
                    id
                    session_id
                    instant_payout {
                    id
                    amount
                    fee
                    terminal {
                    id
                    __typename
                }
                bank_account {
                    id
                    iban
                    holder_name
                    issuing_bank {
                    name
                    slug
                    slug_image
                    __typename
                }
                __typename
                }
                    reference_id
                    reconciled_at
                    created_at
                    status
                    __typename
                }
                __typename
                }
            }
        }
    ',
                    'variables' => [
                        'terminalId' => $terminalId,
                        'filter' => $filter,
                        'id' => '*********',
                    ],
                ]);

        $responseData = $response->json();

        return $responseData;
    }
}
