<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Str;

class ArticleController extends Controller
{
    public function store(Request $request)
    {
        if ($request->hasFile('upload')) {
            $file = $request->file('upload');
            $filename = Str::uuid() . '.' . $file->getClientOriginalExtension();

            // ذخیره فایل در storage/app/uploads
            $file->storeAs('uploads', $filename);

            // اگر Symlink ساخته شده باشد (php artisan storage:link)
            $url = asset('storage/uploads/' . $filename);

            return response()->json([
                'url' => $url,
            ]);
        }

        return response()->json(['error' => 'No file uploaded.'], 400);
    }
}
