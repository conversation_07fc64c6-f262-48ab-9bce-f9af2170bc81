<?php

namespace App\Http\Controllers;

use App\Models\HostCheck;
use App\Models\HostCheckLog;
use App\Models\NodeHostChecker;

class HostCheckerController extends Controller
{
    public function checkHost()
    {

        $hosts = HostCheck::where('active', true)->pluck('url')->toArray();
        $selectedNodes = NodeHostChecker::where('active', true)->pluck('hostname')->toArray();
        $nodeParams = implode('&node=', $selectedNodes);

        $results = [];
        $now = now();

        foreach ($hosts as $host) {
            // بررسی وجود گزارش در 15 دقیقه گذشته
            $recentCheck = HostCheckLog::where('host', $host)
                ->where('created_at', '>=', $now->subMinutes(15))
                ->orderBy('created_at', 'desc')
                ->first();

            if ($recentCheck) {
                $results[] = [
                    'host' => $host,
                    'ok' => true,
                    'cached' => true,
                    'request_id' => $recentCheck->request_id,
                    'permanent_link' => $recentCheck->response['permanent_link'] ?? null,
                    'result' => $recentCheck->result,
                    'last_checked' => $recentCheck->created_at,
                ];

                continue;
            }

            $hostChecklog = HostCheckLog::create([
                'host' => $host,
                'request_id' => null,
                'response' => null,
                'result' => null,
                'status' => 'pending',
            ]);

            $checkUrl = 'https://check-host.net/check-http?host='.$host.'&node='.$nodeParams;
            $checkResponse = curl_get_json($checkUrl, timeoutSeconds: 720);

            if (! $checkResponse || ! isset($checkResponse['request_id'])) {
                $hostChecklog->update([
                    'response' => $checkResponse,
                    'status' => 'error',
                ]);

                $results[] = [
                    'host' => $host,
                    'ok' => false,
                    'error' => 'Invalid response or timeout from check-host',
                    'response' => $checkResponse,
                ];

                continue;
            }

            $requestId = $checkResponse['request_id'];
            $hostChecklog->update([
                'request_id' => $requestId,
                'response' => $checkResponse,
                'status' => 'pending_secondary',
            ]);

            $minutes = 4;
            sleep($minutes * 60);

            $resultUrl = "https://check-host.net/check-result/{$requestId}";
            $resultResponse = curl_get_json($resultUrl, timeoutSeconds: 60);

            if (! $resultResponse || ! is_array($resultResponse)) {
                $hostChecklog->update([
                    'result' => $resultResponse,
                    'status' => 'error',
                ]);

                $results[] = [
                    'host' => $host,
                    'ok' => false,
                    'error' => 'Invalid result or timeout from check-host',
                    'request_id' => $requestId,
                    'result' => $resultResponse,
                ];

                continue;
            }

            $hostChecklog->update([
                'result' => $resultResponse,
                'status' => 'completed',
            ]);

            $results[] = [
                'host' => $host,
                'ok' => true,
                'request_id' => $requestId,
                'permanent_link' => $checkResponse['permanent_link'] ?? null,
                'result' => $resultResponse,
            ];
        }

        return response()->json([
            'all_ok' => collect($results)->every(fn ($r) => $r['ok']),
            'results' => $results,
        ]);
    }

    public function replayCheckHost()
    {

        $hosts = HostCheckLog::whereNotNull('request_id')
            ->where(function ($query) {
                $query->where(function ($subQuery) {
                    $subQuery->where('status', 'completed')
                        ->where('result.error', 'exists', true);
                })
                    ->orWhere('status', 'pending_secondary');
            })
            ->get();

        $results = [];

        foreach ($hosts as $host) {

            $requestId = $host->request_id;

            $resultUrl = "https://check-host.net/check-result/{$requestId}";
            $resultResponse = curl_get_json($resultUrl, timeoutSeconds: 30);

            if (! $resultResponse || ! is_array($resultResponse)) {
                $host->update([
                    'result' => $resultResponse,
                    'status' => 'error',
                ]);

                $results[] = [
                    'host' => $host,
                    'ok' => false,
                    'error' => 'Invalid result or timeout from check-host',
                    'request_id' => $requestId,
                    'result' => $resultResponse,
                ];

                continue;
            }

            $host->update([
                'result' => $resultResponse,
                'status' => 'completed',
            ]);

            $results[] = [
                'host' => $host,
                'ok' => true,
                'request_id' => $requestId,
                'permanent_link' => $checkResponse['permanent_link'] ?? null,
                'result' => $resultResponse,
            ];
        }

        return response()->json([
            'all_ok' => collect($results)->every(fn ($r) => $r['ok']),
            'results' => $results,
        ]);
    }

    public function closePendingRequests()
    {
        $threshold = now()->subMinutes(30);

        $staleRequests = HostCheckLog::whereNull('request_id')
            ->where('status', 'pending')
            ->where('created_at', '<', $threshold)
            ->get();

        foreach ($staleRequests as $request) {
            $request->status = 'cancel';
            $request->save();
        }

        return response()->json([
            'closed_count' => $staleRequests->count(),
            'message' => 'درخواست‌های معلق قدیمی با موفقیت بسته شدند.',
        ]);
    }

    public function getHttpNodes()
    {
        return 'disbaled';

        $checkUrl = 'https://check-host.net/nodes/hosts';
        $checkResponse = curl_get_json($checkUrl, 120);

        // لیست اولیه‌ی fallback (بخشی از JSON کامل)
        $fallbackNodes = [
            'at1.node.check-host.net' => [
                'asn' => 'AS64457',
                'ip' => '*************',
                'location' => ['at', 'Austria', 'Vienna'],
            ],
            'au1.node.check-host.net' => [
                'asn' => 'AS141995',
                'ip' => '**************',
                'location' => ['au', 'Australia', 'Sydney'],
            ],
            'bg1.node.check-host.net' => [
                'asn' => 'AS9028',
                'ip' => '************',
                'location' => ['bg', 'Bulgaria', 'Sofia'],
            ],
            'br1.node.check-host.net' => [
                'asn' => 'AS268581',
                'ip' => '**************',
                'location' => ['br', 'Brazil', 'Sao Paulo'],
            ],
            'ca1.node.check-host.net' => [
                'asn' => 'AS396993',
                'ip' => '**************',
                'location' => ['ca', 'Canada', 'Vancouver'],
            ],
            'ch1.node.check-host.net' => [
                'asn' => 'AS51852',
                'ip' => '**************',
                'location' => ['ch', 'Switzerland', 'Zurich'],
            ],
            'cz1.node.check-host.net' => [
                'asn' => 'AS44477',
                'ip' => '************',
                'location' => ['cz', 'Czechia', 'C.Budejovice'],
            ],
            'de1.node.check-host.net' => [
                'asn' => 'AS24940',
                'ip' => '***************',
                'location' => ['de', 'Germany', 'Nuremberg'],
            ],
            'de4.node.check-host.net' => [
                'asn' => 'AS34549',
                'ip' => '**************',
                'location' => ['de', 'Germany', 'Frankfurt'],
            ],
            'fi1.node.check-host.net' => [
                'asn' => 'AS24940',
                'ip' => '**************',
                'location' => ['fi', 'Finland', 'Helsinki'],
            ],
            'fr1.node.check-host.net' => [
                'asn' => 'AS49556',
                'ip' => '*************',
                'location' => ['fr', 'France', 'Roubaix'],
            ],
            'fr2.node.check-host.net' => [
                'asn' => 'AS12876',
                'ip' => '**************',
                'location' => ['fr', 'France', 'Paris'],
            ],
            'ge1.node.check-host.net' => [
                'asn' => 'AS203647',
                'ip' => '***************',
                'location' => ['ge', 'Georgia', 'Tbilisi'],
            ],
            'hk1.node.check-host.net' => [
                'asn' => 'AS207713',
                'ip' => '*************',
                'location' => ['hk', 'Hong Kong', 'Hong Kong'],
            ],
            'hu1.node.check-host.net' => [
                'asn' => 'AS211619',
                'ip' => '************',
                'location' => ['hu', 'Hungary', 'Nyiregyhaza'],
            ],
            'id2.node.check-host.net' => [
                'asn' => 'AS141968',
                'ip' => '**************',
                'location' => ['id', 'Indonesia', 'Jakarta'],
            ],
            'il1.node.check-host.net' => [
                'asn' => 'AS206446',
                'ip' => '***************',
                'location' => ['il', 'Israel', 'Tel Aviv'],
            ],
            'il2.node.check-host.net' => [
                'asn' => 'AS206446',
                'ip' => '*************',
                'location' => ['il', 'Israel', 'Netanya'],
            ],
            'in1.node.check-host.net' => [
                'asn' => 'AS26383',
                'ip' => '*************',
                'location' => ['in', 'India', 'Mumbai'],
            ],
            'in2.node.check-host.net' => [
                'asn' => 'AS56971',
                'ip' => '***************',
                'location' => ['in', 'India', 'New Delhi'],
            ],
            'ir2.node.check-host.net' => [
                'asn' => 'AS49556',
                'ip' => '************',
                'location' => ['ir', 'Iran', 'Mashhad'],
            ],
            'ir5.node.check-host.net' => [
                'asn' => 'AS207724',
                'ip' => '***********',
                'location' => ['ir', 'Iran', 'Esfahan'],
            ],
            'es1.node.check-host.net' => [
                'asn' => 'AS215691',
                'ip' => '*************',
                'location' => ['es', 'Spain', 'Madrid'],
            ],
            'ir1.node.check-host.net' => [
                'asn' => 'AS47430',
                'ip' => '***************',
                'location' => ['ir', 'Iran', 'Tehran'],
            ],
            'ir3.node.check-host.net' => [
                'asn' => 'AS60423',
                'ip' => '**************',
                'location' => ['ir', 'Iran', 'Shiraz'],
            ],
            'ir6.node.check-host.net' => [
                'asn' => 'AS208264',
                'ip' => '***********',
                'location' => ['ir', 'Iran', 'Karaj'],
            ],
            'it2.node.check-host.net' => [
                'asn' => 'AS60798',
                'ip' => '*************',
                'location' => ['it', 'Italy', 'Milan'],
            ],
            'jp1.node.check-host.net' => [
                'asn' => 'AS149042',
                'ip' => '**************',
                'location' => ['jp', 'Japan', 'Tokyo'],
            ],
            'kz1.node.check-host.net' => [
                'asn' => 'AS203087',
                'ip' => '**************',
                'location' => ['kz', 'Kazakhstan', 'Karaganda'],
            ],
            'lt1.node.check-host.net' => [
                'asn' => 'AS198651',
                'ip' => '*************',
                'location' => ['lt', 'Lithuania', 'Vilnius'],
            ],
            'md1.node.check-host.net' => [
                'asn' => 'AS43289',
                'ip' => '**************',
                'location' => ['md', 'Moldova', 'Chisinau'],
            ],
            'nl1.node.check-host.net' => [
                'asn' => 'AS14576',
                'ip' => '***************',
                'location' => ['nl', 'Netherlands', 'Amsterdam'],
            ],
            'nl2.node.check-host.net' => [
                'asn' => 'AS206446',
                'ip' => '*************',
                'location' => ['nl', 'Netherlands', 'Meppel'],
            ],
            'pl1.node.check-host.net' => [
                'asn' => 'AS31229',
                'ip' => '***************',
                'location' => ['pl', 'Poland', 'Poznan'],
            ],
            'pl2.node.check-host.net' => [
                'asn' => 'AS203394',
                'ip' => '*************',
                'location' => ['pl', 'Poland', 'Warsaw'],
            ],
            'pt1.node.check-host.net' => [
                'asn' => 'AS44222',
                'ip' => '*************',
                'location' => ['pt', 'Portugal', 'Viana'],
            ],
            'rs1.node.check-host.net' => [
                'asn' => 'AS35779',
                'ip' => '*************',
                'location' => ['rs', 'Serbia', 'Belgrade'],
            ],
            'ru1.node.check-host.net' => [
                'asn' => 'AS14576',
                'ip' => '***************',
                'location' => ['ru', 'Russia', 'Moscow'],
            ],
            'ru2.node.check-host.net' => [
                'asn' => 'AS210644',
                'ip' => '*************',
                'location' => ['ru', 'Russia', 'Moscow'],
            ],
            'ru3.node.check-host.net' => [
                'asn' => 'AS216246',
                'ip' => '**************',
                'location' => ['ru', 'Russia', 'Saint Petersburg'],
            ],
            'ru4.node.check-host.net' => [
                'asn' => 'AS41925',
                'ip' => '*************',
                'location' => ['ru', 'Russia', 'Ekaterinburg'],
            ],
            'se1.node.check-host.net' => [
                'asn' => 'AS200019',
                'ip' => '************',
                'location' => ['se', 'Sweden', 'Tallberg'],
            ],
            'sg1.node.check-host.net' => [
                'asn' => 'AS141995',
                'ip' => '**************',
                'location' => ['sg', 'Singapore', 'Singapore'],
            ],
            'tr1.node.check-host.net' => [
                'asn' => 'AS211557',
                'ip' => '************',
                'location' => ['tr', 'Turkey', 'Istanbul'],
            ],
            'tr2.node.check-host.net' => [
                'asn' => 'AS44620',
                'ip' => '*************',
                'location' => ['tr', 'Turkey', 'Gebze'],
            ],
            'ua1.node.check-host.net' => [
                'asn' => 'AS201094',
                'ip' => '*************',
                'location' => ['ua', 'Ukraine', 'Khmelnytskyi'],
            ],
            'ua2.node.check-host.net' => [
                'asn' => 'AS203394',
                'ip' => '*************',
                'location' => ['ua', 'Ukraine', 'Kyiv'],
            ],
            'ua3.node.check-host.net' => [
                'asn' => 'AS14593',
                'ip' => '*************',
                'location' => ['ua', 'Ukraine', 'SpaceX Starlink'],
            ],
            'uk1.node.check-host.net' => [
                'asn' => 'AS44477',
                'ip' => '*************',
                'location' => ['gb', 'UK', 'Coventry'],
            ],
            'us1.node.check-host.net' => [
                'asn' => 'AS18978',
                'ip' => '*************',
                'location' => ['us', 'USA', 'Los Angeles'],
            ],
            'us2.node.check-host.net' => [
                'asn' => 'AS215703',
                'ip' => '************',
                'location' => ['us', 'USA', 'Dallas'],
            ],
            'us3.node.check-host.net' => [
                'asn' => 'AS207713',
                'ip' => '**************',
                'location' => ['us', 'USA', 'Atlanta'],
            ],
            'vn1.node.check-host.net' => [
                'asn' => 'AS63760',
                'ip' => '**************',
                'location' => ['vn', 'Vietnam', 'Ho Chi Minh City'],
            ],
        ];

        // اگر پاسخ معتبر نبود، fallback استفاده شود
        if (! is_array($checkResponse) || ! isset($checkResponse['nodes'])) {
            $nodes = $fallbackNodes;

        } else {
            $nodes = $checkResponse['nodes'];
        }

        foreach ($nodes as $host => $info) {
            if (! isset($info['location'])) {
                continue;
            }

            [$code, $country, $city] = $info['location'];

            \App\Models\NodeHostChecker::updateOrCreate(
                ['hostname' => $host],
                [
                    'country_code' => $code,
                    'country' => $country,
                    'city' => $city,
                    'ip' => $info['ip'] ?? null,
                    'asn' => $info['asn'] ?? null,
                    'active' => true,
                ]
            );
        }

        return ['count' => count($nodes)];
    }
}
