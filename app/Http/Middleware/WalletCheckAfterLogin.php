<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class WalletCheckAfterLogin
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $AMOUNT_PAYMENT_REQUEST_CAR = (float) str_replace(',', '', env('AMOUNT_PAYMENT_REQUEST_CAR'));
        $USER_BALANCE = (float) str_replace(',', '', auth()->user()->balance);

        $userKhalafiyarTransfer = getTransferAppSetting();

        if ($userKhalafiyarTransfer) {
            $USER_BALANCE = $USER_BALANCE + getSourceBalance(auth()->user()->phone, 'khodroyar') ?? 0;
        }

        if ($USER_BALANCE < $AMOUNT_PAYMENT_REQUEST_CAR) {
            return $this->sendError('خلافی', ['message' => 'موجودی شما برای دریافت استعلام خلافی کافی نمی باشد'], Response::HTTP_PAYMENT_REQUIRED);
        }

        return $next($request);
    }

    public function sendError($error, $errorMessages = [], $code = Response::HTTP_NOT_FOUND)
    {
        $response = [
            'success' => false,
            // 'message' => $error,
        ];

        if (! empty($errorMessages)) {
            $response['data'] = $errorMessages;
        }

        $response['status'] = $code;

        return response()->json($response, $code);
    }
}
