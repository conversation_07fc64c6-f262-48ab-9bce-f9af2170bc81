<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class Sitemap extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $url = $this->page == null ? 'https://khodrox.com/blog/' . $this->slug : 'https://khodrox.com' . $this->page;
        $priority = $this->page == null ? 0.7 : 0.8;
        $changeFrequency = $this->page == null ? 'monthly' : 'weekly';
        $url = $this->page == null ? 'https://khodrox.com/blog/' . $this->slug : 'https://khodrox.com' . $this->page;
        return [
            'url' => $url,
            'lastModified' => $this->created_at->format('Y-m-d'),
            'changeFrequency' => $changeFrequency,
            'priority' => $priority,

        ];
    }
}
