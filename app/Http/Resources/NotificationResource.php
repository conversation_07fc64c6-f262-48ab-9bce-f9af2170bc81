<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class NotificationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'app' => $this->app,
            'agent' => $this->agent,
            'ip' => $this->ip,
            'path' => $this->path,
            'token' => $this->token,
            'status' => $this->status,
        ];
    }
}
