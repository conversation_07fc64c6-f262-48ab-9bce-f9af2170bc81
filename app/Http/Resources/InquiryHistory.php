<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class InquiryHistory extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'trace_number' => $this->id,
            'type' => $this->type,
            'detail' => $this->detail_phone != null ? true : false,
            'detail_phone' => $this->detail_phone,
            'detail_national_id' => $this->detail_national_id,
            'plaque' => $this->data['plaque'],
            'status_transaction' => $this->status,
            'created_at' => shamsiDate($this->created_at),
        ];
    }
}
