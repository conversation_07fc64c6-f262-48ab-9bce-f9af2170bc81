<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LicenseStatusResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'CardDateTime' => $this->safeShamsi('CardDateTime'),
            'CardIssuanceDateTime' => $this->safeShamsi('CardIssuanceDateTime'),
            'CardPostalBarcode' => data_get($this, 'CardPostalBarcode'),
            'CardTitle' => data_get($this, 'CardTitle'),
            'CardType' => data_get($this, 'CardType'),
            'DocumentDateTime' => $this->safeShamsi('DocumentDateTime'),
            'DocumentIssuanceDateTime' => $this->safeShamsi('DocumentIssuanceDateTime'),
            'DocumentPostalBarcode' => data_get($this, 'DocumentPostalBarcode'),
            'DocumentTitle' => data_get($this, 'DocumentTitle'),
            'DocumentType' => data_get($this, 'DocumentType'),
            'IsCardSmart' => data_get($this, 'IsCardSmart'),
            'Plaque' => data_get($this, 'PlateNumber'),
        ];
    }

    private function safeShamsi(string $field): ?string
    {
        $value = data_get($this, $field);
        return $value ? shamsiDate($value) : null;
    }
}
