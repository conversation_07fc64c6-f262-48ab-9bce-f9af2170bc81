<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DrivingLicenseStatusResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'Barcode' => $this['Barcode'] ?? null,
            'DateTime' => (!empty($this['DateTime']) && $this['DateTime'] !== '-' && $this['DateTime'] !== 'نامشخص') ? shamsiDate($this['DateTime']) : null,
            'FirstName' => $this['FirstName'] ?? null,
            'LastName' => $this['LastName'] ?? null,
            'NationalID' => $this['NationalID'] ?? null,
            'Number' => $this['Number'] ?? null,
            'PrintConfirmDateTime' => (!empty($this['PrintConfirmDateTime']) && $this['PrintConfirmDateTime'] !== '-' && $this['PrintConfirmDateTime'] !== 'نامشخص') ? shamsiDate($this['PrintConfirmDateTime']) : null,
            'PrintLicenseDateTime' => (!empty($this['PrintLicenseDateTime']) && $this['PrintLicenseDateTime'] !== '-' && $this['PrintLicenseDateTime'] !== 'نامشخص') ? shamsiDate($this['PrintLicenseDateTime']) : null,
            'RequestDateTime' => (!empty($this['RequestDateTime']) && $this['RequestDateTime'] !== '-' && $this['RequestDateTime'] !== 'نامشخص') ? shamsiDate($this['RequestDateTime']) : null,
            'Status' => $this['Status'] ?? null,
            'Type' => $this['Type'] ?? null,
            'ValidityPeriodInYears' => $this['ValidityPeriodInYears'] ?? null,
        ];
    }

}
