<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class InsuranceStatusResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $params = $this['Parameters'] ?? [];
        $contract = $params['ThirdPartyInsuranceContractInfo'] ?? [];
        $vehicle = $params['ThirdPartyInsuranceVehicleInfo'] ?? [];
        $loss = $params['ThirdPartyInsuranceVehicleLossInfo'] ?? [];

        $pdfUrl = $params['PdfUrl'] ?? null;
        $localPdfUrl = null;

        if ($pdfUrl) {
            try {
                $folder = 'insurances/third_party';


                $filename = basename(parse_url($pdfUrl, PHP_URL_PATH));
                if (!Str::endsWith(strtolower($filename), '.pdf')) {
                    $filename .= '.pdf';
                }

                $path = $folder . '/' . $filename;
                $counter = 1;


                if (Storage::disk('public')->exists($path)) {
                    $localPdfUrl = Storage::url($path);
                } else {

                    while (Storage::disk('public')->exists($path)) {
                        $nameWithoutExt = pathinfo($filename, PATHINFO_FILENAME);
                        $ext = pathinfo($filename, PATHINFO_EXTENSION);
                        $newFilename = $nameWithoutExt . '-' . $counter . '.' . $ext;
                        $path = $folder . '/' . $newFilename;
                        $counter++;
                    }


                    $fileContents = @file_get_contents($pdfUrl);
                    if ($fileContents !== false) {
                        Storage::disk('public')->put($path, $fileContents);
                        $localPdfUrl = Storage::url($path);
                    }
                }
            } catch (\Exception $e) {
                $localPdfUrl = null;
            }
        }

        return [
            'CompanyName' => $contract['InsuranceCompanyName'] ?? $params['CompanyName'] ?? null,
            'InsuranceNumber' => $contract['InsuranceNumber'] ?? $params['CompanyDocumentName'] ?? null,
            'UniqueInsuranceCode' => $contract['UniqueInsuranceCode'] ?? null,
            'InsuranceType' => $contract['InsuranceType'] ?? null,

            'FullName' => $contract['FullName'] ?? $params['FullName'] ?? null,
            'NationalID' => $contract['NationalID'] ?? null,

            'StartDate' => isset($contract['StartDate']) ? shamsiDate($contract['StartDate']) : null,
            'EndDate' => isset($contract['EndDate']) ? shamsiDate($contract['EndDate']) : null,
            'IssueDate' => isset($contract['IssueDate']) ? shamsiDate($contract['IssueDate']) : null,

            'Vehicle' => [
                'PlateNumber' => $vehicle['PlateNumber'] ?? $params['PlateNumber'] ?? null,
                'VehicleName' => $vehicle['VehicleName'] ?? null,
                'EngineNumber' => $vehicle['EngineNumber'] ?? null,
                'ChassisNumber' => $vehicle['ChassisNumber'] ?? null,
                'VIN' => $vehicle['VIN'] ?? null,
                'YearsWithoutLoss' => [
                    'Driver' => $vehicle['YearsWithoutDriverLoss'] ?? null,
                    'Health' => $vehicle['YearsWithoutHealthLoss'] ?? null,
                    'Financial' => $vehicle['YearsWithoutFinancialLoss'] ?? null,
                ],
                'DiscountPercent' => [
                    'Driver' => $vehicle['DiscountPercentOnDriverLoss'] ?? null,
                    'Health' => $vehicle['DiscountPercentOnHealthLoss'] ?? null,
                    'Financial' => $vehicle['DiscountPercentOnFinancialLoss'] ?? null,
                ],
            ],

            'LossHistory' => [
                'LossCount' => $loss['LossCount'] ?? null,
                'LossSum' => $loss['LossSum'] ?? null,
            ],

            'PdfUrl' => 'https://dl.khodrox.com' . $localPdfUrl,
        ];
    }
}
