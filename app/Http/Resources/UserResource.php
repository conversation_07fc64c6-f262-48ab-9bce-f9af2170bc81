<?php

namespace App\Http\Resources;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $userKhalafiyarTransfer = getTransferAppSetting();

        $sumBalance = 0;
        $sumBalance = str_replace(',', '', $this->balance);

        if ($userKhalafiyarTransfer) {
            $userBalanceKhalafiyar = $this->getSourceBalance($this->phone ?? '', 'khodroyar');
            $sumBalance += $userBalanceKhalafiyar;

        }

        return [
            'source' => $this->source,
            'fullname' => $this->fullname ?? '',
            'phone' => $this->phone,
            'balance' => formatMoney($sumBalance),
            'roles' => $this->roles->pluck('name')->first() ?? 'user',
            'permissions' => $this->roles
                ->flatMap(function ($role) {
                    return $role->permissions->pluck('name');
                })
                ->unique()
                ->values() ?? [],
        ];

    }

    public function getSourceBalance(string $phone, string $source): float
    {
        $balance = User::where('phone', $phone)
            ->where('source', $source)
            ->value('balance');

        $balance = str_replace([',', ' '], '', $balance);

        return (float) $balance ?? 0.0;
    }
}
