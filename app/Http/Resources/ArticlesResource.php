<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ArticlesResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'title' => $this->title ?? null,
            'slug' => $this->slug ?? null,
            'summary' => $this->summary ?? null,
            'comments_count' => 3,
            // 'author' => $this->author?->fullname,
            'author_fullname' => $this->author->fullname ?? 'مدیر سایت',
            'author_avatar' => null,
            'study_time' => $this->study_time . ' دقیقه ',
            'cover' => 'https://dl.khodrox.com/' . $this->galleries()?->latest()?->first()?->image ?? null,
            'date' => shamsiDate($this->updated_at) ?? null,
            'category' => [
                'title' => $this->category->title ?? null,
                'slug' => $this->category->slug ?? null,
            ],
        ];
    }
}
