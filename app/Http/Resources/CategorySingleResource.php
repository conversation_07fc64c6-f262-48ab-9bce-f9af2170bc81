<?php

namespace App\Http\Resources;

use App\Models\Article;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CategorySingleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $articles = Article::where('type', 'article')
            ->where('status', 'published')
            ->where('category_id', $this->id)
            ->latest()
            ->simplePaginate(10);

        return [
            'type' => 'category',
            'title' => $this->title ?? null,
            'title_eng' => $this->title_eng ?? null,
            'slug' => $this->slug ?? null,
            'title_article' => $this->title_article ?? null,
            'description' => $this->description ?? null,
            'meta_title' => $this->meta_title ?? null,
            'meta_description' => $this->meta_description ?? null,
            'meta_search' => $this->meta_search ?? null,
            'canonical' => $this->canonical ?? null,
            'articles' => ArticlesResource::collection($articles),
        ];
    }
}
