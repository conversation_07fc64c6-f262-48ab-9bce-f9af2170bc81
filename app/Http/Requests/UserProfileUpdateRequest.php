<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UserProfileUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'fullname' => 'required|string|min:3|max:255',
            'landline_number' => 'required|string', // فقط عددی بین 8 تا 15 رقم
            'national_id' => 'required|string', // فقط 10 رقم عددی
            'education' => 'required|string|min:3|max:32',
            'date_of_birth' => 'required|string|min:5|max:20',
            'email' => 'required|string|min:8|max:100',
            'job' => 'required|string|min:3|max:100',
        ];
    }
}
