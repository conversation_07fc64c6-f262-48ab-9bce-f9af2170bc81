<?php

namespace App\Notifications;

use App\Notifications\Channels\BaleChannel;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;

class QabzinoServiceDown extends Notification implements ShouldQueue
{
    use Queueable;

    protected string $message;

    public function __construct(string $message)
    {
        $this->message = $message;
    }

    public function via($notifiable): array
    {
        return [BaleChannel::class];
    }

    public function toBale($notifiable): string
    {
        return $this->message;
    }
}
