<?php

namespace App\Exports;

use App\Models\Sefaresh;
use Maatwebsite\Excel\Concerns\FromCollection;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\WithHeadings;
class SefareshsExport implements FromCollection, WithHeadings
{
    /**
    * @return \Illuminate\Support\Collection
    */
	public function __construct($MonthlyOne , $MonthlyTow, $year)
    {
		
        $this->MonthlyOne = $MonthlyOne;
		$this->MonthlyTow = $MonthlyTow;
		$this->year = $year;
    }
	
	
	public function headings(): array
    {
        return [ 'id', 'آیدی_کاربر', 'آیدی_چت', 'کدسفارش', 'دسته بندی',
			'نوع ساخت', 'مدل', 'جنسیت', 'رنگ', 'فونت',
			'نام پلاک', 'نوع ارسال', 'کدرهگیری', 'توضیحات', 'مبلغ کالا', 'بیعانه اول', 'بیعانه دوم', 'باقی مانده', 
			'جمع کل سفارش', 'نوع پکیج', 'مبلغ پکیج', 'نام سفارش دهنده', 'شماره تماس', 'آدرس', 'کدپستی', 'زنجیر(نوع و جنس)', 'سایز', 'size_wrist',
			'size_ankle', 'تاریخ ثبت سفارش', 'تاریخ مدنظر مشتری', 'وضعیت سفارش', 'تاریخ آخرین وضعیت', 'واتس آپ', 'شماره واتس آپ', 
			'product_code', 'آیدی_دیزاینر', 'آیدی_سازنده','ابعاد سفارش',
			'خوانده شده', 'financial', 'img1','img2','img3','img4','img5','img6','img7',
			'سال','ماه','روز','بوکمارک شده','وضعیت','چکمارک','تاریخ ایجاد','تاریخ اپدیت'
        ];
    }
    public function collection()
    {
		//$query = Sefaresh::whereBetween('month', array($this->MonthlyOne,$this->MonthlyTow))->where('year',$this->year)->get();
		//return dd($query);
		if(auth()->user()->level == 'admin'){

			return Sefaresh::whereBetween('month', array($this->MonthlyOne,$this->MonthlyTow))->where('year',$this->year)->get();
		}
		return Sefaresh::where('user_id', auth()->user()->id)->whereBetween('month', array($this->MonthlyOne,$this->MonthlyTow))->where('year',$this->year)->get();
							
       // return Sefaresh::whereBetween('month', array($this->MonthlyOne,$this->MonthlyTow))
		///					->where('year',$this->year)
		//					->select(array('id', 'user_id'))->get();
    }
}
