<?php

namespace App\Exports;

use App\Models\Sefaresh;
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\BeforeWriting;
use Maatwebsite\Excel\Files\LocalTemporaryFile;
use Maatwebsite\Excel\Excel;
use Maatwebsite\Excel\Sheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class Sample implements WithEvents
{
    public $orderId;
    public function __construct($id)
    {
        $this->orderId = $id;
    }

    public function registerEvents(): array
    {
        return [
            BeforeWriting::class => function(BeforeWriting $event) {
                $templateFile = new LocalTemporaryFile('tmp/factor.xlsx');
                $event->writer->reopen($templateFile, Excel::XLSX);
                $sheet = $event->writer->getSheetByIndex(0);

                $this->populateSheet($sheet);
                
                $event->writer->getSheetByIndex(0)->export($event->getConcernable()); // call the export on the first sheet

                return $event->getWriter()->getSheetByIndex(0);
            }
        ];
    }
    public static function afterSheet(AfterSheet $event)
    {
        //
    }
    
    private function populateSheet($sheet){


        $order = Sefaresh::whereId($this->orderId)->first();
        // Populate the static cells
        $sheet->setCellValue('C4', $order->fullname);
        $sheet->setCellValue('F4', $order->phone);
        $sheet->setCellValue('H4', findSubscribe($order->phone));
        $sheet->setCellValue('I4', todays());

        $sheet->setCellValue('B7', '');
        $sheet->setCellValue('C7', $order->model);
        $sheet->setCellValue('E7', '0');
        $sheet->setCellValue('F7', getGold18k());
        $sheet->setCellValue('G7', '24%');
        $sheet->setCellValue('H7', '7%');

        $deposit1 = str_replace(',', '', $order->deposit1);
        $deposit2 = str_replace(',', '', $order->deposit2);
        $sum = (float) $deposit1 + (float) $deposit2;
        $sheet->setCellValue('I13', $sum);

        
    }

   
}