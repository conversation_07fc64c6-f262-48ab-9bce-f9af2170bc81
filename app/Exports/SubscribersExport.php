<?php

namespace App\Exports;

use App\Models\Subscribe;
use Maatwebsite\Excel\Concerns\FromCollection;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\WithHeadings;
class SubscribersExport implements FromCollection, WithHeadings
{
    /**
    * @return \Illuminate\Support\Collection
    */
	public function __construct()
    {

    }
	
	
	public function headings(): array
    {
        return [ 'id', 'شناسه کاربر','نام مشتری','کدپستی','شماره موبایل','آدرس','تاریخ ایجاد','تاریخ بروزرسانی'
        ];
    }
    public function collection()
    {
		return Subscribe::get();				
    }
}
