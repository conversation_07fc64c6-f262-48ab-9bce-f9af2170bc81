<?php

namespace App\Enums;

use App\Traits\EnumHelpers;
use App\Traits\EnumLabels;

enum StatusEnum: int
{
    use EnumLabels;
    use EnumHelpers;

    case REJECTED = 0;
    case PENDING = 1;
    case CONFIRMED = 2;


    /**
     * Create from string value with fallback to default.
     */
    public static function fromString(string $value): self
    {
        return match (strtolower($value)) {
            'rejected' => self::REJECTED,
            'pending' => self::PENDING,
            'confirmed' => self::CONFIRMED,
            default => self::PENDING,
        };
    }

}
