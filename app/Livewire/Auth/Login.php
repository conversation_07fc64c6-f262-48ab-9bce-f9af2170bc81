<?php

namespace App\Livewire\Auth;

use App\Jobs\AuthSMSJob;
use App\Models\User;
use App\Models\UserToken;
use App\Services\MeliPayamak as ServiceMeliPayamak;
use <PERSON><PERSON><PERSON><PERSON>\LivewireRateLimiting\Exceptions\TooManyRequestsException;
use <PERSON><PERSON><PERSON><PERSON>\LivewireRateLimiting\WithRateLimiting;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\Validate;
use Livewire\Component;

class Login extends Component
{
    use LivewireAlert;
    use WithRateLimiting;

    #[Validate('required|numeric')]
    public $phone;

    public $exError;

    public $page = 'login';

    public function login(Request $req)
    {

        try {

            $this->exError = '';
            $phone = faTOen($this->phone);

            $this->rateLimit(5);
            $this->validate();

            if (!preg_match('/^09[0-9]{9}$/', $phone)) {
                return $this->exError = 'فرمت شماره موبایل اشتباه است!';
            }

            $user = User::where('phone', $phone)->where('admin', true)->latest()->first();
            // dd($user->toArray());
            if (isset($user) && $user->count() > 0) {
                return $this->sendToken($phone, $user->fullname, $req);
            } else {
                // setUserLog($req, $phone, 'login-failed');
                return $this->exError = 'شماره مورد نظر در سامانه یافت نشد!';
            }

        } catch (TooManyRequestsException $exception) {
            $this->exError = 'تلاش های زیادی برای ورود به سیستم انجام شده. لطفا چند دقیقه دیگر دوباره اقدام به ورود کنید';
            throw ValidationException::withMessages([
                'message' => "تلاش های زیادی برای ورود انجام شده، لطفاً  {$exception->secondsUntilAvailable} ثانیه صبر کنید و سپس دوباره اقدام به ورود کنید",
            ]);
        }

    }

    private function sendToken($phone, $username, $req)
    {

        $token = rand(10000, 99990);

        UserToken::create([
            'phone' => (string) $phone,
            'token' => (int) $token,
            'status' => 0,
            'source' => 'admin-panel',
            'ip' => $req->ip(),
            'agent' => $req->userAgent(),
        ]);

        if (app()->isProduction()) {

            try {
                $message = "🔐 *کد فعال‌سازی ورود به پنل مدیریت*\n\n";
                $message .= "👤 *شماره موبایل:* `{$phone}`\n";
                $message .= "📥 *کد ورود:* `{$token}`\n";
                $message .= '📅 *تاریخ:* ' . dateTimeToday();

                sendQabzinoBot($message);
            } catch (\Exception $e) {
                $errorMessage = "❗️*خطا هنگام ارسال پیام به ربات*\n";
                $errorMessage .= '📄 *پیام خطا:* ' . $e->getMessage() . "\n";
                $errorMessage .= '📁 *فایل:* ' . $e->getFile() . "\n";
                $errorMessage .= '🔢 *خط:* ' . $e->getLine();

                sendQabzinoBot($errorMessage);
            }

            (new ServiceMeliPayamak)->sendAuthOtp($token, $phone, '223285', 'admin-panel');
            // dispatch(new AuthSMSJob($token, $phone, 223285, 'admin-panel'));
        }

        $this->exError = '';
        $this->page = 'verfication';

        session(['accept_phone_auth_phone' => $phone]);

        return $this->alert('success', 'کدفعال سازی پیامک شد!', [
            'position' => 'top-start',
        ]);

    }

    public function render()
    {
        return view('livewire.auth.login');
    }
}
