<?php

namespace App\Livewire\Auth;

use App\Models\User;
use App\Models\UserToken;
use <PERSON><PERSON><PERSON><PERSON>\LivewireRateLimiting\Exceptions\TooManyRequestsException;
use <PERSON><PERSON><PERSON><PERSON>\LivewireRateLimiting\WithRateLimiting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\Validate;
use Livewire\Component;

class Verfication extends Component
{
    use LivewireAlert;
    use WithRateLimiting;

    public $exError;

    #[Validate('required')]
    public $token;

    public function mount()
    {
        $this->exError = '';
    }

    public function verfication(Request $req)
    {
        try {

            $phone = session('accept_phone_auth_phone');

            $this->exError = '';
            $this->rateLimit(5);
            $this->validate();

            $token = (int) $this->token;
            $token = UserToken::where('phone', $phone)->where('token', $token)->where('source', 'admin-panel')->where('status', 0)->latest()->first();

            // dd($phone, $this->token, $token);
            if (isset($token) && $token->count() > 0) {
                $currentTime = now();
                $tokenTime = $token->created_at;

                if ($currentTime->diffInMinutes($tokenTime) > 5) {
                    $token->status = 2;
                    $token->save();
                    $this->token = '';

                    return $this->exError = 'کد پیامکی اشتباه است یا منقضی شده، مدت اعتبار کدپیامکی 3 دقیقه می باشد';
                }

                // setUserLog($req, $phone, 'verfication');
                // dd($user->toArray());
                $user = User::where('phone', $token->phone)->where('admin', true)->first();
                if (!$user) {
                    return $this->exError = 'اطلاعات وارد شده اشتباه است';
                }

                // ورود به سیستم با استفاده از شیء $user
                Auth::login($user, true); // `true` برای حفظ وضعیت ورود (remember me)

                if (app()->isProduction()) {
                    try {
                        $message = "🚪 *ورود به پنل مدیریت*\n\n";
                        $message .= "📱 *شماره موبایل:* `{$user->phone}`\n";
                        $message .= '🕰 *تاریخ و زمان:* ' . dateTimeToday();

                        sendQabzinoBot($message);
                    } catch (\Exception $e) {
                        $errorMessage = "❗️ *خطا هنگام ارسال پیام ورود به پنل مدیریت*\n";
                        $errorMessage .= '📄 *خطا:* ' . $e->getMessage() . "\n";
                        $errorMessage .= '📁 *فایل:* ' . $e->getFile() . "\n";
                        $errorMessage .= '🔢 *خط:* ' . $e->getLine();

                        sendQabzinoBot($errorMessage);
                    }
                }


                UserToken::where('phone', $phone)->where('source', 'admin-panel')->update([
                    'status' => 1,
                ]);

                $this->alert('success', 'با موفقیت وارد سامانه شدید', [
                    'position' => 'top-start',
                ]);


                // dd($user, $user->roles()->first(), $user->roles()->first()->redirectPath());
                if ($user) {
                    $role = $user->roles()->first();

                    if ($role) {
                        return redirect($role->redirectPath());
                    } else {
                        return redirect('/');
                    }
                } else {
                    return redirect('/');
                }

            } else {
                $this->token = '';

                return $this->exError = 'اطلاعات اشتباه می باشد. بعد از 3 بار تلاش اشتباه کدپیامکی شما دیگر اعتبار نخواهد داشت';
            }

        } catch (TooManyRequestsException $exception) {
            UserToken::where('phone', $phone)->where('source', 'admin-panel')->update([
                'status' => 1,
            ]);
            $this->exError = 'تلاش های زیادی برای ورود به سیستم انجام شده. لطفا چند دقیقه دیگر دوباره اقدام به ورود کنید';
            throw ValidationException::withMessages([
                'message' => "تلاش های زیادی برای ورود انجام شده، لطفاً  {$exception->secondsUntilAvailable} ثانیه صبر کنید و سپس دوباره اقدام به ورود کنید",
            ]);
        }
    }

    public function render()
    {
        return view('livewire.auth.verfication');
    }
}
