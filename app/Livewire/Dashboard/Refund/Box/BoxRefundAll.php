<?php

namespace App\Livewire\Dashboard\Refund\Box;

use Illuminate\Support\Facades\DB;
use Livewire\Component;

class BoxRefundAll extends Component
{
    public string $data;

    public string $title;

    public $icon;

    public $class;

    public function mount()
    {
        $transactionRefundAll = cache()->remember('transactions_refund_all', 15, function () {
            return DB::connection('mongodb')
                ->table('transaction_refunds')
                ->count();
        });

        $this->data = formatMoney($transactionRefundAll);
    }

    public function render()
    {
        return view('livewire.dashboard.refund.box.box-refund-all');
    }
}
