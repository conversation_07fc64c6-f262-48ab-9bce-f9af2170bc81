<?php

namespace App\Livewire\Dashboard\Refund\Box;

use Illuminate\Support\Facades\DB;
use Livewire\Component;

class BoxRefundAllMoney extends Component
{
    public string $data;

    public string $title;

    public $icon;

    public $class;

    public function mount()
    {
        $transactionRefundMoneyAll = cache()->remember('transactions_refund_money_all', 15, function () {
            return DB::connection('mongodb')
                ->table('transaction_refunds')
                ->sum('refound_amount');
        });

        $this->data = formatMoney($transactionRefundMoneyAll);
    }

    public function render()
    {
        return view('livewire.dashboard.refund.box.box-refund-all-money');
    }
}
