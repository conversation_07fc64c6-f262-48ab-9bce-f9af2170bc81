<?php

namespace App\Livewire\Dashboard\Refund\Box;

use Illuminate\Support\Facades\DB;
use Livewire\Component;

class BoxRefundSuccess extends Component
{
    public string $data;

    public string $title;

    public $icon;

    public $class;

    public function mount()
    {
        $transactionRefundSuccessAll = cache()->remember('transactions_refund_success_all', 15, function () {
            return DB::connection('mongodb')
                ->table('transaction_result_refunds')
                ->whereNotNull('result.data.resource.terminal_id')
                ->whereNotNull('result.data.resource.amount')
                ->whereNull('result.original.error')
                ->sum('result.data.resource.amount');
        });

        $this->data = formatMoney($transactionRefundSuccessAll);

        // dd($this->data);
    }

    public function render()
    {
        return view('livewire.dashboard.refund.box.box-refund-success');
    }
}
