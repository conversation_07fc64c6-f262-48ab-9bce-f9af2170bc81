<?php

namespace App\Livewire\Dashboard\Refund\Box;

use Illuminate\Support\Facades\DB;
use Livewire\Component;

class BoxRefundReject extends Component
{
    public string $data;

    public string $title;

    public $icon;

    public $class;

    public function mount()
    {
        $transactionRefundRejectAll = cache()->remember('transactions_refund_reject_all', 15, function () {
            return DB::connection('mongodb')
                ->table('transaction_result_refunds')
                ->whereNotNull('result.data.resource.terminal_id')
                ->count();
        });

        $this->data = formatMoney($transactionRefundRejectAll);
    }

    public function render()
    {
        return view('livewire.dashboard.refund.box.box-refund-reject');
    }
}
