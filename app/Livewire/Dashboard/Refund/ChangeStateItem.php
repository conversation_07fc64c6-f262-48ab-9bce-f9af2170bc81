<?php

namespace App\Livewire\Dashboard\Refund;

use App\Jobs\ZarinpalRefundTransactionJob;
use App\Models\TransactionRefund;
use Jan<PERSON><PERSON>ezo\LivewireAlert\LivewireAlert;
use LivewireUI\Modal\ModalComponent;

class ChangeStateItem extends ModalComponent
{
    use LivewireAlert;

    public $transactionId;

    public $phone;

    public $status;

    public function rules()
    {
        return [
            'status' => 'required',
            // 'transactionId' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'status.required' => 'وضعیت درخواست را مشخص کنید',
            // 'transactionId.required' => 'آیدی تراکنش را مشخص کنید',
        ];
    }

    public function store()
    {

        $this->validate();

        TransactionRefund::whereId($this->transactionId)->update([
            'status_request' => $this->status,
            'status_gate' => $this->status,
            'pause' => $this->status != 'pending' ? true : false,
        ]);

        dispatch(new ZarinpalRefundTransactionJob);

        $this->alert('success', 'ثبت موفق درخواست', [
            'position' => 'center',
            'timer' => 3000,
            'toast' => false,
            'text' => 'درخواست عودتی وجه ها در سیستم ثبت شد بزودی پروسه عودت وجه شروع می شود',
            'timerProgressBar' => true,
            'showDenyButton' => true,
            'denyButtonText' => 'بسیار خب متوجه شدم',
        ]);

        $this->closeModal();
    }

    public function resetState() {}

    public function render()
    {
        return view('livewire.dashboard.refund.change-state-item');
    }
}
