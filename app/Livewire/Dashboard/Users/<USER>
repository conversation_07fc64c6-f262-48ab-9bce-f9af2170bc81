<?php

namespace App\Livewire\Dashboard\Users;

use App\Models\Role;
use App\Models\User;
use <PERSON><PERSON><PERSON>ezo\LivewireAlert\LivewireAlert;
use LivewireUI\Modal\ModalComponent;

class CreateUser extends ModalComponent
{
    use LivewireAlert;

    public $data = [
        'fullname' => null,
        'phone' => '',
        'source' => null,
        'level' => null,
        'balance' => null,
    ];

    public function resetState() {}

    public function save()
    {

        $phone = trim($this->data['phone']);

        if (! preg_match('/^0[0-9]+$/', $phone)) {
            $phone = '0'.$phone;
        }

        $user = User::where('phone', $phone)->where('source', $this->data['source'])->first();

        if ($user) {
            $this->alert('error', 'خطا در ثبت اطلاعات', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => 'این شماره موبایل در سرویس مورد نظر قبلا عضو شده است در صورتی که میخواهید نقش یا موجودی کاربر رو اصلاح کنید کافیست توی لیست کاربران شماره موبایل مورد نظر را سرچ کنید',
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);

            return;
        }

        $admin = $this->data['level'] == 'user' ? false : true;

        User::create([
            'phone' => $phone,
            'source' => $this->data['source'],
            'balance' => $this->data['balance'] ?? 0,
            'level' => $this->data['level'],
            'admin' => $admin,
        ]);

        $this->alert('success', 'کاربر جدید با موفقیت ایجاد شد', [
            'position' => 'top-start',
        ]);

        $this->closeModal();

    }

    public function render()
    {
        return view('livewire.dashboard.users.create-user', [
            'roles' => Role::get(),
        ]);
    }
}
