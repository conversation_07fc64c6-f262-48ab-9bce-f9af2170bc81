<?php

namespace App\Livewire\Dashboard\Users;

use App\Models\Role;
use App\Models\User;
use <PERSON><PERSON><PERSON><PERSON>o\LivewireAlert\LivewireAlert;
use <PERSON>wireUI\Modal\ModalComponent;

class UserDetailShow extends ModalComponent
{
    use LivewireAlert;

    public $userId;

    public array $data = [
        'phone' => null,
        'fullname' => null,
        'level' => null,
    ];

    public function mount()
    {
        $user = User::whereId($this->userId)->firstorFail();
        $this->data['fullname'] = $user->fullname;
        $this->data['phone'] = $user->phone;
        $role = $user->roles->first();
        $this->data['level'] = $role?->level ?? null;
    }

    public function resetState() {}

    public function update()
    {
        $user = User::whereId($this->userId)->firstOrFail();

        // شماره تلفن را درست تمیز کنیم:
        $phone = trim($this->data['phone']);
        if (! preg_match('/^0[0-9]+$/', $phone)) {
            $phone = '0'.$phone;
        }
        $user->phone = $phone;

        // تعیین admin بودن
        $user->admin = $this->data['level'] != 'user' ? true : false;

        if (! empty($this->data['level']) && $this->data['level'] != 'user') {
            $role = Role::where('name', $this->data['level'])->firstOrFail();

            $user->level = $role->level;

            $user->roles()->sync([$role->id]);
        } else {
            $user->level = null;

            $user->roles()->sync([]); // اینجا درست شد
        }

        $user->fullname = $this->data['fullname'];

        $user->save();

        $this->alert('success', 'اطلاعات با موفقیت بروزرسانی شد', [
            'position' => 'top-start',
        ]);
    }

    public function render()
    {
        return view('livewire.dashboard.users.user-detail-show', [
            'roles' => Role::get(),
        ]);
    }
}
