<?php

namespace App\Livewire\Dashboard\Users\ChangeBalanceWallet;

use App\Models\User;
use LivewireUI\Modal\ModalComponent;

class CheckUsersWallet extends ModalComponent
{
    public array $phones;

    public array $services;

    public function resetState() {}

    public function render()
    {

        $users = User::whereIn('phone', $this->phones)
            ->whereIn('source', $this->services)
            ->get();

        return view('livewire.dashboard.users.change-balance-wallet.check-users-wallet', compact('users'));
    }
}
