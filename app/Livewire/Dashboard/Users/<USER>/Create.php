<?php

namespace App\Livewire\Dashboard\Users\RolePermission;

use App\Models\Permission;
use App\Models\Role;
use <PERSON><PERSON><PERSON>ezo\LivewireAlert\LivewireAlert;
use LivewireUI\Modal\ModalComponent;

class Create extends ModalComponent
{
    use LivewireAlert;

    public $name;

    public $label;

    public $level;

    public $redirect_to;

    public $data = [];

    protected $listeners = ['showModal'];

    public function showModal()
    {

        $this->data = [];
    }

    public function resetState()
    {
    }

    protected $rules = [
        'name' => 'required|unique:roles',
        'label' => 'required',
    ];

    public function save()
    {

        $this->validate();

        $role = Role::Create([
            'name' => $this->name,
            'label' => $this->label,
            'level' => $this->level,
            'redirect_to' => $this->redirect_to,
        ]);
        $role->permissions()->sync($this->data);

        $this->alert('success', 'مقام جدید با موفقیت ثبت شد', [
            'position' => 'top-start',
        ]);

        $this->name = '';
        $this->label = '';

        // $this->dispatchBrowserEvent('modalClose', ['modalName' => 'RolesModal']);
    }

    public function render()
    {
        return view('livewire.dashboard.users.role-permission.create', [
            'permissions' => Permission::get(),
        ]);
    }
}
