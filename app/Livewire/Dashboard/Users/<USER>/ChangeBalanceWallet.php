<?php

namespace App\Livewire\Dashboard\Users\ChangeBalanceWallet;

use App\Models\User;
use <PERSON><PERSON><PERSON>ezo\LivewireAlert\LivewireAlert;
use LivewireUI\Modal\ModalComponent;

class ChangeBalanceWallet extends ModalComponent
{
    use LivewireAlert;

    public array $data = [
        'phones' => null,
        'khalafiyar' => true,
        'khodroyar' => true,
        'naghlie' => true,
        'khalafionline' => true,
        'migrofin' => true,
        'khodrox' => true,
        'pishkhan724' => true,
        'balance' => 0,
    ];

    public function resetState() {}

    public function rules()
    {
        return [
            'data.phones' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'data.phones.required' => 'لیست شماره تلفن ها رو وارد کنید',
        ];
    }

    public function changeBalanceWallet()
    {

        $this->validate();

        $phones = array_map('trim', explode("\n", $this->data['phones']));

        $phones = array_map(function ($phone) {
            return preg_match('/^0[0-9]+$/', $phone) ? $phone : '0'.$phone;
        }, $phones);

        $activeSources = [];
        if ($this->data['khalafiyar']) {
            $activeSources[] = 'khalafiyar';
        }
        if ($this->data['khodroyar']) {
            $activeSources[] = 'khodroyar';
        }
        if ($this->data['naghlie']) {
            $activeSources[] = 'naghlie';
        }
        if ($this->data['khalafionline']) {
            $activeSources[] = 'khalafionline';
        }
        if ($this->data['migrofin']) {
            $activeSources[] = 'migrofin';
        }
        if ($this->data['khodrox']) {
            $activeSources[] = 'khodrox';
        }

        if ($this->data['pishkhan724']) {
            $activeSources[] = 'pishkhan724';
        }

        if (empty($activeSources)) {
            $this->alert('error', 'خطا در اعتبارسنجی', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => 'درخواست شما شامل هیچ سرویسی نمی باشد لطفا حداقل یک سرویس را انتخاب کنید',
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);

            return;
        }

        $balance = (float) str_replace(',', '', $this->data['balance']);
        User::whereIn('phone', $phones)
            ->whereIn('source', $activeSources)
            ->update([
                'balance' => (float) $balance,
            ]);

        // $users = User::whereIn('phone', $phones)->whereIn('source', $activeSources)->get()->toArray();
        // dd($users);
        // $this->closeModal();
        // $this->dispatch('openModal', 'dashboard.users.change-balance-wallet.check-users-wallet', ['arguments' => ['phones' => $phones, 'services' => $activeSources]]);
        $this->dispatch('openModal', 'dashboard.users.change-balance-wallet.check-users-wallet', [
            'phones' => $phones,
            'services' => $activeSources,
        ]);

        // $this->dispatch('check-users-wallet', ['phones' => $phones, 'services' => $activeSources]);

    }

    public function render()
    {
        return view('livewire.dashboard.users.change-balance-wallet.change-balance-wallet');
    }
}
