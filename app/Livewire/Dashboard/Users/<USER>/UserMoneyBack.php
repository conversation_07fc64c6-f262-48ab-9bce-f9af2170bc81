<?php

namespace App\Livewire\Dashboard\Users\MoneyBack;

use App\Jobs\ZarinpalRefundTransactionJob;
use App\Models\TransactionRefund;
use App\Models\User;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use LivewireUI\Modal\ModalComponent;

class UserMoneyBack extends ModalComponent
{
    use LivewireAlert;

    public array $data = [
        'phones' => null,
        'errors' => null,
        'isComplate' => false,
        'users' => [],
    ];

    public array $selectedUsers = [];

    public array $userInputs = [];

    public function rules()
    {
        return [
            'data.phones' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'data.phones.required' => 'لیست شماره تلفن ها رو وارد کنید',
        ];
    }

    public function resetState() {}

    public function backStep()
    {
        $this->data['isComplate'] = false;
    }

    public function save()
    {
        $this->validate();

        $phones = array_map('trim', explode("\n", $this->data['phones']));

        $phones = array_map(function ($phone) {
            return preg_match('/^0[0-9]+$/', $phone) ? $phone : '0'.$phone;
        }, $phones);

        $this->data['users'] = User::with('transactions')->whereIn('phone', $phones)->get();

        foreach ($this->data['users'] as $key => $item) {
            $this->userInputs[$key] = [
                'balance' => $item->balance,
                'card' => '',
            ];
            $this->selectedUsers[$key] = [
                'phone' => $item->phone,
                'current_balance' => $item->balance,
                'source' => $item->source,
                'input_balance' => $item->balance,
                'card_number' => '',
                'transactions' => $item->transactions->toArray(),
                'transaction_3_latest_amount' => $item->lastThreeTransactionTotal() ?? 0,
            ];
            $this->selectedUsers[$key] = true;
        }

        $this->data['isComplate'] = true;
    }

    public function processSelectedUsers()
    {

        if ($this->selectedUsers == []) {
            $this->alert('error', 'خطا در اعتبارسنجی', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => 'لطفا آيتم های مورد نظرتون رو انتخاب کنید',
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);

            return;
        }

        $selectedData = [];

        try {
            // dd($this->selectedUsers);
            foreach ($this->selectedUsers as $key => $isSelected) {
                if ($isSelected) {

                    $user = User::where('phone', $this->data['users'][$key]['phone'])->where('source', $this->data['users'][$key]['source'])->first();
                    if ($user) {

                        $user->balance = 2;
                        $user->save();

                        $balance = (float) str_replace(',', '', $this->data['users'][$key]['balance']);
                        $refound_amount = (float) str_replace(',', '', $this->userInputs[$key]['balance']);
                        $transaction_3_latest_amount = (float) str_replace(',', '', $this->data['users'][$key]->lastThreeTransactionTotal() ?? 0);
                        $after_balance = (float) $balance - $refound_amount;
                        TransactionRefund::create([
                            'phone' => $this->data['users'][$key]['phone'],
                            'card' => $this->userInputs[$key]['card'],
                            'gate' => 'zarinpal',
                            'source' => $this->data['users'][$key]['source'],
                            'terminal_id' => null,
                            'refound_amount' => $refound_amount,
                            'transactions' => $this->data['users'][$key]->transactions->toArray(),
                            'transaction_3_latest_amount' => $transaction_3_latest_amount,
                            'balance' => $balance,
                            'after_balance' => $after_balance >= 0 ? $after_balance : 2,
                            'method' => 'PAYA',
                            'description' => 'عودت وجه به درخواست کاربر : '.$this->data['users'][$key]['phone'],
                            'pause' => false,
                            'locked' => false,
                            'request_count' => 0,
                            'status_request' => 'pending',
                            'status_gate' => 'pending',
                        ]);
                    }

                }
            }

            dispatch(new ZarinpalRefundTransactionJob);

            $this->alert('success', 'ثبت موفق درخواست', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => 'درخواست عودتی وجه ها در سیستم ثبت شد بزودی پروسه عودت وجه شروع می شود',
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);

            $this->closeModal();

        } catch (\Throwable $e) {

            $this->alert('error', 'خطا!', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => 'مشکلی در پردازش اطلاعات پیش آمده است. لطفاً دوباره تلاش کنید.',
                'timerProgressBar' => true,
            ]);
        }
        // dd($selectedData);

    }

    public function render()
    {
        return view('livewire.dashboard.users.money-back.user-money-back');
    }
}
