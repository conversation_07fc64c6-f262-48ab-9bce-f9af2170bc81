<?php

namespace App\Livewire\Dashboard\Users\MoneyBack;

use App\Models\TransactionResultRefund;
use LivewireUI\Modal\ModalComponent;

class TransactionResult extends ModalComponent
{
    public $transactionId;

    public $transactions = [];

    public function resetState() {}

    public function mount()
    {
        $this->transactions = TransactionResultRefund::where('transaction_id', $this->transactionId)->latest()->get();
    }

    public function render()
    {

        return view('livewire.dashboard.users.money-back.transaction-result');
    }
}
