<?php

namespace App\Livewire\Dashboard\Articles\Categories;

use App\Models\Category;
use <PERSON><PERSON>nerezo\LivewireAlert\LivewireAlert;
use LivewireUI\Modal\ModalComponent;

class CategoriesIndex extends ModalComponent
{
    use LivewireAlert;

    public array $data = [
        'title_fa' => null,
        'title_eng' => null,
        'slug' => null,
        'path' => null,
        'active' => false,
    ];

    public function rules()
    {
        return [
            'data.title_fa' => 'required',
            'data.title_eng' => 'required',
            'data.slug' => 'required',
            // 'data.path' => 'required',
            'data.active' => 'required',
        ];
    }

    public function mount()
    {
        $this->data = [
            'title_fa' => null,
            'title_eng' => null,
            'slug' => null,
            'path' => null,
            'active' => false,
        ];
    }

    public function generateSlug()
    {
        $this->data['slug'] = makeSlug($this->data['title_fa']);
    }

    public function removeItem($itemId)
    {

        Category::whereId($itemId)->delete();

        $this->alert('success', 'آیتم حذف شد', [
            'position' => 'top-start',
        ]);
    }

    public function save()
    {

        // dd((bool) $this->data['active']);
        $this->validate();

        Category::create([
            'source' => 'khodrox',
            'title' => $this->data['title_fa'],
            'title_eng' => $this->data['title_eng'],
            'slug' => $this->data['slug'],
            'path' => $this->data['path'],
            'active' => (bool) $this->data['active'],
        ]);

        $this->data = [
            'title_fa' => null,
            'title_eng' => null,
            'slug' => null,
            'path' => null,
            'active' => false,
        ];

        $this->alert('success', 'آیتم جدید اضافه شد', [
            'position' => 'top-start',
        ]);

    }

    public function resetState() {}

    public function render()
    {
        return view('livewire.dashboard.articles.categories.categories-index', [
            'categories' => Category::where('source', 'khodrox')->latest()->paginate(15),
        ]);
    }
}
