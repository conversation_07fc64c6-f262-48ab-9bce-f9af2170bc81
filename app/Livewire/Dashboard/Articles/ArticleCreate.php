<?php

namespace App\Livewire\Dashboard\Articles;

use App\Models\Article;
use App\Models\Category;
use Illuminate\Support\Facades\Storage;
use <PERSON><PERSON>nerezo\LivewireAlert\LivewireAlert;
use Livewire\Component;
use Livewire\WithFileUploads;

class ArticleCreate extends Component
{
    use LivewireAlert, WithFileUploads;

    public $data = [
        'title' => null,
        'slug' => null,
        'category_id' => null,
        'description' => null,
        'author' => null,
        'meta_title' => null,
        'meta_description' => null,
        'meta_tags' => null,
        'view_count' => 0,
        'page' => null,
        'status' => null,
        'tags' => [],
        'chain' => null,
        'schema' => null,
        'type' => null,
        'datePublished' => null,
        'meta_search' => null,
        'cover' => null,
        'canonical' => null,
        'meta_search_toggle' => false,

        'article_id' => null,
        'archive' => null,

        'sidebar' => false,
        'ads_toggle' => false,
        'ads_title' => null,
        'imageAds' => null,
        'ads_link' => null,
    ];

    public $question = '';

    public $answer = '';

    public $faqs = [];

    public $value;

    public $quillId;

    public $image;

    public $imageAds;

    public function mount($value = '')
    {
        $this->value = $value;
    }

    public function updatedDataTitle()
    {
        // if ($this->data['slug'] == null) {
        $this->data['slug'] = makeSlug($this->data['title']);
        // }
    }

    public function generateSlug()
    {
        $this->data['slug'] = makeSlug($this->data['title']);
        $this->generateCanonical();
    }

    public function removeCoverImage()
    {
        if (isset($this->articleId) && $this->articleId != null) {
            $article = Article::findOrFail($this->articleId);
            foreach ($article->galleries as $item) {
                // Storage::disk('public')->delete($item->image);
                $item->delete();
            }
        }

        $this->image = null;
        $this->data['cover'] = null;
    }

    public function removeCoverAdsImage()
    {
        if (isset($this->articleId) && $this->articleId != null) {
            $article = Article::findOrFail($this->articleId);

            foreach ($article->galleries as $item) {
                // Storage::disk('public')->delete($item->imageAds);
                $item->delete();
            }
        }

        $this->imageAds = null;
        $this->data['imageAds'] = null;
    }

    public function addTag()
    {
        if ($this->data['chain']) {
            $tags = array_filter(array_map('trim', explode(',', $this->data['chain'])));
            $this->data['tags'] = array_unique(array_merge($this->data['tags'], $tags));
            $this->data['chain'] = null; // clear input
        }
    }

    public function removeTag($index)
    {
        unset($this->data['tags'][$index]);
        $this->data['tags'] = array_values($this->data['tags']); // reindex
    }

    public function addItem()
    {
        $this->validate([
            'question' => 'required|string',
            'answer' => 'required|string',
        ]);

        $this->faqs[] = [
            'question' => $this->question,
            'answer' => $this->answer,
        ];

        $this->reset(['question', 'answer']);
    }

    public function removeItem($index)
    {
        unset($this->faqs[$index]);
        $this->faqs = array_values($this->faqs);
    }

    public function rules()
    {
        return [
            'data.title' => 'required',
            'data.category_id' => 'required',
            'data.meta_title' => 'required',
            'data.meta_description' => 'required',
            'data.page' => 'required',
            'data.status' => 'required',
            'value' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'data.title.required' => 'عنوان مقاله را بنویسید',
            'data.category_id.required' => 'گروه را مشخص کنید',
            'data.meta_title.required' => 'متا تایتل را بنویسید',
            'data.meta_description.required' => 'متا دیسکریبشن را بنویسید',
            'data.page.required' => 'صفحه مقاله را مشخص کنید',
            'value.required' => 'مقاله مورد نظر را بنویسید',
            'data.status.required' => 'وضعیت مقاله الزامیست',
        ];
    }

    public function generateCanonical()
    {
        $this->data['canonical'] = 'https://khodrox.com/blog/' . $this->data['slug'];
    }

    public function save()
    {
        $copyArticle = Article::where('title', $this->data['title'])->where('archive', true)->first();
        if ($copyArticle) {

            $this->alert('error', 'خطا در اعتبارسنجی', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => 'عنوان مقاله "' . $this->data['title'] . '" تکراری می باشد لطفا بررسی کنید',
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);

            return;
        }

        // dd($this->data);

        // $this->validate();

        // dd($this->data);
        $meta_search_toggle = $this->data['meta_search_toggle'] ? 'noindex, nofollow' : null;

        if (empty($this->value)) {

            $this->alert('error', 'خطا در اعتبارسنجی', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => 'مقاله خود را بنویسید و سپس مجددا درخواست خود را ثبت کنید',
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);

            return;
        }

        $this->data['description'] = $this->value;

        if ($this->imageAds) {
            $this->data['imageAds'] = saveFileFromTemporaryImage($this->imageAds);
        }

        // dd($this->data);
        $article = Article::create([

            'user_id' => auth()->id(),
            'type' => $this->data['page'] == null ? 'article' : 'services',
            'title' => $this->data['title'],
            'slug' => $this->data['slug'],
            'category_id' => $this->data['category_id'],
            'description' => $this->data['description'],
            'meta_title' => $this->data['meta_title'],
            'meta_description' => $this->data['meta_description'],
            'page' => $this->data['page'],
            'tags' => $this->data['tags'],
            'view_count' => $this->data['view_count'],
            'status' => $this->data['status'],
            'schema' => $this->data['schema'],
            'faqs' => $this->faqs,
            'datePublished' => $this->data['datePublished'],
            'meta_search' => $meta_search_toggle,
            'canonical' => $this->data['canonical'],

            'active' => true,

            'sidebar' => $this->data['sidebar'] ?? false,
            'ads_toggle' => $this->data['ads_toggle'] ?? false,
            'ads' => [
                'cover' => $this->data['imageAds'],
                'title' => $this->data['ads_title'],
                'description' => null,
                'link' => $this->data['ads_link'],
            ],
        ]);

        if ($this->image) {

            $path = saveFileFromTemporaryImage($this->image);
            // $this->image->store('articles', 'public');
            // dd($path);
            $article = Article::find($article->id);
            $article->galleries()->create([
                'image' => $path,
            ]);

        }

        $this->alert('success', 'ثبت موفق', [
            'position' => 'center',
            'timer' => 5000,
            'toast' => false,
            'text' => 'مقاله جدید با موفقیت ثبت شد',
            'timerProgressBar' => true,
            'showDenyButton' => true,
            'denyButtonText' => 'بسیار خب متوجه شدم',
        ]);

        // return redirect()->route('article-manager');
        return redirect()->route('article-show', $article->id);

        // dd($this->data);
    }

    // public function updatedValue()
    // {
    //     // dd($this->value);
    // }

    public function render()
    {
        return view('livewire.dashboard.articles.article-create', [
            'categories' => Category::where('source', 'khodrox')->get(),
        ]);
    }
}
