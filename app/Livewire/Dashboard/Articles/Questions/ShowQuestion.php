<?php

namespace App\Livewire\Dashboard\Articles\Questions;

use App\Models\Article;
use Livewire\Component;
use LivewireUI\Modal\ModalComponent;
use Jan<PERSON>nerezo\LivewireAlert\LivewireAlert;
class ShowQuestion extends ModalComponent
{
    use LivewireAlert;
    public $question;
    public $answer;

    public function rules()
    {
        return [
            'question' => 'required',
            'answer' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'question.required' => 'عنوان سوال نمی تواند خالی باشد',
            'answer.required' => 'جواب به سوال را وارد کنید',
        ];
    }

    public $articleId;
    public $index;

    public function mount($articleId, $index)
    {
        $this->articleId = $articleId;
        $this->index = $index;

        // مقاله رو بگیر
        $article = Article::findOrFail($this->articleId);

        // مقدار سوال و جواب رو از آرایه بگیر
        $this->question = $article->faqs[$this->index]['question'] ?? '';
        $this->answer = $article->faqs[$this->index]['answer'] ?? '';
    }

    public function update()
    {
        $this->validate();

        $article = Article::findOrFail($this->articleId);

        // ویرایش آیتم
        $faqs = $article->faqs;
        $faqs[$this->index]['question'] = $this->question;
        $faqs[$this->index]['answer'] = $this->answer;

        // ذخیره
        $article->faqs = $faqs;
        $article->save();

        $this->alert('success', 'اطلاعات با موفقیت بروزرسانی شد', [
            'position' => 'top-start',
        ]);

        $this->dispatch('reload-faqs');


        $this->closeModal();

    }

    public function resetState()
    {
    }

    public function render()
    {
        return view('livewire.dashboard.articles.questions.show-question');
    }
}
