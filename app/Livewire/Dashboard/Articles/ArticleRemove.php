<?php

namespace App\Livewire\Dashboard\Articles;

use App\Models\Article;
use <PERSON><PERSON><PERSON>ezo\LivewireAlert\LivewireAlert;
use LivewireUI\Modal\ModalComponent;

class ArticleRemove extends ModalComponent
{
    use LivewireAlert;

    public $articleId;

    public $softDelete;

    public function mount($articleId, $softDelete = false)
    {
        $this->articleId = $articleId;
        $this->softDelete = $softDelete;
    }

    public function removeItem()
    {
        try {

            if ($this->softDelete) {
                Article::withTrashed()->find($this->articleId)->forceDelete();
            } else {
                Article::whereId($this->articleId)->delete();
            }

            $this->alert('success', 'آیتم مورد نظر با موفقیت حذف شد', [
                'position' => 'top-start',
            ]);

            $this->dispatch('reload-articles');

            $this->closeModal();

        } catch (\Exception $e) {

            $this->alert('error', 'خطا در حذف مقاله', [
                'position' => 'center',
                'timer' => 10000,
                'toast' => false,
                'text' => $e->getMessage(),
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);

        }
    }

    public function restoreItem()
    {
        try {

            Article::withTrashed()->find($this->articleId)->restore();

            $this->alert('success', 'آیتم مورد نظر بازیابی شد', [
                'position' => 'top-start',
            ]);

            $this->dispatch('reload-articles');

            $this->closeModal();

        } catch (\Exception $e) {

            $this->alert('error', 'خطا در حذف مقاله', [
                'position' => 'center',
                'timer' => 10000,
                'toast' => false,
                'text' => $e->getMessage(),
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);

        }
    }

    public function render()
    {
        return view('livewire.dashboard.articles.article-remove');
    }
}
