<?php

namespace App\Livewire\Dashboard\Articles\Box;

use Illuminate\Support\Facades\DB;
use Livewire\Component;

class BoxArticle extends Component
{
    public string $data;

    public string $title;

    public $icon;

    public $class;

    public function mount()
    {
        // $article_counts = cache()->remember('article_total_counts', 15, function () {
        //     return DB::connection('mongodb')
        //         ->table('articles')
        //         ->count();
        // });

        // $this->data = formatMoney($article_counts);
    }

    public function render()
    {
        return view('livewire.dashboard.articles.box.box-article');
    }
}
