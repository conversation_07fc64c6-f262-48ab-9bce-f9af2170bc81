<?php

namespace App\Livewire\Dashboard\Categories;

use App\Models\Category;
use <PERSON><PERSON>nerezo\LivewireAlert\LivewireAlert;
use LivewireUI\Modal\ModalComponent;

class CreateCategoryForm extends ModalComponent
{
    use LivewireAlert;

    public array $data = [
        'title_fa' => null,
        'title_eng' => null,
        'slug' => null,
        'title_article' => null,
        'description' => null,
        'status' => true,

        'meta_search' => null,
        'meta_title' => null,
        'canonical' => null,
        'meta_description' => null,
    ];

    public function generateSlug()
    {
        $this->data['slug'] = makeSlug($this->data['title_fa']);
    }

    public function resetState() {}

    public function rules()
    {
        return [
            'data.title_fa' => 'required',
            'data.title_eng' => 'required',
            'data.slug' => 'required',
            // 'data.title_article' => 'required',
            // 'data.description' => 'required',
            'data.status' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'data.title_fa.required' => 'عنوان فارسی الزامیست',
            'data.title_eng.required' => 'عنوان انگلیسی الزامیست',
            'data.slug.required' => 'اسلاگ گروه بندی مشخص کنید',
            // 'data.title_article.required' => '',
            // 'data.description.required' => '',
            'data.status.required' => 'وضعیت را مشخص کنید',
        ];
    }

    public function store()
    {
        $this->validate();

        $meta_search = $this->data['meta_search'] ? 'noindex, nofollow' : null;

        Category::create([
            'source' => 'khodrox',
            'title' => $this->data['title_fa'],
            'title_eng' => $this->data['title_eng'],
            'slug' => $this->data['slug'],
            'active' => $this->data['status'] == 'true' ? true : false,
            'title_article' => $this->data['title_article'],
            'description' => $this->data['description'],

            'meta_search' => $meta_search,
            'meta_title' => $this->data['meta_title'],
            'canonical' => $this->data['canonical'],
            'meta_description' => $this->data['meta_description'],
        ]);

        $this->dispatch('reload-categories-list');

        $this->alert('success', 'ثبت موفق', [
            'position' => 'center',
            'timer' => 5000,
            'toast' => false,
            'text' => 'آیتم جدید با موفقیت افزوده شد',
            'timerProgressBar' => true,
            'showDenyButton' => true,
            'denyButtonText' => 'بسیار خب متوجه شدم',
        ]);

        $this->closeModal();

    }

    public function generateCanonical()
    {
        $this->data['canonical'] = 'https://khodrox.com/blog/'.$this->data['slug'];
    }

    public function render()
    {
        return view('livewire.dashboard.categories.create-category-form');
    }
}
