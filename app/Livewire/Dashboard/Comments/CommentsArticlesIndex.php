<?php

namespace App\Livewire\Dashboard\Comments;

use App\Models\Comment;
use App\Enums\StatusEnum;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\On;

class CommentsArticlesIndex extends Component
{
    use WithPagination;

    protected $listeners = ['comment-updated' => 'refreshComments'];

    public $search = '';
    public $statusFilter = '';
    public $selectedComments = [];
    public $selectAll = false;

    public function refreshComments()
    {
        // این متد باعث refresh شدن کامپوننت می‌شود
        $this->resetPage();

        // نمایش پیام موفقیت
        $this->dispatch('$refresh');
    }


    public function placeholder()
    {
        return <<<'HTML'
        <div
                    class="flex gap-3 rounded-md bg-white p-5 h-96 min-h-96 "

                >
                    <svg
                        class="inline h-6 w-6 animate-spin text-red-700"
                        role="status"
                        aria-hidden="true"
                        viewBox="0 0 100 101"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="#E5E7EB"
                        />
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentColor"
                        />
                    </svg>
                    <span class="whitespace-nowrap text-base font-bold"> درحال دریافت اطلاعات از سرور ...</span>
                </div>

        HTML;
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingStatusFilter()
    {
        $this->resetPage();
    }

    // متد openViewModal حذف شد - حالا از wire-elements/modal استفاده می‌کنیم

    // متد closeViewModal حذف شد

    // متد openConfirmModal حذف شد

    // متدهای مودال قدیمی حذف شدند

    public function changeCommentStatus($commentId, $status)
    {
        try {
            $comment = Comment::find($commentId);
            if ($comment) {
                $statusEnum = StatusEnum::from((int) $status);
                $comment->update(['status' => $statusEnum]);
                $this->dispatch('comment-updated');

                $statusText = match ($statusEnum) {
                    StatusEnum::PENDING => 'در انتظار تایید',
                    StatusEnum::CONFIRMED => 'تایید شده',
                    StatusEnum::REJECTED => 'رد شده',
                };

                session()->flash('message', "وضعیت کامنت به '{$statusText}' تغییر یافت.");
            } else {
                session()->flash('error', 'کامنت یافت نشد.');
            }
        } catch (\Exception $e) {
            session()->flash('error', 'خطا در تغییر وضعیت: ' . $e->getMessage());
        }
    }









    // متد refreshComments تکراری حذف شد

    public function toggleSelectAll()
    {
        if ($this->selectAll) {
            $this->selectedComments = Comment::whereIn('commentable_type', ['App\Models\Content\Article', 'App\Models\Article'])
                ->whereNull('parent_id')
                ->pluck('id')
                ->toArray();
        } else {
            $this->selectedComments = [];
        }
    }

    public function bulkApprove()
    {
        if (empty($this->selectedComments)) {
            session()->flash('error', 'هیچ کامنتی انتخاب نشده است.');
            return;
        }

        Comment::whereIn('id', $this->selectedComments)
            ->update(['status' => StatusEnum::CONFIRMED]);

        $this->selectedComments = [];
        $this->selectAll = false;
        $this->dispatch('comment-updated');
        session()->flash('message', count($this->selectedComments) . ' کامنت تایید شد.');
    }

    public function bulkReject()
    {
        if (empty($this->selectedComments)) {
            session()->flash('error', 'هیچ کامنتی انتخاب نشده است.');
            return;
        }

        Comment::whereIn('id', $this->selectedComments)
            ->update(['status' => StatusEnum::REJECTED]);

        $this->selectedComments = [];
        $this->selectAll = false;
        $this->dispatch('comment-updated');
        session()->flash('message', count($this->selectedComments) . ' کامنت رد شد.');
    }

    public function bulkDelete()
    {
        if (empty($this->selectedComments)) {
            session()->flash('error', 'هیچ کامنتی انتخاب نشده است.');
            return;
        }

        $count = count($this->selectedComments);
        Comment::whereIn('id', $this->selectedComments)->delete();

        $this->selectedComments = [];
        $this->selectAll = false;
        $this->dispatch('comment-updated');
        session()->flash('message', $count . ' کامنت حذف شد.');
    }

    public function exportComments()
    {
        $comments = Comment::with(['user', 'commentable'])->
            whereIn('commentable_type', ['App\Models\Content\Article', 'App\Models\Article'])
            ->whereNull('parent_id')
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('body', 'like', '%' . $this->search . '%')
                        ->orWhereHas('user', function ($userQuery) {
                            $userQuery->where('fullname', 'like', '%' . $this->search . '%')
                                ->orWhere('phone', 'like', '%' . $this->search . '%');
                        });
                });
            })
            ->when($this->statusFilter, function ($query) {
                $query->where('status', (int) $this->statusFilter);
            })
            ->latest()
            ->get();

        $csvData = "نام کاربر,شماره تلفن,عنوان مقاله,متن نظر,امتیاز,وضعیت,تاریخ\n";

        foreach ($comments as $comment) {
            $status = match ($comment->status->value) {
                0 => 'رد شده',
                1 => 'در انتظار تایید',
                2 => 'تایید شده',
                default => 'نامشخص'
            };

            $csvData .= sprintf(
                '"%s","%s","%s","%s","%s","%s","%s"' . "\n",
                $comment->user->fullname ?? 'کاربر ناشناس',
                $comment->user->phone ?? '-',
                $comment->commentable->title ?? 'مقاله حذف شده',
                str_replace('"', '""', $comment->body),
                $comment->rate ?? '-',
                $status,
                $comment->shamsi_date
            );
        }

        return response()->streamDownload(function () use ($csvData) {
            echo "\xEF\xBB\xBF" . $csvData; // UTF-8 BOM for Excel
        }, 'comments-export-' . date('Y-m-d-H-i-s') . '.csv', [
            'Content-Type' => 'text/csv; charset=UTF-8',
        ]);
    }

    public function render()
    {
        $comments = Comment::with(['user', 'commentable', 'replies.user'])
            ->whereIn('commentable_type', ['App\Models\Content\Article', 'App\Models\Article'])
            ->whereNull('parent_id')
            ->latest()
            ->paginate(15);



        return view('livewire.dashboard.comments.comments-articles-index', [
            'comments' => $comments,
            'statusOptions' => [
                '' => 'همه وضعیت‌ها',
                StatusEnum::PENDING->value => 'در انتظار تایید',
                StatusEnum::CONFIRMED->value => 'تایید شده',
                StatusEnum::REJECTED->value => 'رد شده'
            ]
        ]);
    }
}
