<?php

namespace App\Livewire\Dashboard\Comments;

use App\Models\Comment;
use App\Enums\StatusEnum;
use Livewire\Component;
use LivewireUI\Modal\ModalComponent;

class ConfirmActionModal extends ModalComponent
{
    public $commentId;
    public $action;
    public $title;
    public $message;
    public $confirmText;
    public $cancelText = 'انصراف';

    public function mount($commentId, $action, $title = null, $message = null, $confirmText = null)
    {
        $this->commentId = $commentId;
        $this->action = $action;
        
        // تنظیم پیام‌ها بر اساس نوع عملیات
        switch($action) {
            case 'approve':
                $this->title = $title ?? 'تایید کامنت';
                $this->message = $message ?? 'آیا از تایید این کامنت اطمینان دارید؟';
                $this->confirmText = $confirmText ?? 'تایید';
                break;
            case 'reject':
                $this->title = $title ?? 'رد کامنت';
                $this->message = $message ?? 'آیا از رد این کامنت اطمینان دارید؟';
                $this->confirmText = $confirmText ?? 'رد';
                break;
            case 'delete':
                $this->title = $title ?? 'حذف کامنت';
                $this->message = $message ?? 'آیا از حذف این کامنت اطمینان دارید؟ این عمل قابل بازگشت نیست.';
                $this->confirmText = $confirmText ?? 'حذف';
                break;
            default:
                $this->title = $title ?? 'تایید عملیات';
                $this->message = $message ?? 'آیا از انجام این عملیات اطمینان دارید؟';
                $this->confirmText = $confirmText ?? 'تایید';
        }
    }

    public function confirm()
    {
        $comment = Comment::find($this->commentId);
        
        if (!$comment) {
            session()->flash('error', 'کامنت یافت نشد.');
            $this->closeModal();
            return;
        }

        switch($this->action) {
            case 'approve':
                $comment->update(['status' => StatusEnum::CONFIRMED]);
                session()->flash('message', 'کامنت تایید شد.');
                break;
            case 'reject':
                $comment->update(['status' => StatusEnum::REJECTED]);
                session()->flash('message', 'کامنت رد شد.');
                break;
            case 'delete':
                $comment->delete();
                session()->flash('message', 'کامنت حذف شد.');
                break;
        }

        $this->dispatch('comment-updated');
        $this->closeModal();
    }

    public static function modalMaxWidth(): string
    {
        return 'md';
    }

    public function render()
    {
        return view('livewire.dashboard.comments.confirm-action-modal');
    }
}
