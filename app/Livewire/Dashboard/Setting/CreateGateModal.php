<?php

namespace App\Livewire\Dashboard\Setting;

use App\Models\Gate;
use <PERSON><PERSON><PERSON>ezo\LivewireAlert\LivewireAlert;
use LivewireUI\Modal\ModalComponent;

class CreateGateModal extends ModalComponent
{
    use LivewireAlert;

    public array $data = [
        'title' => null,
        'terminalId' => null,
        'merchentId' => null,
        'site' => null,
    ];

    public function resetState() {}

    public function rules()
    {
        return [
            'data.title' => 'required',
            'data.terminalId' => 'required',
            'data.merchentId' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'data.title.required' => 'عنوان درگاه را وارد کنید',
            'data.terminalId.required' => 'شناسه ترمینال را وارد کنید',
            'data.merchentId.required' => 'مرچنت آیدی را وارد کنید',
        ];
    }

    public function save()
    {
        $this->validate();

        Gate::create([
            'title' => $this->data['title'],
            'terminalId' => $this->data['terminalId'],
            'merchentId' => $this->data['merchentId'],
            'site' => $this->data['site'],
        ]);

        $this->dispatch('reload-gate');

        $this->alert('success', 'ثبت اطلاعات با موفقیت انجام شد', [
            'position' => 'top-start',
        ]);

        $this->closeModal();

    }

    public function render()
    {
        return view('livewire.dashboard.setting.create-gate-modal');
    }
}
