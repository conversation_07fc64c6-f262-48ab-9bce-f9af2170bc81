<?php

namespace App\Livewire\Dashboard\Setting;

use App\Models\Gate;
use <PERSON><PERSON><PERSON>ezo\LivewireAlert\LivewireAlert;
use LivewireUI\Modal\ModalComponent;

class EditGateModal extends ModalComponent
{
    use LivewireAlert;

    public array $data = [
        'title' => null,
        'terminalId' => null,
        'merchentId' => null,
        'site' => null,
    ];

    public $gateId;

    public function resetState() {}

    public function rules()
    {
        return [
            'data.title' => 'required',
            'data.terminalId' => 'required',
            'data.merchentId' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'data.title.required' => 'عنوان درگاه را وارد کنید',
            'data.terminalId.required' => 'شناسه ترمینال را وارد کنید',
            'data.merchentId.required' => 'مرچنت آیدی را وارد کنید',
        ];
    }

    public function mount()
    {

        $gate = Gate::whereId($this->gateId)->first();
        if (isset($gate) && $gate != null) {
            $this->data = [
                'title' => $gate->title,
                'terminalId' => $gate->terminalId,
                'merchentId' => $gate->merchentId,
                'site' => $gate?->site,
            ];
        }
    }

    public function update()
    {
        $this->validate();

        Gate::whereId($this->gateId)->update([
            'title' => $this->data['title'],
            'terminalId' => $this->data['terminalId'],
            'merchentId' => $this->data['merchentId'],
            'site' => $this->data['site'],
        ]);

        $this->dispatch('reload-gate');

        $this->alert('success', 'اطلاعات با موفقیت بروزرسانی شد', [
            'position' => 'top-start',
        ]);

        $this->closeModal();

    }

    public function render()
    {
        return view('livewire.dashboard.setting.edit-gate-modal');
    }
}
