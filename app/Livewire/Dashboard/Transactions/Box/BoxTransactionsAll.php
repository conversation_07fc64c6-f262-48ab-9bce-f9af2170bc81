<?php

namespace App\Livewire\Dashboard\Transactions\Box;

use Illuminate\Support\Facades\DB;
use Livewire\Attributes\On;
use Livewire\Component;

class BoxTransactionsAll extends Component
{
    public string $data;

    public string $title;

    public $icon;

    public $class;

    public string $source = 'all';

    public function mount()
    {
        $transactionPendingToDay = cache()->remember('transactions_pending', 15, function () {
            return DB::connection('mongodb')
                ->table('paymentpendings')
                ->whereDate('createdAt', now()->toDateString())
                ->sum('amount');
        });

        $transactionSuccessToDay = cache()->remember('transactions_today_success', 15, function () {
            return DB::connection('mongodb')
                ->table('transactions')
                ->whereDate('creationTime', now()->toDateString())
                ->sum('amount');
        });

        $this->data = formatMoney($transactionSuccessToDay + $transactionPendingToDay);
    }

    #[On('data-transactions-all')]
    public function updateData($data)
    {
        $this->data = $data;
    }

    #[On('filterService')]
    public function filterService($service)
    {
        if ($service == 'all') {
            return $this->mount();
        }

        $pending = DB::connection('mongodb')
            ->table('paymentpendings')
            ->whereDate('createdAt', now()->toDateString())
            ->where('source', $service)
            ->sum('amount');

        $success = DB::connection('mongodb')
            ->table('transactions')
            ->whereDate('creationTime', now()->toDateString())
            ->where('source', $service)
            ->sum('amount');

        $this->data = formatMoney($pending + $success);
    }

    public function render()
    {
        return view('livewire.dashboard.transactions.box.box-transactions-all');
    }
}
