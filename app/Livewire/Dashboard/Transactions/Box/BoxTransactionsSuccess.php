<?php

namespace App\Livewire\Dashboard\Transactions\Box;

use Illuminate\Support\Facades\DB;
use Livewire\Attributes\On;
use Livewire\Component;

class BoxTransactionsSuc<PERSON> extends Component
{
    public string $data;

    public string $title;

    public $icon;

    public $class;

    public string $source = 'all';

    public function mount()
    {

        $transactionSuccessToDay = cache()->remember('transactions_today_success', 15, function () {
            return DB::connection('mongodb')
                ->table('transactions')
                ->whereDate('creationTime', now()->toDateString())
                ->sum('amount');
        });

        $this->data = formatMoney($transactionSuccessToDay);
    }

    #[On('data-transactions-success')]
    public function updateData($data)
    {
        $this->data = $data;
    }

    #[On('filterService')]
    public function filterService($service)
    {
        if ($service == 'all') {
            return $this->mount();
        }

        $success = DB::connection('mongodb')
            ->table('transactions')
            ->whereDate('creationTime', now()->toDateString())
            ->where('source', $service)
            ->sum('amount');

        $this->data = formatMoney($success);
    }

    public function render()
    {
        return view('livewire.dashboard.transactions.box.box-transactions-success');
    }
}
