<?php

namespace App\Livewire\Dashboard\Transactions;

use App\Models\Gate;
use Illuminate\Support\Facades\Http;
use Livewire\Attributes\Url;
use Livewire\Component;

class TransactionsZarinpal extends Component
{
    #[Url]
    public $terminalId = '545784';

    #[Url]
    public $filter = 'ACTIVE';

    public $gates = [];

    public function mount()
    {
        $this->gates = Gate::all();
    }

    public function setGate($terminalId)
    {

        $this->terminalId = $terminalId;
    }

    public function placeholder()
    {
        return <<<'HTML'
        <div
                    class="flex gap-3 rounded-md bg-white p-5 h-96 min-h-96 "

                >
                    <svg
                        class="inline h-6 w-6 animate-spin text-red-700"
                        role="status"
                        aria-hidden="true"
                        viewBox="0 0 100 101"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="#E5E7EB"
                        />
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentColor"
                        />
                    </svg>
                    <span class="whitespace-nowrap text-base font-bold"> درحال دریافت اطلاعات از سرور ...</span>
                </div>

        HTML;
    }

    private function sendRequest($token)
    {

        return Http::withHeaders([
            'Authorization' => 'Bearer '.$token,
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ])->post('https://next.zarinpal.com/api/v4/graphql', [
            'query' => '
        query GetSession($terminalId: ID!, $filter: FilterEnum!) {
            Session(terminal_id: $terminalId, filter: $filter) {
                session_tries {
                    id
                    session_id
                    payment_id
                    payer_ip
                    init_time
                    verify_time
                    status
                    rrn
                    card_pan
                    created_at

                }
                description
                amount
                fee
                payer_info {
                    name
                    mobile
                    email
                    order_id
                    card_holder_account_number
                    description
                    zarin_link_id
                    card_holder_name
                    card_holder_iban
                }
                refund {
                    id
                    session_id
                    instant_payout {
                    id
                    amount
                    fee
                    terminal {
                    id
                    __typename
                }
                bank_account {
                    id
                    iban
                    holder_name
                    issuing_bank {
                    name
                    slug
                    slug_image
                    __typename
                }
                __typename
                }
                    reference_id
                    reconciled_at
                    created_at
                    status
                    __typename
                }
                __typename
                }
            }
        }
    ',
            'variables' => [
                'terminalId' => $this->terminalId,
                'filter' => $this->filter,
            ],
        ]);
    }

    public function render()
    {
        $token = env('ZARINPAL_ACCESS_TOKEN_KHODROX');
        $response = $this->sendRequest($token);

        // بررسی محتویات پاسخ برای خطای Unauthorized
        $responseData = $response->json();

        // اگر خطای "Unauthorized" در داده‌ها وجود داشت، از توکن دوم استفاده کن
        if (isset($responseData['errors']) && ! empty($responseData['errors'])) {
            foreach ($responseData['errors'] as $error) {
                if (strpos($error['message'], 'Unauthorized') !== false) {
                    // توکن دوم را امتحان کن
                    $token = env('ZARINPAL_ACCESS_TOKEN');
                    $response = $this->sendRequest($token);
                    break;
                }
            }
        }

        // // نمایش پاسخ دریافتی برای عیب‌یابی
        // if ($response->failed()) {
        //     dd($response->json(), $response->status()); // نمایش وضعیت و پیام خطا
        // }

        // بررسی موفقیت‌آمیز بودن درخواست
        if ($response->successful()) {
            $transactions = $response->json();

            // dd($transactions);

            if (isset($transactions['data']['Session']) && ! empty($transactions['data']['Session'])) {
                return view('livewire.dashboard.transactions.transactions-zarinpal', [
                    'transactions' => $transactions['data']['Session'],
                ]);
            } else {
                return view('livewire.dashboard.transactions.transactions-zarinpal', [
                    'errorMessage' => 'هیچ تراکنشی یافت نشد.',
                ]);
            }
        } else {
            return view('livewire.dashboard.transactions.transactions-zarinpal', [
                'errorMessage' => 'خطا در دریافت داده‌ها.',
            ]);
        }
    }
}
