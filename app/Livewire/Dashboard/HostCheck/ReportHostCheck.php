<?php

namespace App\Livewire\Dashboard\HostCheck;

use App\Models\HostCheckLog;
use Carbon\Carbon;
use Livewire\Component;

class ReportHostCheck extends Component
{
    public $chartData;

    public function mount()
    {
        $from = Carbon::now()->subDays(9)->startOfDay();
        $to = Carbon::now()->endOfDay();

        $logs = HostCheckLog::whereBetween('created_at', [$from, $to])
            ->get()
            ->groupBy(fn ($log) => $log->host);

        $chartData = [];

        foreach ($logs as $host => $items) {
            $daily = [];

            $days = $items->groupBy(fn ($item) => Carbon::parse($item->created_at)->format('Y-m-d'));

            foreach ($days as $date => $checks) {
                $totalChecks = 0;
                $successful = 0;

                foreach ($checks as $check) {
                    // بررسی وجود result
                    if (! isset($check->result)) {
                        continue;
                    }

                    // decode کردن result
                    $result = is_array($check->result)
                        ? $check->result
                        : json_decode($check->result, true);

                    if (! is_array($result)) {
                        continue;
                    }

                    foreach ($result as $nodeResult) {
                        // بررسی ساختار داخلی
                        if (! isset($nodeResult[0]) || ! is_array($nodeResult[0])) {
                            continue;
                        }

                        $status = $nodeResult[0][3] ?? null;

                        if ($status) {
                            $totalChecks++;
                            if ($status == '200') {
                                $successful++;
                            }
                        }
                    }
                }

                $uptimePercent = $totalChecks > 0
                    ? round(($successful / $totalChecks) * 100, 2)
                    : 0;

                $daily[] = [
                    'date' => $date,
                    'uptime' => $uptimePercent,
                ];
            }

            $chartData[] = [
                'host' => $host,
                'data' => collect($daily)->sortBy('date')->values(),
            ];
        }

        $this->chartData = $chartData;
    }

    public function render()
    {
        return view('livewire.dashboard.host-check.report-host-check');
    }
}
