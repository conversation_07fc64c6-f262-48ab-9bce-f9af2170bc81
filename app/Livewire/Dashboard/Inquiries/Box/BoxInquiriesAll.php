<?php

namespace App\Livewire\Dashboard\Inquiries\Box;

use Illuminate\Support\Facades\DB;
use Livewire\Attributes\On;
use Livewire\Component;

class BoxInquiriesAll extends Component
{
    public string $data;

    public string $today;

    public string $title;

    public $icon;

    public $class;

    public string $source = 'all';

    public string $project = 'iranisoft';

    public function mount()
    {
        if ($this->project == 'iranisoft') {
            $total = cache()->remember('inquiry_resultsـall_counts', 15, function () {
                return DB::connection('mongodb')->table('inquiry_results')->count();
            });

            $today = cache()->remember('inquiry_resultsـtoDays_counts', 15, function () {
                return DB::connection('mongodb')->table('inquiry_results')->whereDate('created_at', today())->count();
            });

            $this->data = formatMoney($total);
            $this->today = formatMoney($today);
        } else {
            $total = cache()->remember('inquiry_shebacart_resultـall_counts', 15, function () {
                return DB::connection('mongodb_shebacart')->table('inquiry_app_results')->whereIn('inquiryType', ['khalafi-motor-b', 'khalafi-khodro-b'])->count();
            });

            $today = cache()->remember('inquiry_shebacart_resultsـtoـday_counts', 15, function () {
                return DB::connection('mongodb_shebacart')->table('inquiry_app_results')->whereIn('inquiryType', ['khalafi-motor-b', 'khalafi-khodro-b'])->whereDate('createdAt', today())->count();
            });

            $this->data = formatMoney($total);
            $this->today = formatMoney($today);
        }

    }

    #[On('data-inquiries-all')]
    public function updateData($data)
    {
        $this->data = $data;
    }

    #[On('filterService')]
    public function filterService($service)
    {
        if ($service == 'all') {
            return $this->mount();
        }

        if ($this->project == 'iranisoft') {
            $total = DB::connection('mongodb')->table('inquiry_results')->where('source', $service)->count();

            $today = DB::connection('mongodb')->table('inquiry_results')->where('source', $service)->whereDate('created_at', today())->count();

            $this->data = formatMoney($total);
        } else {

            $service = ShebacartFindService($service);

            $total = DB::connection('mongodb_shebacart')->table('inquiry_app_results')->where('source', $service)->count();

            $today = DB::connection('mongodb_shebacart')->table('inquiry_app_results')->where('source', $service)->whereDate('createdAt', today())->count();

            $this->data = formatMoney($total);
        }

        $this->today = formatMoney($today);

    }

    public function render()
    {
        return view('livewire.dashboard.inquiries.box.box-inquiries-all');
    }
}
