<?php

namespace App\Livewire\Dashboard\Inquiries\Box;

use Illuminate\Support\Facades\DB;
use Livewire\Attributes\On;
use Livewire\Component;

class BoxInquiriesTotal extends Component
{
    public string $data;

    public string $today;

    public string $title;

    public $icon;

    public $class;

    public string $source = 'all';

    public string $project = 'iranisoft';

    public function mount()
    {
        if ($this->project == 'iranisoft') {
            $total = cache()->remember('transactionss_khalafi', 300, function () {
                return DB::connection('mongodb')
                    ->table('inquiry_results')
                    ->whereDate('created_at', now()->toDateString())
                    ->get()
                    ->sum(function ($item) {
                        $parameters = (array) ($item->result['Parameters'] ?? []);

                        return (int) ($parameters['Amount'] ?? 0) + (int) ($parameters['TotalAmount'] ?? 0);
                    });
            });

            $this->data = formatMoney($total);
        } else {
            $total = cache()->remember('transactions_shebacart_khalafi', 300, function () {
                return DB::connection('mongodb_shebacart')
                    ->table('inquiry_app_results')
                    ->whereIn('inquiryType', ['khalafi-motor-b', 'khalafi-khodro-b'])
                    // ->whereDate('createdAt', now()->toDateString())
                    ->get()
                    ->sum(function ($item) {
                        $parameters = (array) ($item->inquiryResult['Parameters'] ?? []);

                        return (int) ($parameters['Amount'] ?? 0) + (int) ($parameters['TotalAmount'] ?? 0);
                    });
            });

            $this->data = formatMoney($total);

        }

    }

    #[On('data-inquiries-all')]
    public function updateData($data)
    {
        $this->data = $data;
    }

    #[On('filterService')]
    public function filterService($service)
    {
        if ($service == 'all') {
            return $this->mount();
        }

        if ($this->project == 'iranisoft') {
            $total = DB::connection('mongodb')->table('inquiry_results')
                ->whereDate('created_at', now()->toDateString())
                ->where('source', $service)
                ->get()
                ->sum(function ($item) {
                    $parameters = (array) ($item->result['Parameters'] ?? []);

                    return (int) ($parameters['Amount'] ?? 0) + (int) ($parameters['TotalAmount'] ?? 0);
                });

            $this->data = formatMoney($total);
        } else {

            $service = ShebacartFindService($service);

            $total = DB::connection('mongodb_shebacart')->table('inquiry_app_results')
                ->whereDate('createdAt', now()->toDateString())
                ->where('source', $service)
                ->get()
                ->sum(function ($item) {
                    $parameters = (array) ($item->inquiryResult['Parameters'] ?? []);

                    return (int) ($parameters['Amount'] ?? 0) + (int) ($parameters['TotalAmount'] ?? 0);
                });

            $this->data = formatMoney($total);

        }

    }

    public function render()
    {
        return view('livewire.dashboard.inquiries.box.box-inquiries-total');
    }
}
