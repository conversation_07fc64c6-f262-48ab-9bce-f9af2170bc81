<?php

namespace App\Livewire\Dashboard\Inquiries\Box;

use Illuminate\Support\Facades\DB;
use Livewire\Attributes\On;
use Livewire\Component;

class BoxInquiriesMotor extends Component
{
    public string $data;

    public string $today;

    public string $title;

    public $icon;

    public $class;

    public string $source = 'all';

    public string $project = 'iranisoft';

    public function mount()
    {
        if ($this->project == 'iranisoft') {
            $total = cache()->remember('inquiry_results_motor_counts', 15, function () {
                return DB::connection('mongodb')->table('inquiry_results')->where('type', 'motor')->count();
            });

            $today = cache()->remember('inquiry_results_toDay_motor_counts', 15, function () {
                return DB::connection('mongodb')->table('inquiry_results')->where('type', 'motor')->whereDate('created_at', today())->count();
            });

            $this->data = formatMoney($total);
            $this->today = formatMoney($today);
        } else {
            $total = cache()->remember('inquiry_results_shebacart_motor_counts', 15, function () {
                return DB::connection('mongodb_shebacart')->table('inquiry_app_results')->where('inquiryType', 'khalafi-motor-b')->count();
            });

            $today = cache()->remember('inquiry_results_shebacart_toDay_motor_counts', 15, function () {
                return DB::connection('mongodb_shebacart')->table('inquiry_app_results')->where('inquiryType', 'motkhalafi-motor-b')->whereDate('createdAt', today())->count();
            });

            $this->data = formatMoney($total);
            $this->today = formatMoney($today);
        }

    }

    #[On('data-inquiries-all')]
    public function updateData($data)
    {
        $this->data = $data;
    }

    #[On('filterService')]
    public function filterService($service)
    {
        if ($service == 'all') {
            return $this->mount();
        }

        if ($this->project == 'iranisoft') {

            $total = DB::connection('mongodb')->table('inquiry_results')->where('type', 'motor')->where('source', $service)->count();

            $today = DB::connection('mongodb')->table('inquiry_results')->where('type', 'motor')->where('source', $service)->whereDate('created_at', today())->count();

            $this->data = formatMoney($total);

            $this->today = formatMoney($today);
        } else {

            $service = ShebacartFindService($service);

            $total = DB::connection('mongodb_shebacart')->table('inquiry_app_results')->where('inquiryType', 'motkhalafi-motor-b')->where('source', $service)->count();

            $today = DB::connection('mongodb_shebacart')->table('inquiry_app_results')->where('inquiryType', 'motkhalafi-motor-b')->where('source', $service)->whereDate('createdAt', today())->count();

            $this->data = formatMoney($total);

            $this->today = formatMoney($today);
        }

    }

    public function render()
    {
        return view('livewire.dashboard.inquiries.box.box-inquiries-motor');
    }
}
