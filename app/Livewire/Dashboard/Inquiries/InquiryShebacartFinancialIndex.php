<?php

namespace App\Livewire\Dashboard\Inquiries;

use Illuminate\Support\Facades\DB;
use Livewire\Component;
use Livewire\WithPagination;

class InquiryShebacartFinancialIndex extends Component
{
    use WithPagination;

    public function placeholder()
    {
        return <<<'HTML'
        <div
                    class="flex gap-3 rounded-md bg-white p-5 h-96 min-h-96 "

                >
                    <svg
                        class="inline h-6 w-6 animate-spin text-red-700"
                        role="status"
                        aria-hidden="true"
                        viewBox="0 0 100 101"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="#E5E7EB"
                        />
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentColor"
                        />
                    </svg>
                    <span class="whitespace-nowrap text-base font-bold"> درحال دریافت اطلاعات از سرور ...</span>
                </div>

        HTML;
    }

    public function filterParam($service)
    {
        dd($service);
    }

    public function render()
    {
        $inquiries = DB::connection('mongodb_shebacart')->table('inquiry_app_results')
            ->whereIn('inquiryType', ['deposit-to-iban-a',
                'card-to-deposit-a',
                'card-to-iban-a',
                'iban-inquiry-a',
                'card-inquiry-a', ])
            ->orderBy('createdAt', 'desc');

        // if (! empty($this->service) && $this->service != 'all') {
        //     $service = ShebacartFindService($this->service);
        //     $inquiries->where('source', $service);
        // }

        // if (! empty($this->data['phone'])) {
        //     $inquiries->where('phone', 'like', '%'.$this->data['phone'].'%');

        // }

        // if (! empty($this->data['balance'])) {
        //     $amount = str_replace(',', '', $this->data['balance']);
        //     $inquiries->where('balance', '>=', $amount);
        // }

        // if (! empty($this->data['data_start'])) {
        //     // تبدیل تاریخ شروع شمسی به میلادی و سپس به Carbon
        //     $startDateTime = Verta::parse($this->data['data_start'])->toCarbon()->startOfDay(); // شروع روز

        //     // تبدیل تاریخ پایان شمسی به میلادی و سپس به Carbon
        //     $endDateTime = ! empty($this->data['date_end'])
        //         ? Verta::parse($this->data['date_end'])->toCarbon()->endOfDay() // پایان روز
        //         : Carbon::now()->endOfDay(); // پایان روز کنونی

        //     // اعمال فیلتر بر روی هر دو فیلد تاریخ
        //     $inquiries->where(function ($query) use ($startDateTime, $endDateTime) {
        //         $query->WhereBetween('createdAt', [$startDateTime, $endDateTime]);
        //     });
        // }

        // صفحه‌بندی نتایج
        $inquiries = $inquiries->paginate(40);

        return view('livewire.dashboard.inquiries.inquiry-shebacart-financial-index', [
            'inquiries' => $inquiries,
        ]);
    }
}
