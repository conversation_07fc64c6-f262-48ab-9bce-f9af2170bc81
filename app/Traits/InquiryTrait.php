<?php

namespace App\Traits;

use App\Http\Resources\InquiryResource;
use App\Models\InquiryResult;
use App\Models\ServiceHealthLog;
use App\Models\User;
use App\Notifications\Channels\BaleChannel;
use App\Notifications\QabzinoServiceDown;
use App\Services\Actions\InquiryCar\InquiryCarMouckResponseAction;
use App\Services\Actions\InquiryCar\InquiryCarResponseAction;
use App\Services\Actions\InquiryMotor\InquiryMotorMouckResponseAction;
use App\Services\Actions\InquiryMotor\InquiryMotorResponseAction;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Symfony\Component\HttpFoundation\Response;

trait InquiryTrait
{
    private function inquiry($request, array $data, string $type)
    {

        $userKhalafiyarTransfer = getTransferAppSetting();

        $AMOUNT_PAYMENT_REQUEST_CAR = (float) str_replace(',', '', env('AMOUNT_PAYMENT_REQUEST_CAR'));

        $USER_BALANCE = (float) str_replace(',', '', auth()->user()->balance);

        if ($userKhalafiyarTransfer) {
            $USER_BALANCE = $USER_BALANCE + getSourceBalance(auth()->user()->phone, 'khodroyar') ?? 0;
        }

        if ($USER_BALANCE < $AMOUNT_PAYMENT_REQUEST_CAR) {
            return $this->sendError('خلافی', ['message' => 'موجودی شما برای دریافت استعلام خلافی کافی نمی باشد'], Response::HTTP_PAYMENT_REQUIRED);
        }

        // $this->withdrawMoney($AMOUNT_PAYMENT_REQUEST_CAR, $USER_BALANCE);

        // if ($resultWallet) {
        $app = app('authenticated_application');

        $result = $this->createRequestToDatabase($app, $data, $request, $type);

        if (isset($result) && $result != null) {

            $inquiryResult = $result['inquiryResult'];

            $traceNumber = $result['traceNumber'];

            if (app()->environment('production')) {

                if ($type == 'car') {
                    if ($inquiryResult->detail_national_id != null) {
                        $result = InquiryCarResponseAction::handleCarWithDetailsHttpClient($inquiryResult, $traceNumber);

                        $inquiryFind = InquiryResult::where('user_id', auth()->id())
                            ->where('source', $app->source)
                            ->where('trace_number', $traceNumber)
                            ->latest()->first();

                        if ($result['Status']['Code'] == 'G00000' || $result['Status']['Code'] == 'GM5031') {

                            $AMOUNT_PAYMENT_REQUEST_CAR = (float) str_replace(',', '', env('AMOUNT_PAYMENT_REQUEST_CAR'));
                            $USER_BALANCE = (float) str_replace(',', '', auth()->user()->balance);
                            $this->withdrawMoney($AMOUNT_PAYMENT_REQUEST_CAR, $USER_BALANCE);
                            // $this->withdrawMoney2($AMOUNT_PAYMENT_REQUEST_CAR, $USER_BALANCE);

                            if ($inquiryFind) {

                                $USER_AFTER_BALANCE = $USER_BALANCE;
                                $userKhalafiyarTransfer = getTransferAppSetting();

                                if ($userKhalafiyarTransfer) {
                                    $USER_AFTER_BALANCE = $USER_AFTER_BALANCE + getSourceBalance($inquiryFind->phone, 'khodroyar') ?? 0;
                                }

                                // $USER_AFTER_BALANCE = $USER_AFTER_BALANCE - $AMOUNT_PAYMENT_REQUEST_CAR;

                                $inquiryFind->balance_after = $USER_AFTER_BALANCE;
                                $inquiryFind->status = 'success';
                                $inquiryFind->result = $result;
                                $inquiryFind->save();
                            }

                            return $this->sendResponse(
                                'نتیجه استعلام خلافی',
                                [
                                    'message' => 'درخواست با موفقیت انجام شد',
                                    'trace_number' => $inquiryResult->id,
                                    'showDialogInquiry' => false,
                                ],
                            );
                        } else {
                            $USER_AFTER_BALANCE = $USER_BALANCE;
                            $userKhalafiyarTransfer = getTransferAppSetting();

                            if ($userKhalafiyarTransfer) {
                                $USER_AFTER_BALANCE = $USER_AFTER_BALANCE + getSourceBalance($inquiryFind->phone, 'khodroyar') ?? 0;
                            }

                            $inquiryFind->balance_after = $USER_AFTER_BALANCE;
                            $inquiryFind->status = 'reject';
                            $inquiryFind->result = $result;
                            $inquiryFind->save();
                        }

                        $this->sendQabzinoServiceDown($result);

                        return $this->sendError('خلافی', ['message' => $result['Status']['Description']], Response::HTTP_SERVICE_UNAVAILABLE);

                    } else {
                        $result = InquiryCarResponseAction::HandleCarHttpClient($inquiryResult, $traceNumber);

                        $inquiryFind = InquiryResult::where('user_id', auth()->id())
                            ->where('source', $app->source)
                            ->where('trace_number', $traceNumber)
                            ->latest()->first();

                        if ($result['Status']['Code'] == 'G00000' || $result['Status']['Code'] == 'GM5031') {

                            $AMOUNT_PAYMENT_REQUEST_CAR = (float) str_replace(',', '', env('AMOUNT_PAYMENT_REQUEST_CAR'));
                            $USER_BALANCE = (float) str_replace(',', '', auth()->user()->balance);

                            $this->withdrawMoney($AMOUNT_PAYMENT_REQUEST_CAR, $USER_BALANCE);
                            // $this->withdrawMoney2($AMOUNT_PAYMENT_REQUEST_CAR, $USER_BALANCE);

                            if ($inquiryFind) {
                                $USER_AFTER_BALANCE = $USER_BALANCE;
                                $userKhalafiyarTransfer = getTransferAppSetting();

                                if ($userKhalafiyarTransfer) {
                                    $USER_AFTER_BALANCE = $USER_AFTER_BALANCE + getSourceBalance($inquiryFind->phone, 'khodroyar') ?? 0;
                                }

                                // $USER_AFTER_BALANCE = $USER_AFTER_BALANCE - $AMOUNT_PAYMENT_REQUEST_CAR;

                                $inquiryFind->balance_after = $USER_AFTER_BALANCE;
                                $inquiryFind->status = 'success';
                                $inquiryFind->result = $result;
                                $inquiryFind->save();
                            }

                            return $this->sendResponse(
                                'نتیجه استعلام خلافی',
                                [
                                    'message' => 'درخواست با موفقیت انجام شد',
                                    'trace_number' => $inquiryResult->id,
                                    'showDialogInquiry' => false,
                                ],
                            );
                        } else {

                            $USER_AFTER_BALANCE = $USER_BALANCE;
                            $userKhalafiyarTransfer = getTransferAppSetting();

                            if ($userKhalafiyarTransfer) {
                                $USER_AFTER_BALANCE = $USER_AFTER_BALANCE + getSourceBalance($inquiryFind->phone, 'khodroyar') ?? 0;
                            }

                            // $USER_AFTER_BALANCE = $USER_AFTER_BALANCE - $AMOUNT_PAYMENT_REQUEST_CAR;

                            $inquiryFind->balance_after = $USER_AFTER_BALANCE;
                            $inquiryFind->status = 'reject';
                            $inquiryFind->result = $result;
                            $inquiryFind->save();

                        }

                        $this->sendQabzinoServiceDown($result);

                        return $this->sendError('خلافی', ['message' => $result['Status']['Description']], Response::HTTP_SERVICE_UNAVAILABLE);
                    }
                }

                if ($type == 'motor') {
                    if ($inquiryResult->detail_national_id != null) {
                        $result = InquiryMotorResponseAction::handleMotorWithDetailsHttpClient($inquiryResult, $traceNumber);

                        $inquiryFind = InquiryResult::where('user_id', auth()->id())
                            ->where('source', $app->source)
                            ->where('trace_number', $traceNumber)
                            ->latest()->first();

                        if ($result['Status']['Code'] == 'G00000' || $result['Status']['Code'] == 'GM5031') {

                            $AMOUNT_PAYMENT_REQUEST_CAR = (float) str_replace(',', '', env('AMOUNT_PAYMENT_REQUEST_CAR'));
                            $USER_BALANCE = (float) str_replace(',', '', auth()->user()->balance);

                            $this->withdrawMoney($AMOUNT_PAYMENT_REQUEST_CAR, $USER_BALANCE);
                            // $this->withdrawMoney2($AMOUNT_PAYMENT_REQUEST_CAR, $USER_BALANCE);

                            if ($inquiryFind) {

                                $USER_AFTER_BALANCE = $USER_BALANCE;
                                $userKhalafiyarTransfer = getTransferAppSetting();

                                if ($userKhalafiyarTransfer) {
                                    $USER_AFTER_BALANCE = $USER_AFTER_BALANCE + getSourceBalance($inquiryFind->phone, 'khodroyar') ?? 0;
                                }

                                // $USER_AFTER_BALANCE = $USER_AFTER_BALANCE - $AMOUNT_PAYMENT_REQUEST_CAR;

                                $inquiryFind->balance_after = $USER_AFTER_BALANCE;
                                $inquiryFind->status = 'success';
                                $inquiryFind->result = $result;
                                $inquiryFind->save();
                            }

                            return $this->sendResponse(
                                'نتیجه استعلام خلافی',
                                [
                                    'message' => 'درخواست با موفقیت انجام شد',
                                    'trace_number' => $inquiryResult->id,
                                    'showDialogInquiry' => false,
                                ],
                            );
                        } else {
                            $USER_AFTER_BALANCE = $USER_BALANCE;
                            $userKhalafiyarTransfer = getTransferAppSetting();

                            if ($userKhalafiyarTransfer) {
                                $USER_AFTER_BALANCE = $USER_AFTER_BALANCE + getSourceBalance($inquiryFind->phone, 'khodroyar') ?? 0;
                            }

                            // $USER_AFTER_BALANCE = $USER_AFTER_BALANCE - $AMOUNT_PAYMENT_REQUEST_CAR;

                            $inquiryFind->balance_after = $USER_AFTER_BALANCE;
                            $inquiryFind->status = 'reject';
                            $inquiryFind->result = $result;
                            $inquiryFind->save();
                        }

                        $this->sendQabzinoServiceDown($result);

                        return $this->sendError('خلافی', ['message' => $result['Status']['Description']], Response::HTTP_SERVICE_UNAVAILABLE);
                    } else {
                        $result = InquiryMotorResponseAction::HandleMotorHttpClient($inquiryResult, $traceNumber);
                        $inquiryFind = InquiryResult::where('user_id', auth()->id())
                            ->where('source', $app->source)
                            ->where('trace_number', $traceNumber)
                            ->latest()->first();
                        // return $result;
                        if ($result['Status']['Code'] == 'G00000' || $result['Status']['Code'] == 'GM5031') {

                            $AMOUNT_PAYMENT_REQUEST_CAR = (float) str_replace(',', '', env('AMOUNT_PAYMENT_REQUEST_CAR'));
                            $USER_BALANCE = (float) str_replace(',', '', auth()->user()->balance);

                            $this->withdrawMoney($AMOUNT_PAYMENT_REQUEST_CAR, $USER_BALANCE);
                            // $this->withdrawMoney2($AMOUNT_PAYMENT_REQUEST_CAR, $USER_BALANCE);

                            if ($inquiryFind) {

                                $USER_AFTER_BALANCE = $USER_BALANCE;
                                $userKhalafiyarTransfer = getTransferAppSetting();

                                if ($userKhalafiyarTransfer) {
                                    $USER_AFTER_BALANCE = $USER_AFTER_BALANCE + getSourceBalance($inquiryFind->phone, 'khodroyar') ?? 0;
                                }

                                // $USER_AFTER_BALANCE = $USER_AFTER_BALANCE - $AMOUNT_PAYMENT_REQUEST_CAR;

                                $inquiryFind->balance_after = $USER_AFTER_BALANCE;

                                $inquiryFind->status = 'success';
                                $inquiryFind->result = $result;
                                $inquiryFind->save();
                            }

                            return $this->sendResponse(
                                'نتیجه استعلام خلافی',
                                [
                                    'message' => 'درخواست با موفقیت انجام شد',
                                    'trace_number' => $inquiryResult->id,
                                    'showDialogInquiry' => false,
                                ],
                            );
                        } else {
                            $USER_AFTER_BALANCE = $USER_BALANCE;
                            $userKhalafiyarTransfer = getTransferAppSetting();

                            if ($userKhalafiyarTransfer) {
                                $USER_AFTER_BALANCE = $USER_AFTER_BALANCE + getSourceBalance($inquiryFind->phone, 'khodroyar') ?? 0;
                            }

                            // $USER_AFTER_BALANCE = $USER_AFTER_BALANCE - $AMOUNT_PAYMENT_REQUEST_CAR;

                            $inquiryFind->balance_after = $USER_AFTER_BALANCE;
                            $inquiryFind->status = 'reject';
                            $inquiryFind->result = $result;
                            $inquiryFind->save();
                        }

                        $this->sendQabzinoServiceDown($result);

                        return $this->sendError('خلافی', ['message' => $result['Status']['Description']], Response::HTTP_SERVICE_UNAVAILABLE);
                    }
                }

            }

            // $AMOUNT_PAYMENT_REQUEST_CAR = (float) str_replace(',', '', env('AMOUNT_PAYMENT_REQUEST_CAR'));
            // $USER_BALANCE = (float) str_replace(',', '', auth()->user()->balance);

            // $resultWallet = $this->withdrawMoney($AMOUNT_PAYMENT_REQUEST_CAR, $USER_BALANCE);

            // return $this->sendResponse('نتیجه استعلام خلافی',
            //     [
            //         'message' => 'درخواست با موفقیت انجام شد',
            //         'trace_number' => $inquiryResult->id,
            //         'showDialogInquiry' => false,
            //     ],
            // );
        }

        return $this->sendError('خلافی', ['message' => 'خطا سمت سرور چند دقیقه دیگر دوباره درخواست خود را ثبت کنید'], Response::HTTP_SERVICE_UNAVAILABLE);

        // }

    }

    private function createRequestToDatabase($app, $data, $request, $type)
    {
        $USER_AFTER_BALANCE = (float) str_replace(',', '', auth()->user()->balance);

        $userKhalafiyarTransfer = getTransferAppSetting();

        if ($userKhalafiyarTransfer) {
            $USER_AFTER_BALANCE = $USER_AFTER_BALANCE + getSourceBalance(auth()->user()->phone, 'khodroyar') ?? 0;
        }

        $traceNumber = Crypt::encrypt('track_number:' . auth()->user()->phone . '_type_car:' . rand(10000, 99999) . 'created_at:' . now());
        $InquiryResult = InquiryResult::create([
            'user_id' => auth()->id(),
            'balance' => $USER_AFTER_BALANCE,
            'balance_after' => $USER_AFTER_BALANCE,
            'source' => $app->source,
            'trace_number' => $traceNumber,
            'phone' => auth()->user()->phone,
            'type' => $type,
            'detail_phone' => isset($data['details']['phone']) && $data['details']['phone'] != 'null' ? $data['details']['phone'] : '',
            'detail_national_id' => isset($data['details']['national_id']) && $data['details']['national_id'] != 'null' ? $data['details']['national_id'] : '',
            'plaque_left' => $data['plaque']['left'],
            'plaque_mid' => $data['plaque']['mid'] ?? '',
            'plaque_right' => $data['plaque']['right'],
            'plaque_alphabet' => $data['plaque']['alphabet'] ?? '',
            'data' => $data,
            'result' => null,
            'status' => 'pending',
            'agent' => $request->userAgent(),
            'redirect' => $request->input('redirect') ?? false,
        ]);

        return ['inquiryResult' => $InquiryResult, 'traceNumber' => $traceNumber];
    }

    private function responseCarNoDetails($data)
    {
        if (!app()->environment('production')) {

            return $this->sendResponse(
                'نتیجه استعلام خلافی',
                InquiryCarMouckResponseAction::handleNoDetails($data),
            );
        }

        if (!isset($data['result']['Parameters'])) {

            $data_response = [
                'type' => $data['type'],
                'with_detail_type' => $data['detail_national_id'] ? true : false,
                'amount' => null,
                'bill_id' => null,
                'payment_id' => null,
                'complaint_status' => null,
                'complaint_code' => null,
                'plaque' => isset($data['data']['plaque'])
                    ? $data['data']['plaque']
                    : [
                        'left' => $data['data']['Left'] ?? null,
                        'mid' => $data['data']['Mid'] ?? null,
                        'right' => $data['data']['Right'] ?? null,
                        'alphabet' => $data['data']['Alphabet'] ?? null,
                    ],

                'plaque_details' => isset($data['data']['details']) ? $data['data']['details'] : [],
                'payment_url' => null,
                'trace_number' => $data->id,
                'date_inquiry' => shamsiDate($data->created_at),
                'message' => $data['result']['Status']['Description'] ?? null,
            ];

            return $this->sendResponse(
                'نتیجه استعلام خلافی',
                $data_response,
            );
            // return $this->sendResponse('نتیجه استعلام خلافی',
            //     [

            //         'message' => 'خلافی برای این شماره پلاک ثبت نشده است'
            //     ],
            // );
        }

        $resultParameters = $data['result']['Parameters'];
        if ($resultParameters) {

            $data_response = [
                'type' => $data['type'],
                'with_detail_type' => false,
                'amount' => isset($resultParameters['Amount'])
                    ? formatMoney($resultParameters['Amount'] / 10)
                    : null,
                'bill_id' => $resultParameters['BillID'],
                'payment_id' => $resultParameters['PaymentID'],
                'complaint_status' => $resultParameters['ComplaintStatus'],
                'complaint_code' => $resultParameters['ComplaintCode'],
                'plaque' => isset($data['data']['plaque'])
                    ? $data['data']['plaque']
                    : [
                        'left' => $data['data']['Left'] ?? null,
                        'mid' => $data['data']['Mid'] ?? null,
                        'right' => $data['data']['Right'] ?? null,
                        'alphabet' => $data['data']['Alphabet'] ?? null,
                    ],

                'plaque_details' => isset($data['data']['details']) ? $data['data']['details'] : [],
                'payment_url' => generatePaymentLink($resultParameters['BillID'], $resultParameters['PaymentID'], $data['id']),
                'trace_number' => $data->id,
                'date_inquiry' => shamsiDate($data->created_at),
            ];

            return $this->sendResponse(
                'نتیجه استعلام خلافی',
                $data_response,
            );
        }

        $data_response = [
            'type' => $data['type'],
            'with_detail_type' => true,
            'amount' => 0,
            'bill_id' => null,
            'payment_id' => null,
            'complaint_status' => null,
            'complaint_code' => null,
            'plaque' => isset($data['data']['plaque'])
                ? $data['data']['plaque']
                : [
                    'left' => $data['data']['Left'] ?? null,
                    'mid' => $data['data']['Mid'] ?? null,
                    'right' => $data['data']['Right'] ?? null,
                    'alphabet' => $data['data']['Alphabet'] ?? null,
                ],

            'plaque_details' => [],
            'payment_url' => null,
            'trace_number' => $data->id,
            'date_inquiry' => shamsiDate($data->created_at),
            'message' => $data['result']['Status']['Description'] ?? 'اطلاعات ارسال شده نامعتبر می باشد. ',
        ];

        return $this->sendResponse(
            'نتیجه استعلام خلافی',
            $data_response,
        );

        // return $this->sendResponse('نتیجه استعلام خلافی',
        //     ['message' => 'خلافی برای این شماره پلاک ثبت نشده است'],
        // );
    }

    private function responseCarWithDetails($data)
    {

        if (!app()->environment('production')) {

            $data_response = InquiryCarMouckResponseAction::handleWithDetails($data);

            return $this->sendResponse(
                'نتیجه استعلام خلافی',
                $data_response,
            );
        }

        if (isset($data['result']['Parameters'])) {
            $resultParameters = $data['result']['Parameters'];

            $data_response = [
                'type' => $data['type'],
                'with_detail_type' => true,
                'amount' => formatMoney($resultParameters['TotalAmount'] / 10),
                'bill_id' => $resultParameters['BillID'],
                'payment_id' => $resultParameters['PaymentID'],
                'complaint_status' => '',
                'complaint_code' => '',
                'plaque' => isset($data['data']['plaque'])
                    ? $data['data']['plaque']
                    : [
                        'left' => $data['data']['Left'] ?? null,
                        'mid' => $data['data']['Mid'] ?? null,
                        'right' => $data['data']['Right'] ?? null,
                        'alphabet' => $data['data']['Alphabet'] ?? null,
                    ],

                'plaque_details' => isset($data['data']['details']) ? $data['data']['details'] : [],
                'details' => InquiryResource::collection($resultParameters['Details']),
                'payment_url' => generatePaymentLink($resultParameters['BillID'], $resultParameters['PaymentID'], $data['id']),
                'trace_number' => $data->id,
                'date_inquiry' => shamsiDate($data->created_at),
            ];

            return $this->sendResponse(
                'نتیجه استعلام خلافی',
                $data_response,
            );
        }

        $data_response = [
            'type' => $data['type'],
            'with_detail_type' => true,
            'amount' => 0,
            'bill_id' => null,
            'payment_id' => null,
            'complaint_status' => null,
            'complaint_code' => null,
            'plaque' => isset($data['data']['plaque'])
                ? $data['data']['plaque']
                : [
                    'left' => $data['data']['Left'] ?? null,
                    'mid' => $data['data']['Mid'] ?? null,
                    'right' => $data['data']['Right'] ?? null,
                    'alphabet' => $data['data']['Alphabet'] ?? null,
                ],

            'plaque_details' => [],
            'payment_url' => null,
            'trace_number' => $data->id,
            'date_inquiry' => shamsiDate($data->created_at),
            'message' => $data['result']['Status']['Description'] ?? 'اطلاعات ارسال شده نامعتبر می باشد. ',
        ];

        return $this->sendResponse(
            'نتیجه استعلام خلافی',
            $data_response,
        );
        // return $this->sendError('خلافی', ['message' => $data['result']['Status']['Description']], Response::HTTP_REQUEST_TIMEOUT);

    }

    private function withdrawMoneyDebug(float $amount, float $userBalance)
    {
        $useKhalafiyar = getTransferAppSetting();

        $khalafiyarBalance = $useKhalafiyar ? getSourceBalance(auth()->user()->phone, 'khodroyar') : 0;

        if ($userBalance < $amount && (!$useKhalafiyar || $khalafiyarBalance < $amount)) {
            return $this->sendError('خلافی', ['message' => 'موجودی شما برای دریافت استعلام خلافی کافی نمی باشد'], Response::HTTP_PAYMENT_REQUIRED);
        }

        if ($useKhalafiyar && $khalafiyarBalance >= $amount) {
            User::where('phone', auth()->user()->phone)
                ->where('source', 'khodroyar')
                ->update(['balance' => $khalafiyarBalance - $amount]);
        } else {

            User::whereId(auth()->id())->update([
                'balance' => $userBalance - $amount,
            ]);
        }

        return true;
    }

    private function withdrawMoney(float $AMOUNT_PAYMENT_REQUEST_CAR, float $USER_BALANCE)
    {
        $userKhalafiyarTransfer = getTransferAppSetting();

        if ($userKhalafiyarTransfer) {

            $userKhalafiuarBalance = getSourceBalance(auth()->user()->phone, 'khodroyar');

            if ($userKhalafiuarBalance < $AMOUNT_PAYMENT_REQUEST_CAR) {

                if ($USER_BALANCE < $AMOUNT_PAYMENT_REQUEST_CAR) {
                    return $this->sendError('خلافی', ['message' => 'موجودی شما برای دریافت استعلام خلافی کافی نمی باشد'], Response::HTTP_PAYMENT_REQUIRED);
                } elseif ($USER_BALANCE >= $AMOUNT_PAYMENT_REQUEST_CAR) {
                    $RESULT = $USER_BALANCE - $AMOUNT_PAYMENT_REQUEST_CAR;
                    User::whereId(auth()->id())->update([
                        'balance' => (float) $RESULT,
                    ]);

                    return true;
                }

                return $this->sendError('خلافی', ['message' => 'موجودی شما برای دریافت استعلام خلافی کافی نمی باشد'], Response::HTTP_PAYMENT_REQUIRED);

            } else {

                if ($userKhalafiuarBalance > $AMOUNT_PAYMENT_REQUEST_CAR) {
                    $RESULT = $userKhalafiuarBalance - $AMOUNT_PAYMENT_REQUEST_CAR;
                    User::where('phone', auth()->user()->phone)
                        ->where('source', 'khodroyar')->update([
                                'balance' => (float) $RESULT,
                            ]);

                    return true;
                }

                return $this->sendError('خلافی', ['message' => 'موجودی شما برای دریافت استعلام خلافی کافی نمی باشد'], Response::HTTP_PAYMENT_REQUIRED);
            }

        } else {
            if ($USER_BALANCE < $AMOUNT_PAYMENT_REQUEST_CAR) {
                return $this->sendError('خلافی', ['message' => 'موجودی شما برای دریافت استعلام خلافی کافی نمی باشد'], Response::HTTP_PAYMENT_REQUIRED);
            }

            $RESULT = $USER_BALANCE - $AMOUNT_PAYMENT_REQUEST_CAR;
            User::whereId(auth()->id())->update([
                'balance' => (float) $RESULT,
            ]);

            return true;
        }

    }

    private function showResultWithTraceCar($data)
    {

        if ($data->detail_phone == null) {

            return $this->responseCarNoDetails($data);
        }

        return $this->responseCarWithDetails($data);
    }

    private function showResultWithTraceMotor($data)
    {
        if ($data->detail_phone == null) {
            return $this->responseMotorNoDetails($data);
        }

        return $this->responseMotorWithDetails($data);
    }

    private function responseMotorWithDetails($data)
    {
        if (!app()->environment('production')) {

            return $this->sendResponse(
                'نتیجه استعلام خلافی',
                InquiryMotorMouckResponseAction::handleWithDetails($data),
            );
        }

        if (isset($data['result']['Parameters'])) {
            $resultParameters = $data['result']['Parameters'];

            $data_response = [
                'type' => 'motor',
                'with_detail_type' => true,
                'amount' => formatMoney($resultParameters['TotalAmount'] / 10),
                'bill_id' => $resultParameters['BillID'],
                'payment_id' => $resultParameters['PaymentID'],
                'complaint_status' => '',
                'complaint_code' => '',
                'plaque' => isset($data['data']['plaque'])
                    ? $data['data']['plaque']
                    : [
                        'left' => $data['data']['Left'] ?? null,
                        'mid' => $data['data']['Mid'] ?? null,
                        'right' => $data['data']['Right'] ?? null,
                        'alphabet' => $data['data']['Alphabet'] ?? null,
                    ],

                'plaque_details' => isset($data['data']['details']) ? $data['data']['details'] : [],
                'details' => InquiryResource::collection($resultParameters['Details']),
                'payment_url' => generatePaymentLink($resultParameters['BillID'], $resultParameters['PaymentID'], $data['id']),
                'trace_number' => $data->id,
                'date_inquiry' => shamsiDate($data->created_at),
            ];

            return $this->sendResponse(
                'نتیجه استعلام خلافی',
                $data_response,
            );
        }

        $data_response = [
            'type' => 'motor',
            'with_detail_type' => true,
            'amount' => 0,
            'bill_id' => null,
            'payment_id' => null,
            'complaint_status' => null,
            'complaint_code' => null,
            'plaque' => isset($data['data']['plaque'])
                ? $data['data']['plaque']
                : [
                    'left' => $data['data']['Left'] ?? null,
                    'mid' => $data['data']['Mid'] ?? null,
                    'right' => $data['data']['Right'] ?? null,
                    'alphabet' => $data['data']['Alphabet'] ?? null,
                ],

            'plaque_details' => [],
            'payment_url' => null,
            'trace_number' => $data->id,
            'date_inquiry' => shamsiDate($data->created_at),
            'message' => $data['result']['Status']['Description'] ?? 'اطلاعات ارسال شده نامعتبر می باشد. ',
        ];

        return $this->sendResponse(
            'نتیجه استعلام خلافی',
            $data_response,
        );

        // return $this->sendError('خلافی', ['message' => 'اطلاعات ارسال شده نامعتبر می باشد. '], Response::HTTP_FORBIDDEN);

    }

    private function responseMotorNoDetails($data)
    {
        if (!app()->environment('production')) {

            return $this->sendResponse(
                'نتیجه استعلام خلافی',
                InquiryMotorMouckResponseAction::handleNoDetails($data),
            );
        }

        $resultParameters = $data['result']['Parameters'];
        if ($resultParameters) {

            $data_response = [
                'type' => 'motor',
                'with_detail_type' => false,
                'amount' => formatMoney($resultParameters['Amount'] / 10),
                'bill_id' => $resultParameters['BillID'],
                'payment_id' => $resultParameters['PaymentID'],
                'complaint_status' => $resultParameters['ComplaintStatus'],
                'complaint_code' => $resultParameters['ComplaintCode'],
                'plaque' => isset($data['data']['plaque'])
                    ? $data['data']['plaque']
                    : [
                        'left' => $data['data']['Left'] ?? null,
                        'mid' => $data['data']['Mid'] ?? null,
                        'right' => $data['data']['Right'] ?? null,
                        'alphabet' => $data['data']['Alphabet'] ?? null,
                    ],

                'plaque_details' => isset($data['data']['details']) ? $data['data']['details'] : [],
                'payment_url' => generatePaymentLink($resultParameters['BillID'], $resultParameters['PaymentID'], $data['id']),
                'trace_number' => $data->id,
                'date_inquiry' => shamsiDate($data->created_at),
            ];

            return $this->sendResponse(
                'نتیجه استعلام خلافی',
                $data_response,
            );
        }

        $data_response = [
            'type' => 'motor',
            'with_detail_type' => false,
            'amount' => 0,
            'bill_id' => null,
            'payment_id' => null,
            'complaint_status' => null,
            'complaint_code' => null,
            'plaque' => isset($data['data']['plaque'])
                ? $data['data']['plaque']
                : [
                    'left' => $data['data']['Left'] ?? null,
                    'mid' => $data['data']['Mid'] ?? null,
                    'right' => $data['data']['Right'] ?? null,
                    'alphabet' => $data['data']['Alphabet'] ?? null,
                ],

            'plaque_details' => [],
            'payment_url' => null,
            'trace_number' => $data->id,
            'date_inquiry' => shamsiDate($data->created_at),
            'message' => $data['result']['Status']['Description'] ?? 'هیچ اطلاعاتی برای این پلاک یافت نشد',
        ];

        return $this->sendResponse(
            'نتیجه استعلام خلافی',
            $data_response,
        );

        // return $this->sendResponse('نتیجه استعلام خلافی',
        //     ['message' => 'خلافی برای این شماره پلاک ثبت نشده است'],
        // );
    }

    private function checkInquiryDatabase(array $data, $type, $app, $request)
    {
        // $AMOUNT_PAYMENT_REQUEST_CAR = (float) str_replace(',', '', env('AMOUNT_PAYMENT_REQUEST_CAR'));
        // $USER_BALANCE = (float) str_replace(',', '', auth()->user()->balance);
        // if ($USER_BALANCE < $AMOUNT_PAYMENT_REQUEST_CAR) {
        //     return $this->sendError('خلافی', ['message' => 'موجودی شما برای دریافت استعلام خلافی کافی نمی باشد'], Response::HTTP_PAYMENT_REQUIRED);
        // }

        if (!isset($data['inquiry']) || $data['inquiry'] === false) {

            $plaque_left = isset($data['plaque']['left']) ? $data['plaque']['left'] : '';
            $plaque_right = isset($data['plaque']['right']) ? $data['plaque']['right'] : '';
            $plaque_mid = isset($data['plaque']['mid']) ? $data['plaque']['mid'] : '';
            $plaque_alphabet = isset($data['plaque']['alphabet']) ? $data['plaque']['alphabet'] : '';
            $national_id = isset($data['details']['national_id']) ? $data['details']['national_id'] : '';
            $detail_phone = isset($data['details']['phone']) ? $data['details']['phone'] : '';

            $result = null;
            $query = InquiryResult::where('phone', auth()->user()->phone)
                // ->where('source', $app->source)
                ->where('type', $type)
                ->where('plaque_left', $plaque_left)
                ->where('plaque_mid', $plaque_mid)
                ->where('plaque_right', $plaque_right)
                ->where('plaque_alphabet', $plaque_alphabet)
                ->whereNotNull('result.Parameters')
                // ->whereIn('status', ['pending', 'reject'])
                ->where('created_at', '>=', now()->subDays(2));
            // ->latest()->first();


            if ($detail_phone != '' && $national_id != '') {
                $result = $query->where('detail_national_id', $national_id)
                    ->where('detail_phone', $detail_phone)
                    ->latest()
                    ->first();
            } else {
                $result = $query->where('detail_national_id', '')
                    ->where('detail_phone', '')
                    ->latest()
                    ->first();
            }



            if (isset($result)) {

                return $this->sendResponse(
                    'نتیجه استعلام خلافی',
                    [
                        'message' => 'شما در 2 روز گذشته با این شماره پلاک استعلام گرفته اید آیا میخواهید مجدد استعلام بگیرید و هزینه استعلام از حساب شما کسر گردد یا میخواهید همان استعلام قبی به شما نمایش داده شود',
                        'trace_number' => $result->id,
                        'showDialogInquiry' => true,
                    ],
                );
            }

            return null;

        }
    }

    private function validateInquiry($request, $type)
    {
        if ($type == 'car') {
            return $request->validate([
                'details' => 'nullable|array',
                'details.phone' => 'nullable',
                'details.national_id' => 'nullable',
                'plaque.left' => 'required',
                'plaque.mid' => 'required',
                'plaque.right' => 'required',
                'plaque.alphabet' => 'required|string',
                'inquiry' => 'nullable|bool',
            ]);
        }

        return $request->validate([
            'details' => 'nullable|array',
            'details.phone' => 'nullable',
            'details.national_id' => 'nullable',
            'plaque.left' => 'required',
            'plaque.right' => 'required',
            'inquiry' => 'nullable|bool',
        ]);
    }

    private function inquiryStart($type, $request, $data)
    {
        if ($type == 'car' || $type == 'motor') {

            return $this->inquiry($request, $data, $type);

        }

        return $this->sendError('خلافی', ['message' => 'آدرس صدا زده شده وجود ندارد. در حال حاضر فقط سرویس استعلام خودرو و موتور در این سرویس وجود دارد'], Response::HTTP_FORBIDDEN);

    }

    private function message(string $errorCode)
    {
        return match ($errorCode) {
            'GM5001' => 'اطلاعات مورد نظر یافت نشد.',
            'GM5002' => 'پارامترهای ورودی به درستی تامین نشده است.',
            'GM5004' => 'امکان برقراری ارتباط با سرویس‌دهنده وجود ندارد، لطفاً لحظاتی دیگر مجدداً تلاش نمایید.',
            'GM5022' => 'کد ملی معتبر نیست.',
            'GM5023' => 'شماره پلاک صحیح نیست.',
            'GM5024' => 'شماره همراه ارسال شده نامعتبر است، الزامی است شماره همراه به نام متقاضی باشد.',
            'GM5025' => 'کد ملی ارسال شده نامعتبر است، الزمه است کد ملی متعلق به مالک وسیله نقلیه باشد.',
            'GM5026' => 'کاربر گرامی، موجودی کیف پول شما برای ارائه خدمت درخواست شده کافی نیست. لطفاً نسبت به شارژ کیف پول خود اقدام نمایید.',
            'GM5028' => 'کاربر گرامی، در حال حاضر به دلیل محدودیت اعمال شده از طرف سرویس‌دهنده امکان ارائه خدمت به پلاک مورد نظر وجود ندارد.',
            'GM5031' => 'قبضی برای پرداخت یافت نشد.',
            'GM5032' => 'پلاک در سامانه اختصاص نیافته است.',
            'GM5038' => 'پلاک مورد نظر دارای شکایت می‌باشد، از این رو امکان ارائه خدمات از سمت پلیس راهور تا زمان رفع شکایت وجود ندارد.',
            default => 'کد خطا نامعتبر است.',
        };
    }


    private function sendQabzinoServiceDown($result)
    {
        try {
            if ($result['Status']['Code'] == 'GM5004') {

                $phone = '09334332338';

                Cache::put('enabled_qabzino_service_down_job', true);

                $message = '🔴 قبضینو - سرویس استعلام خلافی قبضینو با خطای «امکان برقراری ارتباط با سرویس دهنده وجود ندارد» مواجه شد' . ' - تاریخ ' . dateTimeToday();

                ServiceHealthLog::create([
                    'service' => 'qabzino',
                    'type' => 'disconnect',
                    'phone' => $phone,
                    'message' => $message,
                ]);

                Notification::route(BaleChannel::class, 'any_identifier')
                    ->notify(new QabzinoServiceDown($message));
            }
        } catch (\Exception $e) {

        }
    }
}
