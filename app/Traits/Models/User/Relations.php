<?php

namespace App\Traits\Models\User;

use App\Models\InquiryResult;
use App\Models\Transaction;

trait Relations
{
    public function inquires()
    {
        return $this->hasMany(InquiryResult::class, 'phone', 'phone');
    }

    public function transactions()
    {
        return $this->hasMany(Transaction::class, 'phone', 'phone');
    }

    public function transaction()
    {
        return $this->belongsTo(Transaction::class, 'phone', 'phone')->latest();
    }
}
