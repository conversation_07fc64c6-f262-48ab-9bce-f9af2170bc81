<?php

namespace App\Traits\Models\User;

use App\Models\Role;

trait Functions
{
    public function latestTransactionDate()
    {
        $latestDate = $this->transactions()
            ->latest()
            ->pluck('creationTime')
            ->filter()
            ->first();

        if (! $latestDate) {
            $latestDate = $this->transactions()
                ->latest()
                ->pluck('created_at')
                ->filter()
                ->first();
        }

        return $latestDate ? shamsiDate($latestDate) : null;
    }

    public function lastThreeTransactionTotal()
    {
        $total = $this->transactions()
            ->latest()
            ->take(10)
            ->pluck('amount')
            ->filter()
            ->sum();

        return $total > 0 ? formatMoney($total) : null;
    }

    public function isAdmin()
    {
        return $this->level == 'admin' ? true : false;
    }

    public function roles()
    {
        return $this->belongsToMany(Role::class);
    }

    public function hasRole($role)
    {
        if (is_string($role)) {
            return $this->roles->contains('name', $role);
        }

        return (bool) $role->intersect($this->roles)->count();
    }
}
