<?php

namespace App\Jobs;

use App\Models\TransactionRefund;
use App\Models\TransactionResultRefund;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class ZarinpalRefundTransactionJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    // $refounds = TransactionRefund::where('pause', false)->where('status_request', 'pending')->get();
    // foreach ($refounds as $item) {

    //     $refId = $item->transactions[0]['refId'] ?? null;
    //     $transaction_amount = (float) str_replace(',', '', $item->transactions[0]['amount']) ?? 0;
    //     $refound_amount = (float) str_replace(',', '', $item->refound_amount);
    //     $response = refoundZarinpalWithRefId(substr(strval($item->refId), 0, -2), $refound_amount, $item->description, $item->method);
    //     $result = $response->getData(true);
    //     $item->status_request = 'accept';
    //     if (isset($result['data']['resource']['timeline']['refund_status'])) {
    //         $item->status_gate = $result['data']['resource']['timeline']['refund_status'];
    //         $item->pause = true;
    //     }
    //     $item->save();

    //     TransactionResultRefund::create([
    //         'transaction_id' => $item->id,
    //         'phone' => $item->phone,
    //         'result' => $response,
    //     ]);
    // }

    public function handle(): void
    {

        $refounds = TransactionRefund::where('pause', false)
            ->where('status_request', 'pending')
            ->get();
        // $a = 0;
        foreach ($refounds as $refoundItem) {
            $refound_amount = (float) str_replace(',', '', $refoundItem->refound_amount);
            $remaining_amount = $refound_amount;

            // مرتب‌سازی تراکنش‌ها از بزرگ‌ترین مبلغ
            $transactions = collect($refoundItem->transactions)
                ->sortByDesc('amount');

            foreach ($transactions as $transaction) {

                if ($remaining_amount <= 0) {
                    break;
                }

                $refId = $transaction['refId'] ?? null;
                if (! $refId) {
                    continue;
                }

                $transaction_amount = (float) $transaction['amount'];
                $amount_to_refund = min($transaction_amount, $remaining_amount);

                // فراخوانی تابع بازگشت وجه
                $response = refoundZarinpalWithRefId(
                    substr(strval($refId), 0, -2),
                    $amount_to_refund,
                    $refoundItem->description,
                    $refoundItem->method
                );

                $result = is_object($response) && method_exists($response, 'getData')
                    ? $response->getData(true)
                    : (is_array($response) ? $response : json_decode(json_encode($response), true));

                // ثبت نتیجه بازگشت وجه

                $remaining_amount -= $amount_to_refund;

                TransactionResultRefund::create([
                    'transaction_id' => $refoundItem->id,
                    'phone' => $refoundItem->phone,
                    'result' => $result,
                    'amount' => $amount_to_refund,
                ]);

            }

            $refoundItem->status_request = 'accept';
            $refoundItem->request_count += 1;

            if ($remaining_amount <= 0) {

                $refoundItem->pause = true;
                $refoundItem->status_gate = 'accept';

            } else {
                $refoundItem->status_gate = 'reject';
            }

            $refoundItem->save();
        }
    }
}
