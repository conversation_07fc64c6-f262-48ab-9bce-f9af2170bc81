<?php

namespace App\Jobs;

use App\Models\Payamk;
use App\Services\MeliPayamak as ServiceMeliPayamak;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class AuthSMSJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $token;

    public $phone;

    public $pattern;

    public $source;

    public $tries = 5;

    public function __construct($token, $phone, $pattern, $source)
    {
        $this->token = $token;
        $this->phone = $phone;
        $this->pattern = $pattern;
        $this->source = $source;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {

            $result = (new ServiceMeliPayamak)->sendAuthOtp($this->token, $this->phone, $this->pattern);
            $content = ' پنل مدیریت - ارسال کد فعال سازی برای شماره موبایل '.$this->phone;

            Payamk::create([
                'phone' => $this->phone,
                'message' => $this->token,
                'source' => $this->source,
                'description' => $content,
                'gate' => 'melipayamak',
                'response' => $result,
            ]);

        } catch (Exception $e) {
            echo $e->getMessage();
        }
    }
}
