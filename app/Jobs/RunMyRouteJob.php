<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Http;

class RunMyRouteJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Http::timeout(60)->retry(3, 100)->get('https://dl.khodrox.com/close-request-pending');
        Http::timeout(60)->retry(3, 100)->get('https://dl.khodrox.com/replay-host-checker');
        Http::timeout(60)->retry(3, 100)->get('https://dl.khodrox.com/start-host-checker');
    }
}
