<?php

namespace App\Jobs;

use App\Models\InquiryResult;
use App\Models\ServiceHealthLog;
use App\Models\Setting;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Cache;

class CheckQbzinoServiceHealthJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {

        if (Setting::isActive('reportServiceUpQabzinoBot')) {

            $userKhalafiyarTransfer = getTransferAppSetting();

            // if (! $userKhalafiyarTransfer) {
            //     $lastInquiry = InquiryResult::where('status', 'success')
            //         ->latest()->where('source', 'khodroyar')->first();
            // } else {
            $lastInquiry = InquiryResult::where('status', 'success')
                ->latest()->first();
            // }

            $now = now();
            $phone = '09334332338';

            if (! $lastInquiry || $lastInquiry->created_at <= $now->subMinutes(value: 15)) {

                // if (! $userKhalafiyarTransfer) {
                //     $lastAlert = ServiceHealthLog::where('service', 'qabzino')
                //         ->where('type', 'inquiry-downtime')
                //         ->latest('created_at')
                //         ->first();
                // } else {
                $lastAlert = InquiryResult::where('result.Status.Code', 'G00000')
                    ->latest()
                    ->first();
                // }

                $shouldSend = false;
                $delayMinutes = 0;

                if (! $lastAlert) {
                    $shouldSend = true;
                    $delayMinutes = 15;
                } else {
                    $count = ServiceHealthLog::where('service', 'qabzino')
                        ->where('type', 'inquiry-downtime')
                        ->where('created_at', '>=', $now->subMinutes(15))
                        ->count();

                    $delayMinutes = 15 + ($count - 1) * 15;

                    if ($lastAlert->created_at <= $now->subMinutes($delayMinutes)) {
                        $shouldSend = true;
                    }
                }

                if ($shouldSend) {
                    $minutesAgo = get_ago($lastInquiry->created_at);
                    $message = "🔴 قبضینو - در حدود $minutesAgo  هیچ استعلام موفقی ثبت نشده است. لطفاً سیستم بررسی شود".' - تاریخ '.dateTimeToday();

                    ServiceHealthLog::create([
                        'service' => 'qabzino',
                        'type' => 'inquiry-downtime',
                        'phone' => $phone,
                        'message' => $message,
                    ]);

                    sendQabzinoBot($message);

                    Cache::put('enabled_inquiryـrecoveredـqabzino', true);

                }

            } else {

                $message = '🟢 قبضینو - خلافی خودرو و موتور'.' - تاریخ '.dateTimeToday();
                $enabled = Cache::get('enabled_inquiryـrecoveredـqabzino', true);
                if ($enabled) {
                    // if (! $userKhalafiyarTransfer) {

                    ServiceHealthLog::create([
                        'service' => 'qabzino',
                        'type' => 'inquiry-recovered',
                        'phone' => $phone,
                        'message' => $message,
                    ]);

                    sendQabzinoBot($message);
                    Cache::put('enabled_inquiryـrecoveredـqabzino', false);

                    // Cache::put('enabled_qabzino_service_down_job', true);
                    // } else {
                    //     $lastAlertDownTitme = ServiceHealthLog::where('service', 'qabzino')
                    //         ->latest()
                    //         ->first();

                    //     if ($lastAlertDownTitme->type != 'reconnect' && $lastAlertDownTitme->type != 'inquiry-recovered') {
                    //         sendQabzinoBot($message);
                    //         Cache::put('enabled_qabzino_service_down_job', true);
                    //     }
                    // }

                }

            }

        }

    }
}
