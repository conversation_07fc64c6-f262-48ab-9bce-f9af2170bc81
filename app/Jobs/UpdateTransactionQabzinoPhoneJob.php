<?php

namespace App\Jobs;

use App\Models\TransactionUserQabzino;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class UpdateTransactionQabzinoPhoneJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {

        TransactionUserQabzino::where('phone', null)
            ->chunk(5, function ($transactions) {
                foreach ($transactions as $transaction) {

                    $relatedPhones = $transaction->relatedPhones();

                    if ($relatedPhones->isNotEmpty()) {

                        $transaction->phone = $relatedPhones->implode(', ');
                        $transaction->save();
                    }
                }
            });
    }
}
