<?php

namespace App\Jobs;

use App\Models\HostCheckLog;
use App\Models\NodeHostChecker;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class HostHttpCheckJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $hosts = HostCheckLog::where('active', true)->pluck('url')->toArray();

        // dd($hosts);

        $selectedNodes = NodeHostChecker::where('active', true)->pluck('hostname')->toArray();
        $nodeParams = implode('&node=', $selectedNodes);

        $results = [];

        foreach ($hosts as $host) {
            $hostChecklog = HostCheckLog::create([
                'host' => $host,
                'request_id' => null,
                'response' => null,
                'result' => null,
                'status' => 'pending',
            ]);

            $checkUrl = 'https://check-host.net/check-http?host='.urlencode($host).'&node='.$nodeParams;
            $checkResponse = curl_get_json($checkUrl, 60);

            if (! $checkResponse || ! isset($checkResponse['request_id'])) {
                $hostChecklog->update([
                    'response' => $checkResponse,
                    'status' => 'error',
                ]);

                $results[] = [
                    'host' => $host,
                    'ok' => false,
                    'error' => 'Invalid response or timeout from check-host',
                    'response' => $checkResponse,
                ];

                continue;
            }

            $requestId = $checkResponse['request_id'];

            $hostChecklog->update([
                'request_id' => $requestId,
                'response' => $checkResponse,
            ]);

            sleep(60); // دقت کن: برای چند سایت زمان زیادی طول می‌کشه. راهکار بهتر: صف یا پردازش async.

            $resultUrl = "https://check-host.net/check-result/{$requestId}";
            $resultResponse = curl_get_json($resultUrl, 10);

            if (! $resultResponse || ! is_array($resultResponse)) {
                $hostChecklog->update([
                    'result' => $resultResponse,
                    'status' => 'error',
                ]);

                $results[] = [
                    'host' => $host,
                    'ok' => false,
                    'error' => 'Invalid result or timeout from check-host',
                    'request_id' => $requestId,
                    'result' => $resultResponse,
                ];

                continue;
            }

            $hostChecklog->update([
                'result' => $resultResponse,
                'status' => 'completed',
            ]);

            $results[] = [
                'host' => $host,
                'ok' => true,
                'request_id' => $requestId,
                'permanent_link' => $checkResponse['permanent_link'] ?? null,
                'result' => $resultResponse,
            ];
        }

        // return response()->json([
        //     'all_ok' => collect($results)->every(fn ($r) => $r['ok']),
        //     'results' => $results,
        // ]);
    }
}
