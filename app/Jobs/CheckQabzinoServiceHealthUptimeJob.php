<?php

namespace App\Jobs;

use App\Models\InquiryResult;
use App\Models\ServiceHealthLog;
use App\Models\Setting;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Cache;

class CheckQabzinoServiceHealthUptimeJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct() {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        if (Setting::isActive('reportServiceUpQabzinoBot')) {
            $enabled = Cache::get('enabled_qabzino_service_down_job', true);
            if ($enabled) {

                $resultSuccess = InquiryResult::where('result.Status.Code', 'G00000')
                    ->latest()
                    ->first();

                if ($resultSuccess) {

                    $phone = '09334332338';

                    $lastLog = ServiceHealthLog::where('service', 'qabzino')
                        ->where('type', 'disconnect')
                        ->latest('created_at')
                        ->first();

                    if ($lastLog) {

                        $message = '🟢 قبضینو - سرویس خلافی خودرو و موتور'.' - تاریخ '.dateTimeToday();

                        ServiceHealthLog::create(attributes: [
                            'service' => 'qabzino',
                            'type' => 'reconnect',
                            'phone' => $phone,
                            'message' => $message,
                        ]);

                        // sendQabzinoBot($message);

                        Cache::put('enabled_qabzino_service_down_job', false);

                    }
                }

            }
        }
    }
}
