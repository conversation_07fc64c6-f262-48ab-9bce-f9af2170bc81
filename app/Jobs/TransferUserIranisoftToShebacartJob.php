<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\DB;

class TransferUserIranisoftToShebacartJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // $chunkSize = 10;

        // DB::connection('mongodb_iranisoft')->table('users')
        //     ->whereIn('source', [
        //         'migrofin', 'bankyar', 'pishkhan724', 'kouroshcc',
        //         'shebacart', 'cardtosheba', 'irancheque', 'cardbecard', 'pishkhaneman',
        //     ])
        //     ->orderBy('_id') // مهم برای مرتب بودن chunkها
        //     ->chunk($chunkSize, function ($users) {
        //         foreach ($users as $user) {
        //             DB::connection('mongodb_farzard')->table('users')->latest()->insert([
        //                 'phone' => $user->phone,
        //                 'balance' => $user->balance,
        //                 'source' => $user->source,
        //                 'transfer' => true,
        //                 'lock' => false,
        //             ]);
        //         }
        //     });
    }
}
