<?php

namespace App\Jobs;

use App\Models\InquiryResult;
use App\Models\Setting;
use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class GenerateDailyQabzinoReportJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            if (! Setting::isActive('reportDailyBot')) {
                return;
            }

            $today = \Carbon\Carbon::today();
            $sources = ['khodrox', 'naghlie', 'khodroyar', 'khalafionline', 'migrofin'];

            $sourceNameMap = [
                'khodrox' => 'خودراکس',
                'naghlie' => 'نقلیه',
                'khodroyar' => 'خلافیار',
                'khalafionline' => 'خلافی‌آنلاین',
                'migrofin' => 'مایگروفین قدیم',
            ];

            // استفاده از کوئری‌های بهینه‌تر و موازی سازی
            $inquiryResults = [
                'total' => DB::connection('mongodb')->table('inquiry_results')
                    ->where(function ($q) use ($today) {
                        $q->whereDate('created_at', $today)
                            ->orWhereDate('created_time', $today);
                    })->count(),

                'car' => InquiryResult::where('type', 'car')->whereToday()->count(),
                'motor' => InquiryResult::where('type', 'motor')->whereToday()->count(),
                'success' => InquiryResult::whereIn('result.Status.Code', ['G00000', 'GM5031'])->whereToday()->count(),
                'reject' => InquiryResult::whereNotIn('result.Status.Code', ['G00000', 'GM5031'])->whereToday()->count(),
                'redirected' => InquiryResult::where('redirect', true)->whereToday()->count(),
            ];

            // جمع‌آوری آمار منابع به صورت موازی
            $sourceCounts = [];
            foreach ($sources as $source) {
                $sourceCounts[$source] = InquiryResult::where('source', $source)->whereToday()->count();
            }

            // کاربران جدید
            $newUsersCount = User::whereToday()->count();
            $shebacarUser = DB::connection('mongodb_shebacart')->table('users')
                ->where(function ($q) use ($today) {
                    $q->whereDate('creationTime', $today)
                        ->orWhereDate('createdAt', $today);
                })->count();
            $totalNewUsers = $newUsersCount + $shebacarUser;

            // تراکنش‌ها
            $transactions = [
                'success' => [
                    'amount' => DB::connection('mongodb')->table('transactions')
                        ->where(function ($q) use ($today) {
                            $q->whereDate('createdAt', $today)
                                ->orWhereDate('creationTime', $today);
                        })->sum('amount'),
                    'count' => DB::connection('mongodb')->table('transactions')
                        ->where(function ($q) use ($today) {
                            $q->whereDate('createdAt', $today)
                                ->orWhereDate('creationTime', $today);
                        })->count(),
                ],
                'pending' => DB::connection('mongodb')->table('paymentpendings')
                    ->where(function ($q) use ($today) {
                        $q->whereDate('createdAt', $today)
                            ->orWhereDate('creationTime', $today);
                    })->sum('amount'),
            ];

            $shebacartTransactions = [
                'success' => DB::connection('mongodb_shebacart')->table('transactions')
                    ->where('status', 'success')
                    ->where(function ($q) use ($today) {
                        $q->whereDate('createdAt', $today)
                            ->orWhereDate('creationTime', $today);
                    })->sum('amount'),
                'pending' => DB::connection('mongodb_shebacart')->table('transactions')
                    ->where('status', '!=', 'success')
                    ->where(function ($q) use ($today) {
                        $q->whereDate('createdAt', $today)
                            ->orWhereDate('creationTime', $today);
                    })->sum('amount'),
            ];

            // تراکنش‌های به تفکیک منبع
            $transactionsBySource = [];
            foreach ($sources as $source) {
                $transactionsBySource[$source] = [
                    'sum' => DB::connection('mongodb')->table('transactions')
                        ->where('source', $source)
                        ->where(function ($q) use ($today) {
                            $q->whereDate('createdAt', $today)
                                ->orWhereDate('creationTime', $today);
                        })->sum('amount'),
                    'count' => DB::connection('mongodb')->table('transactions')
                        ->where('source', $source)
                        ->where(function ($q) use ($today) {
                            $q->whereDate('createdAt', $today)
                                ->orWhereDate('creationTime', $today);
                        })->count(),
                ];
            }

            // ساخت پیام گزارش
            $message = "📊 *گزارش روزانه استعلام خلافی خودرو و موتور*\n";
            $message .= '📅 تاریخ: '.dateTimeToday()."\n\n";

            $message .= "🔍 تعداد کل استعلام‌ها: *{$inquiryResults['total']}*\n";
            $message .= "🚗 استعلام خودرو: *{$inquiryResults['car']}*\n";
            $message .= "🏍️ استعلام موتور: *{$inquiryResults['motor']}*\n\n";

            foreach ($sourceCounts as $source => $count) {
                $nameFa = $sourceNameMap[$source] ?? $source;
                $message .= "🔹{$nameFa}: *{$count}*\n";
            }

            $message .= "🔁 خلافیار به خودراکس: *{$inquiryResults['redirected']}*\n\n";

            $message .= "📈 *نتایج استعلام‌ها:*\n";
            $message .= "✅ موفق: *{$inquiryResults['success']}*\n";
            $message .= "❌ ناموفق: *{$inquiryResults['reject']}*\n\n";

            $message .= "🧑‍💻 *کاربران جدید امروز:* *{$totalNewUsers} نفر*\n";
            $message .= "🔸 شباکارت: *{$shebacarUser} نفر*\n\n";

            $message .= "💳 *تراکنش‌های مالی امروز:*\n";
            $totalTransactionRaw = $transactions['success']['amount'] + $transactions['pending'];
            $message .= '📦 مجموع کل: *'.formatMoney($totalTransactionRaw)." تومان*\n";
            $message .= '✅ موفق: *'.formatMoney($transactions['success']['amount'])." تومان*\n";
            $message .= '   تعداد : *'.formatMoney($transactions['success']['count'])." عدد*\n\n";

            foreach ($transactionsBySource as $source => $data) {
                $nameFa = $sourceNameMap[$source] ?? $source;
                $message .= "🔹 *{$nameFa}*\n";
                $message .= '  ✅ موفق: *'.formatMoney($data['sum'])." تومان*\n";
                $message .= '   تعداد: *'.formatMoney($data['count'])." عدد*\n\n";
            }

            $shTotal = $shebacartTransactions['success'] + $shebacartTransactions['pending'];
            $message .= "🔹 *سرویس های شباکارت*\n";
            $message .= '  ✅ موفق: *'.formatMoney($shebacartTransactions['success'])." تومان*\n";
            $message .= '  ⏳ در انتظار: *'.formatMoney($shebacartTransactions['pending'])." تومان*\n";
            $message .= '  💰 مجموع: *'.formatMoney($shTotal)." تومان*\n\n";

            // return $message;
            // ارسال گزارش
            sendQabzinoBot($message);
        } catch (\Exception $e) {
            Log::error('Daily report error: '.$e->getMessage());

            // return $e->getMessage();
        }
    }
}
