<?php

namespace App\Jobs;

use App\Models\Article;
use App\Services\FtpSitemapService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;
use Exception;
use Illuminate\Support\Facades\Storage;

class GenerateSitemapJob implements ShouldQueue
{
    use Queueable;

    private const SITEMAP_BASE_PATH = 'sitemaps';


    public function __construct()
    {

    }


    public function handle(): void
    {
        try {

            $this->generateArticlesSitemap();

            $this->generateServicesSitemap();

            $this->generateMainSitemap();

            // $this->uploadToFtp();

        } catch (Exception $e) {

            throw $e;
        }
    }


    private function generateArticlesSitemap()
    {
        $articles = Article::where('active', true)
            ->where(function ($q) {
                $q->whereNull('page')
                    ->orWhere('page', '');
            })
            ->where('status', 'published')
            ->where('active', true)
            ->get();

        $urls = [];
        foreach ($articles as $article) {
            $urls[] = [
                'loc' => "https://khodrox.com/blog/{$article->slug}",
                'lastmod' => $article->updated_at->format('Y-m-d'),
                'changefreq' => 'monthly',
                'priority' => '0.7'
            ];
        }

        $xml = $this->generateUrlsetXml($urls);


        $this->ensureSitemapsDirectory();


        $path = storage_path(self::SITEMAP_BASE_PATH . '/articles.xml');
        file_put_contents($path, $xml);

        Log::info("فایل articles.xml تولید شد - تعداد مقالات: " . count($articles));
    }


    private function generateServicesSitemap()
    {
        $services = Article::where('active', true)
            ->whereNotNull('page')
            ->where('page', '!=', '')
            ->where('status', 'published')
            ->get();

        $urls = [];
        foreach ($services as $service) {
            $urls[] = [
                'loc' => "https://khodrox.com{$service->page}",
                'lastmod' => $service->updated_at->format('Y-m-d'),
                'changefreq' => 'weekly',
                'priority' => '0.8'
            ];
        }

        $xml = $this->generateUrlsetXml($urls);


        $path = storage_path(self::SITEMAP_BASE_PATH . '/services.xml');
        file_put_contents($path, $xml);

        Log::info("فایل services.xml تولید شد - تعداد سرویس‌ها: " . count($services));
    }


    private function generateMainSitemap()
    {
        $sitemaps = [
            [
                'loc' => 'https://khodrox.com/' . self::SITEMAP_BASE_PATH . '/articles.xml',
                'lastmod' => now()->format('Y-m-d')
            ],
            [
                'loc' => 'https://khodrox.com/' . self::SITEMAP_BASE_PATH . '/services.xml',
                'lastmod' => now()->format('Y-m-d')
            ]
        ];

        $xml = $this->generateSitemapIndexXml($sitemaps);


        $path = storage_path(self::SITEMAP_BASE_PATH . '/sitemap.xml');
        file_put_contents($path, $xml);

        Log::info("فایل sitemap.xml اصلی تولید شد");
    }


    private function generateSitemapIndexXml($sitemaps)
    {
        $xml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
        $xml .= "<sitemapindex xmlns=\"https://www.sitemaps.org/schemas/sitemap/0.9\">\n";

        foreach ($sitemaps as $sitemap) {
            $xml .= "  <sitemap>\n";
            $xml .= "    <loc>" . htmlspecialchars($sitemap['loc']) . "</loc>\n";
            $xml .= "    <lastmod>{$sitemap['lastmod']}</lastmod>\n";
            $xml .= "  </sitemap>\n";
        }

        $xml .= "</sitemapindex>";

        return $xml;
    }


    private function generateUrlsetXml($urls)
    {
        $xml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
        $xml .= "<urlset xmlns=\"https://www.sitemaps.org/schemas/sitemap/0.9\">\n";

        foreach ($urls as $url) {
            $xml .= "  <url>\n";
            $xml .= "    <loc>" . htmlspecialchars($url['loc']) . "</loc>\n";
            $xml .= "    <lastmod>{$url['lastmod']}</lastmod>\n";
            $xml .= "    <changefreq>{$url['changefreq']}</changefreq>\n";
            $xml .= "    <priority>{$url['priority']}</priority>\n";
            $xml .= "  </url>\n";
        }

        $xml .= "</urlset>";

        return $xml;
    }


    private function uploadToFtp()
    {
        try {

            if (!env('FTP_SITEMAP_HOST')) {
                Log::info('FTP تنظیم نشده است - آپلود انجام نمی‌شود');
                return;
            }

            $ftpService = new FtpSitemapService();
            $uploadedFiles = $ftpService->uploadSitemapFiles();

            Log::info('آپلود FTP تکمیل شد. فایل‌های آپلود شده: ' . implode(', ', $uploadedFiles));

        } catch (Exception $e) {
            Log::error('خطا در آپلود FTP: ' . $e->getMessage());

        }
    }


    private function ensureSitemapsDirectory()
    {
        $directory = storage_path(self::SITEMAP_BASE_PATH);
        if (!file_exists($directory)) {
            mkdir($directory, 0755, true);
        }
    }
}
